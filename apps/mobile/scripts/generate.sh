#!/bin/bash

# Script to generate code for the mobile app
# This ensures all generated files are up to date

set -e

echo "🔄 Generating code for mobile app..."

# Navigate to mobile directory
cd "$(dirname "$0")/.."

# Install dependencies if needed
echo "📦 Installing dependencies..."
flutter pub get

# Generate code
echo "🏗️  Running code generation..."
dart run build_runner build --delete-conflicting-outputs

echo "✅ Code generation completed successfully!"

# Optional: Run analysis to verify everything is working
if [ "$1" = "--analyze" ]; then
    echo "🔍 Running Flutter analyze..."
    flutter analyze
    echo "✅ Analysis completed successfully!"
fi

echo "🎉 All done! Generated files are up to date."
