#!/bin/bash

# Local network build script
# Uses local network IP for testing on physical devices

set -e

echo "🚀 Building mobile app for LOCAL NETWORK environment..."

# Navigate to mobile directory
cd "$(dirname "$0")/.."

# Environment variables
API_URL="http://************:3000/graphql"
ENVIRONMENT="development"

echo "📍 Environment: $ENVIRONMENT (Local Network)"
echo "🌐 API URL: $API_URL"

# Build for Android (debug)
echo "🤖 Building Android debug APK..."
flutter build apk --debug \
  --dart-define=API_URL="$API_URL" \
  --dart-define=ENVIRONMENT="$ENVIRONMENT"

echo "✅ Local network build completed successfully!"
echo "📱 APK location: build/app/outputs/flutter-apk/app-debug.apk"
echo "💡 This build can connect to your local API server from other devices on the same network" 