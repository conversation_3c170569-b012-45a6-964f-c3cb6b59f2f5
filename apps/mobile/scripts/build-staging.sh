#!/bin/bash

# Staging build script
# Uses staging API URL for testing

set -e

echo "🚀 Building mobile app for STAGING environment..."

# Navigate to mobile directory
cd "$(dirname "$0")/.."

# Environment variables
API_URL="https://staging-api.budapp.com/graphql"  # TODO: Replace with actual staging URL
ENVIRONMENT="staging"

echo "📍 Environment: $ENVIRONMENT"
echo "🌐 API URL: $API_URL"

# Build for Android (release)
echo "🤖 Building Android release APK..."
flutter build apk --release \
  --dart-define=API_URL="$API_URL" \
  --dart-define=ENVIRONMENT="$ENVIRONMENT"

echo "✅ Staging build completed successfully!"
echo "📱 APK location: build/app/outputs/flutter-apk/app-release.apk" 