#!/bin/bash

# Production build script
# Uses production API URL for release builds

set -e

echo "🚀 Building mobile app for PRODUCTION environment..."

# Navigate to mobile directory
cd "$(dirname "$0")/.."

# Environment variables
API_URL="https://api.budapp.com/graphql"  # TODO: Replace with actual production URL
ENVIRONMENT="production"

echo "📍 Environment: $ENVIRONMENT"
echo "🌐 API URL: $API_URL"

# Build for Android (release)
echo "🤖 Building Android release APK..."
flutter build apk --release \
  --dart-define=API_URL="$API_URL" \
  --dart-define=ENVIRONMENT="$ENVIRONMENT"

echo "✅ Production build completed successfully!"
echo "📱 APK location: build/app/outputs/flutter-apk/app-release.apk"
echo "⚠️  This is a PRODUCTION build - ensure all testing is complete!" 