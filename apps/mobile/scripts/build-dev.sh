#!/bin/bash

# Development build script
# Uses localhost API URL for local development

set -e

echo "🚀 Building mobile app for DEVELOPMENT environment..."

# Navigate to mobile directory
cd "$(dirname "$0")/.."

# Environment variables
API_URL="https://budapp-yzlw.onrender.com/graphql"
ENVIRONMENT="development"

echo "📍 Environment: $ENVIRONMENT"
echo "🌐 API URL: $API_URL"

# Build for Android (debug)
echo "🤖 Building Android debug APK..."
flutter build apk --debug \
  --dart-define=API_URL="$API_URL" \
  --dart-define=ENVIRONMENT="$ENVIRONMENT"

echo "✅ Development build completed successfully!"
echo "📱 APK location: build/app/outputs/flutter-apk/app-debug.apk" 