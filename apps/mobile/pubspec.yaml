name: budapp
description: A personal finance management application
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2
  flutter: ">=3.29.3"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  google_fonts: ^6.1.0

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.5

  # Navigation
  go_router: ^15.1.2

  # GraphQL
  graphql_flutter: ^5.1.2

  # Storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.2.4

  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.2
  equatable: ^2.0.5
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

  # Firebase
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5

  # Analytics & Monitoring
  sentry_flutter: ^8.14.2
  flutter_appauth: ^9.0.0

# Dev Dependencies
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

  # Code Generation
  build_runner: ^2.4.11
  riverpod_generator: ^2.4.1
  freezed: ^2.4.1
  json_serializable: ^6.8.0
  analyzer: ^6.11.0
  analyzer_plugin: ^0.11.3

  # Testing
  mocktail: ^1.0.2

  # Added flutter_launcher_icons
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  generate: true

  assets:
    - assets/icon/

#  assets:
#    - assets/images/
#    - assets/icons/

#  fonts:
#    - family: Inter


  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Added flutter_launcher_icons
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/budapp_icon.png" # Placeholder - replace with your actual icon path
  min_sdk_android: 21 # Optional
  remove_alpha_ios: true # Optional
  # web:
  #   generate: true
  #   image_path: "path/to/image.png"
  #   background_color: "#hexcode"
  #   theme_color: "#hexcode"
  # windows:
  #   generate: true
  #   image_path: "path/to/image.png"
  #   icon_size: 48 # Optional
  # macos:
  #   generate: true
  #   image_path: "path/to/image.png"
