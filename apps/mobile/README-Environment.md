# Mobile App Environment Configuration

This document explains how to configure the mobile app for different environments using build-time variables.

## Overview

The mobile app now supports configurable API URLs and environment settings using <PERSON><PERSON><PERSON>'s `--dart-define` feature. This eliminates hardcoded URLs and makes the app suitable for different deployment scenarios.

## Environment Configuration

### Available Environments

| Environment | API URL | Build Type | Use Case |
|-------------|---------|------------|----------|
| Development | `http://localhost:3000/graphql` | Debug | Local development with API server on same machine |
| Local Network | `http://************:3000/graphql` | Debug | Testing on physical devices connected to same network |
| Staging | `https://staging-api.budapp.com/graphql` | Release | Testing with staging backend |
| Production | `https://api.budapp.com/graphql` | Release | Production deployment |

### Build Scripts

Pre-configured build scripts are available in the `scripts/` directory:

```bash
# Development build (localhost)
./scripts/build-dev.sh

# Local network build (for physical device testing)
./scripts/build-local.sh

# Staging build
./scripts/build-staging.sh

# Production build
./scripts/build-production.sh
```

### Manual Build Commands

You can also build manually with custom environment variables:

```bash
# Development build
flutter build apk --debug \
  --dart-define=API_URL="http://localhost:3000/graphql" \
  --dart-define=ENVIRONMENT="development"

# Custom API URL
flutter build apk --debug \
  --dart-define=API_URL="http://your-custom-url:3000/graphql" \
  --dart-define=ENVIRONMENT="development"
```

### Running in Development

For development with hot reload:

```bash
# Default (localhost)
flutter run

# With custom API URL
flutter run \
  --dart-define=API_URL="http://************:3000/graphql" \
  --dart-define=ENVIRONMENT="development"
```

## Environment Variables

### API_URL
- **Type**: String
- **Default**: `http://localhost:3000/graphql`
- **Description**: The GraphQL API endpoint URL

### ENVIRONMENT
- **Type**: String
- **Default**: `development`
- **Options**: `development`, `staging`, `production`
- **Description**: Environment identifier for logging and debugging

## Implementation Details

### Environment Class

The `lib/config/environment.dart` file provides:

```dart
class Environment {
  static const String apiUrl = String.fromEnvironment('API_URL', 
    defaultValue: 'http://localhost:3000/graphql');
  
  static const String environment = String.fromEnvironment('ENVIRONMENT', 
    defaultValue: 'development');
  
  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';
}
```

### GraphQL Provider Integration

The GraphQL provider automatically uses the configured API URL:

```dart
final HttpLink httpLink = HttpLink(Environment.apiUrl);
```

In debug mode, the app will print the current environment configuration to the console.

## Team Usage

### For Developers

1. **Local Development**: Use `flutter run` or `./scripts/build-dev.sh`
2. **Device Testing**: Use `./scripts/build-local.sh` to test on physical devices
3. **Custom URLs**: Use manual build commands with your specific API URL

### For CI/CD

Use the build scripts or manual commands in your CI/CD pipeline:

```yaml
# Example GitHub Actions step
- name: Build Android APK
  run: |
    cd apps/mobile
    flutter build apk --release \
      --dart-define=API_URL="${{ secrets.API_URL }}" \
      --dart-define=ENVIRONMENT="production"
```

### For QA Testing

1. **Staging Builds**: Use `./scripts/build-staging.sh`
2. **Production Builds**: Use `./scripts/build-production.sh`

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure the API server is running and accessible at the configured URL
2. **Network Errors**: Check firewall settings and network connectivity
3. **Wrong Environment**: Verify the correct build script is being used

### Debug Information

In debug mode, the app prints environment information to the console:

```
Environment: development
API URL: http://localhost:3000/graphql
```

### Updating URLs

To update staging or production URLs:

1. Edit the respective build script in `scripts/`
2. Update the `API_URL` variable
3. Commit the changes to version control

## Security Notes

- Environment variables are compiled into the app at build time
- No sensitive information should be stored in environment variables
- Production URLs should be configured through secure CI/CD variables
- API authentication is handled separately through secure storage 