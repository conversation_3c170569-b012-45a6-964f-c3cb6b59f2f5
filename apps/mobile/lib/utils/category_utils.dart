import 'package:flutter/material.dart';
import 'package:budapp/models/category.dart';

class CategoryUtils {
  // Map of icon names to IconData
  static const Map<String, IconData> iconMap = {
    'home': Icons.home,
    'restaurant': Icons.restaurant,
    'car': Icons.directions_car,
    'shopping-cart': Icons.shopping_cart,
    'gas': Icons.local_gas_station,
    'medical': Icons.medical_services,
    'education': Icons.school,
    'entertainment': Icons.movie,
    'fitness': Icons.fitness_center,
    'personal-care': Icons.spa,
    'travel': Icons.flight,
    'gifts': Icons.card_giftcard,
    'pets': Icons.pets,
    'phone': Icons.phone,
    'utilities': Icons.electrical_services,
    'insurance': Icons.security,
    'savings': Icons.savings,
    'investments': Icons.trending_up,
    'salary': Icons.work,
    'business': Icons.business,
    'bank': Icons.account_balance,
    'money': Icons.monetization_on,
    'other': Icons.more_horiz,
    'briefcase': Icons.work_outline,
    'chart-line': Icons.trending_up,
    'gift': Icons.card_giftcard,
    'key': Icons.vpn_key,
    'bolt': Icons.bolt,
    'utensils': Icons.restaurant,
    'shopping-bag': Icons.shopping_bag,
    'money-bill': Icons.attach_money,
  };

  /// Get IconData from icon name string
  static IconData getIconFromName(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return Icons.category; // Default icon
    }
    return iconMap[iconName] ?? Icons.category;
  }

  /// Get icon name from IconData
  static String? getIconName(IconData icon) {
    for (final entry in iconMap.entries) {
      if (entry.value == icon) {
        return entry.key;
      }
    }
    return null;
  }

  /// Convert color hex string to Color object
  static Color getColorFromHex(String? colorHex) {
    if (colorHex == null || colorHex.isEmpty) {
      return Colors.grey; // Default color
    }
    
    try {
      // Remove # if present
      String hex = colorHex.replaceFirst('#', '');
      
      // Add alpha if not present
      if (hex.length == 6) {
        hex = 'FF$hex';
      }
      
      return Color(int.parse(hex, radix: 16));
    } catch (e) {
      return Colors.grey; // Fallback color
    }
  }

  /// Convert Color object to hex string
  static String getHexFromColor(Color color) {
    return '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
  }

  /// Get default color for category type
  static Color getDefaultColorForType(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return const Color(0xFF4CAF50); // Green
      case CategoryType.expense:
        return const Color(0xFFF44336); // Red
    }
  }

  /// Get default icon for category type
  static IconData getDefaultIconForType(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return Icons.add_circle_outline;
      case CategoryType.expense:
        return Icons.remove_circle_outline;
    }
  }
}


// Predefined category colors
class CategoryColors {
  static const List<int> predefinedColors = [
    0xFFEF4444, // Red
    0xFFF97316, // Orange
    0xFFF59E0B, // Amber
    0xFFEAB308, // Yellow
    0xFF84CC16, // Lime
    0xFF22C55E, // Green
    0xFF10B981, // Emerald
    0xFF14B8A6, // Teal
    0xFF06B6D4, // Cyan
    0xFF0EA5E9, // Sky
    0xFF3B82F6, // Blue
    0xFF6366F1, // Indigo
    0xFF8B5CF6, // Violet
    0xFFA855F7, // Purple
    0xFFD946EF, // Fuchsia
    0xFFEC4899, // Pink
    0xFFF43F5E, // Rose
  ];

  static int getColorForCategory(String categoryName) {
    final hash = categoryName.hashCode;
    return predefinedColors[hash.abs() % predefinedColors.length];
  }
}

// Predefined category icons
class CategoryIcons {
  static const List<String> incomeIcons = [
    '💰', '💵', '💸', '🏦', '📈', '💼', '🎯', '🎁', '💎', '🏆'
  ];

  static const List<String> expenseIcons = [
    '🛒', '🍔', '⛽', '🏠', '🚗', '👕', '🎬', '📱', '💊', '✈️',
    '🎓', '🏥', '🔧', '🎮', '📚', '🍕', '☕', '🚌', '💡', '🧾'
  ];

  static String getIconForCategory(String categoryName, CategoryType type) {
    final icons = type == CategoryType.income ? incomeIcons : expenseIcons;
    final hash = categoryName.hashCode;
    return icons[hash.abs() % icons.length];
  }
} 