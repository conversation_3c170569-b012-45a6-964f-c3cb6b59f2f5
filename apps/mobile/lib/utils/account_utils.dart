import 'package:budapp/models/account.dart';

extension AccountTypeExtension on AccountType {
  String get icon {
    switch (this) {
      case AccountType.checking:
        return '🏦';
      case AccountType.savings:
        return '💰';
      case AccountType.creditCard:
        return '💳';
      case AccountType.cash:
        return '💵';
      case AccountType.investment:
        return '📈';
      case AccountType.loan:
        return '🏠';
      case AccountType.asset:
        return '🏛️';
      case AccountType.liability:
        return '📋';
    }
  }

  String get displayName {
    switch (this) {
      case AccountType.checking:
        return 'Checking';
      case AccountType.savings:
        return 'Savings';
      case AccountType.creditCard:
        return 'Credit Card';
      case AccountType.cash:
        return 'Cash';
      case AccountType.investment:
        return 'Investment';
      case AccountType.loan:
        return 'Loan';
      case AccountType.asset:
        return 'Asset';
      case AccountType.liability:
        return 'Liability';
    }
  }
}

extension AccountExtension on Account {
  /// Get the current balance (alias for currentBalance)
  double get balance => currentBalance;
  
  /// Get the description (alias for notes)
  String? get description => notes;
} 