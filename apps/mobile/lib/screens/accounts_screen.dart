import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/providers/accounts_provider.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/utils/account_utils.dart';

class AccountsScreen extends ConsumerWidget {
  const AccountsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final accountsAsync = ref.watch(accountsListProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.push('/accounts/add');
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.refresh(accountsListProvider.future),
        child: accountsAsync.when(
          data: (accounts) => _buildAccountsList(context, accounts),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(context, ref, error),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/add-transaction');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAccountsList(BuildContext context, List<Account> accounts) {
    if (accounts.isEmpty) {
      return _buildEmptyState(context);
    }

    // Group accounts by type
    final groupedAccounts = <AccountType, List<Account>>{};
    for (final account in accounts) {
      groupedAccounts.putIfAbsent(account.type, () => []).add(account);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: groupedAccounts.length,
      itemBuilder: (context, index) {
        final entry = groupedAccounts.entries.elementAt(index);
        final accountType = entry.key;
        final typeAccounts = entry.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (index > 0) const SizedBox(height: AppSpacing.lg),
            _buildAccountTypeHeader(context, accountType, typeAccounts),
            const SizedBox(height: AppSpacing.sm),
            ...typeAccounts.map((account) => Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: _buildAccountCard(context, account),
            )),
          ],
        );
      },
    );
  }

  Widget _buildAccountTypeHeader(BuildContext context, AccountType type, List<Account> accounts) {
    final totalBalance = accounts.fold<double>(0.0, (sum, account) => sum + account.balance);
    final isNegative = totalBalance < 0;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: _getAccountTypeColor(type).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                _getAccountTypeIcon(type),
                color: _getAccountTypeColor(type),
                size: 24,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                type.displayName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: AppTypography.fontWeightSemiBold,
                  color: _getAccountTypeColor(type),
                ),
              ),
            ],
          ),
          Text(
            '${isNegative ? '-' : ''}\$${totalBalance.abs().toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: AppTypography.fontWeightBold,
              color: isNegative ? AppColors.error : AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountCard(BuildContext context, Account account) {
    final isNegative = account.balance < 0;
    final color = _getAccountTypeColor(account.type);

    return Card(
      child: InkWell(
        onTap: () => context.push('/account/${account.id}/transactions?name=${Uri.encodeComponent(account.name)}'),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: Icon(
                  _getAccountTypeIcon(account.type),
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: AppTypography.fontWeightSemiBold,
                      ),
                    ),
                    if (account.description?.isNotEmpty == true) ...[
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        account.description!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${isNegative ? '-' : ''}\$${account.balance.abs().toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: AppTypography.fontWeightBold,
                      color: isNegative ? AppColors.error : AppColors.success,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'View transactions',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppColors.primary,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.money;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.account_balance_wallet;
      case AccountType.asset:
        return Icons.home;
      case AccountType.liability:
        return Icons.warning;
    }
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Colors.blue;
      case AccountType.savings:
        return Colors.green;
      case AccountType.creditCard:
        return Colors.red;
      case AccountType.cash:
        return Colors.orange;
      case AccountType.investment:
        return Colors.purple;
      case AccountType.loan:
        return Colors.brown;
      case AccountType.asset:
        return Colors.teal;
      case AccountType.liability:
        return Colors.red;
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.account_balance_wallet_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            'No accounts yet',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first account to get started',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.push('/accounts/add');
            },
            child: const Text('Add Account'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Failed to load accounts',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Retry loading accounts
              ref.invalidate(accountsListProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
} 