import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:budapp/providers/category_provider.dart';
import 'package:budapp/models/category.dart';
import 'package:budapp/utils/category_utils.dart';
import 'package:budapp/config/design_tokens.dart';

class CategoryNode {
  final Category category;
  final List<CategoryNode> children;

  CategoryNode({
    required this.category,
    required this.children,
  });
}

class CategoriesScreen extends ConsumerWidget {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Categories'),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                context.go('/categories/add');
              },
            ),
          ],
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Expenses'),
              Tab(text: 'Income'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            _ExpenseCategoriesTab(),
            _IncomeCategoriesTab(),
          ],
        ),
      ),
    );
  }
}

class _ExpenseCategoriesTab extends ConsumerWidget {
  const _ExpenseCategoriesTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expenseCategoriesAsync = ref.watch(expenseCategoriesProvider);
    
    return expenseCategoriesAsync.when(
      data: (categories) {
        if (categories.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.category_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No expense categories yet',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Tap the + button to create your first category',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }
        
        // Build hierarchical structure
        final hierarchicalCategories = _buildCategoryHierarchy(categories);
        
        return RefreshIndicator(
          onRefresh: () => ref.refresh(expenseCategoriesProvider.future),
          child: ListView(
            padding: const EdgeInsets.all(AppSpacing.md),
            children: [
              ...hierarchicalCategories.map((categoryNode) => 
                _buildCategoryTree(context, categoryNode, 0)),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading categories: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(expenseCategoriesProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  List<CategoryNode> _buildCategoryHierarchy(List<Category> categories) {
    final Map<String, CategoryNode> nodeMap = {};
    final List<CategoryNode> rootNodes = [];

    // Create nodes for all categories
    for (final category in categories) {
      nodeMap[category.id] = CategoryNode(category: category, children: []);
    }

    // Build hierarchy
    for (final category in categories) {
      final node = nodeMap[category.id]!;
      
      if (category.parentId != null && nodeMap.containsKey(category.parentId)) {
        // Add to parent's children
        nodeMap[category.parentId]!.children.add(node);
      } else {
        // Root level category
        rootNodes.add(node);
      }
    }

    return rootNodes;
  }

  Widget _buildCategoryTree(BuildContext context, CategoryNode node, int depth) {
    return Column(
      children: [
        _buildCategoryCard(context, node.category, depth),
        if (node.children.isNotEmpty)
          ...node.children.map((child) => 
            _buildCategoryTree(context, child, depth + 1)),
      ],
    );
  }

  Widget _buildCategoryCard(BuildContext context, Category category, int depth) {
    final categoryColor = CategoryUtils.getColorFromHex(category.color);
    final categoryIcon = CategoryUtils.getIconFromName(category.icon);
    final leftPadding = depth * 24.0;

    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.sm,
        left: leftPadding,
      ),
      child: Card(
        elevation: depth > 0 ? 1 : 2,
        child: InkWell(
          onTap: () => context.push('/category/${category.id}/transactions?name=${Uri.encodeComponent(category.name)}'),
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                if (depth > 0) ...[
                  const SizedBox(width: AppSpacing.sm),
                  Icon(
                    Icons.subdirectory_arrow_right,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: AppSpacing.sm),
                ],
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: categoryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                  ),
                  child: Icon(
                    categoryIcon,
                    color: categoryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: AppTypography.fontWeightSemiBold,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Row(
                        children: [
                          Text(
                            'Created ${_formatDate(category.createdAt)}',
                            style: const TextStyle(
                              fontSize: AppTypography.fontSizeSm,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          if (category.isSystem) ...[
                            const SizedBox(width: AppSpacing.sm),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.xs,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.success.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                              ),
                              child: const Text(
                                'System',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppColors.success,
                                  fontWeight: AppTypography.fontWeightBold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'View transactions',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                      color: AppColors.primary,
                    ),
                  ],
                ),
                if (!category.isSystem) ...[
                  const SizedBox(width: AppSpacing.sm),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        // TODO: Navigate to edit category screen
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Edit ${category.name} coming soon')),
                        );
                      } else if (value == 'delete') {
                        _showDeleteConfirmation(context, category);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    }
  }

  void _showDeleteConfirmation(BuildContext context, Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                // Note: This would need to be implemented with proper ref access
                // For now, just show a message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Delete ${category.name} functionality coming soon')),
                );
              } catch (error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error deleting category: $error')),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _IncomeCategoriesTab extends ConsumerWidget {
  const _IncomeCategoriesTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final incomeCategoriesAsync = ref.watch(incomeCategoriesProvider);
    
    return incomeCategoriesAsync.when(
      data: (categories) {
        if (categories.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.category_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No income categories yet',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Tap the + button to create your first category',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }
        
        // Build hierarchical structure
        final hierarchicalCategories = _buildCategoryHierarchy(categories);
        
        return RefreshIndicator(
          onRefresh: () => ref.refresh(incomeCategoriesProvider.future),
          child: ListView(
            padding: const EdgeInsets.all(AppSpacing.md),
            children: [
              ...hierarchicalCategories.map((categoryNode) => 
                _buildCategoryTree(context, categoryNode, 0)),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading categories: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(incomeCategoriesProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  List<CategoryNode> _buildCategoryHierarchy(List<Category> categories) {
    final Map<String, CategoryNode> nodeMap = {};
    final List<CategoryNode> rootNodes = [];

    // Create nodes for all categories
    for (final category in categories) {
      nodeMap[category.id] = CategoryNode(category: category, children: []);
    }

    // Build hierarchy
    for (final category in categories) {
      final node = nodeMap[category.id]!;
      
      if (category.parentId != null && nodeMap.containsKey(category.parentId)) {
        // Add to parent's children
        nodeMap[category.parentId]!.children.add(node);
      } else {
        // Root level category
        rootNodes.add(node);
      }
    }

    return rootNodes;
  }

  Widget _buildCategoryTree(BuildContext context, CategoryNode node, int depth) {
    return Column(
      children: [
        _buildIncomeCard(context, node.category, depth),
        if (node.children.isNotEmpty)
          ...node.children.map((child) => 
            _buildCategoryTree(context, child, depth + 1)),
      ],
    );
  }

  Widget _buildIncomeCard(BuildContext context, Category category, int depth) {
    final categoryColor = CategoryUtils.getColorFromHex(category.color);
    final categoryIcon = CategoryUtils.getIconFromName(category.icon);
    final leftPadding = depth * 24.0;

    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.sm,
        left: leftPadding,
      ),
      child: Card(
        elevation: depth > 0 ? 1 : 2,
        child: InkWell(
          onTap: () => context.push('/category/${category.id}/transactions?name=${Uri.encodeComponent(category.name)}'),
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                if (depth > 0) ...[
                  const SizedBox(width: AppSpacing.sm),
                  Icon(
                    Icons.subdirectory_arrow_right,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: AppSpacing.sm),
                ],
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: categoryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                  ),
                  child: Icon(
                    categoryIcon,
                    color: categoryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: AppTypography.fontWeightSemiBold,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Row(
                        children: [
                          Text(
                            'Created ${_formatDate(category.createdAt)}',
                            style: const TextStyle(
                              fontSize: AppTypography.fontSizeSm,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          if (category.isSystem) ...[
                            const SizedBox(width: AppSpacing.sm),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.xs,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.success.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                              ),
                              child: const Text(
                                'System',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppColors.success,
                                  fontWeight: AppTypography.fontWeightBold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'View transactions',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                      color: AppColors.primary,
                    ),
                  ],
                ),
                if (!category.isSystem) ...[
                  const SizedBox(width: AppSpacing.sm),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        // TODO: Navigate to edit category screen
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Edit ${category.name} coming soon')),
                        );
                      } else if (value == 'delete') {
                        _showDeleteConfirmation(context, category);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    }
  }

  void _showDeleteConfirmation(BuildContext context, Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                // Note: This would need to be implemented with proper ref access
                // For now, just show a message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Delete ${category.name} functionality coming soon')),
                );
              } catch (error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error deleting category: $error')),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

 