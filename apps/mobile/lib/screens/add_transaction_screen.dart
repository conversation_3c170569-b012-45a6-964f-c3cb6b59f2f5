import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:budapp/models/transaction.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/models/category.dart';
import 'package:budapp/providers/transactions_provider.dart';
import 'package:budapp/providers/accounts_provider.dart';
import 'package:budapp/providers/category_provider.dart';
import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/utils/account_utils.dart';
import 'package:budapp/utils/category_utils.dart';

class AddTransactionScreen extends ConsumerStatefulWidget {
  const AddTransactionScreen({super.key});

  @override
  ConsumerState<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends ConsumerState<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  
  TransactionType _selectedType = TransactionType.expense;
  DateTime _selectedDate = DateTime.now();
  Account? _selectedAccount;
  Category? _selectedCategory;
  Account? _selectedToAccount; // For transfers
  bool _isLoading = false;

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accountsAsync = ref.watch(accountsListProvider);
    final categoriesAsync = ref.watch(categoryNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Transaction'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppSpacing.md),
          children: [
            // Transaction Type Selection
            _buildTransactionTypeSelector(),
            const SizedBox(height: AppSpacing.lg),

            // Description Field
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter transaction description',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Description is required';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.md),

            // Amount Field
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                hintText: '0.00',
                prefixText: '\$ ',
                border: OutlineInputBorder(),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Amount is required';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Please enter a valid amount';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.md),

            // Date Field
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Date',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  DateFormat('MMM d, y').format(_selectedDate),
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Account Selection
            accountsAsync.when(
              data: (accounts) => _buildAccountDropdown(accounts),
              loading: () => const CircularProgressIndicator(),
              error: (error, stack) => Text('Error loading accounts: $error'),
            ),
            const SizedBox(height: AppSpacing.md),

            // Transfer To Account (only for transfers)
            if (_selectedType == TransactionType.transfer) ...[
              accountsAsync.when(
                data: (accounts) => _buildToAccountDropdown(accounts),
                loading: () => const CircularProgressIndicator(),
                error: (error, stack) => Text('Error loading accounts: $error'),
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // Category Selection (not for transfers)
            if (_selectedType != TransactionType.transfer) ...[
              categoriesAsync.when(
                data: (categories) => _buildCategoryDropdown(categories),
                loading: () => const CircularProgressIndicator(),
                error: (error, stack) => Text('Error loading categories: $error'),
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // Notes Field
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Add any additional notes',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Save Button
            ElevatedButton(
              onPressed: _isLoading ? null : _saveTransaction,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Save Transaction'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Transaction Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Row(
          children: TransactionType.values.map((type) {
            final isSelected = _selectedType == type;
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                child: ChoiceChip(
                  label: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        type.icon,
                        style: const TextStyle(fontSize: 20),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        type.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedType = type;
                        // Reset account selections when type changes
                        _selectedAccount = null;
                        _selectedToAccount = null;
                        _selectedCategory = null;
                      });
                    }
                  },
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAccountDropdown(List<Account> accounts) {
    return DropdownButtonFormField<Account>(
      value: _selectedAccount,
      decoration: InputDecoration(
        labelText: _selectedType == TransactionType.transfer ? 'From Account' : 'Account',
        border: const OutlineInputBorder(),
      ),
      items: accounts.map((account) {
        return DropdownMenuItem(
          value: account,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                account.type.icon,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(width: AppSpacing.sm),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      account.name,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      NumberFormat.currency(symbol: '\$').format(account.balance),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (account) {
        setState(() {
          _selectedAccount = account;
        });
      },
      validator: (value) {
        if (value == null) {
          return 'Please select an account';
        }
        return null;
      },
    );
  }

  Widget _buildToAccountDropdown(List<Account> accounts) {
    // Filter out the selected "from" account
    final availableAccounts = accounts
        .where((account) => account.id != _selectedAccount?.id)
        .toList();

    return DropdownButtonFormField<Account>(
      value: _selectedToAccount,
      decoration: const InputDecoration(
        labelText: 'To Account',
        border: OutlineInputBorder(),
      ),
      items: availableAccounts.map((account) {
        return DropdownMenuItem(
          value: account,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                account.type.icon,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(width: AppSpacing.sm),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      account.name,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      NumberFormat.currency(symbol: '\$').format(account.balance),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (account) {
        setState(() {
          _selectedToAccount = account;
        });
      },
      validator: (value) {
        if (_selectedType == TransactionType.transfer && value == null) {
          return 'Please select a destination account';
        }
        return null;
      },
    );
  }

  Widget _buildCategoryDropdown(List<Category> categories) {
    // Filter categories by transaction type
    final filteredCategories = categories.where((category) {
      switch (_selectedType) {
        case TransactionType.income:
          return category.categoryType == CategoryType.income;
        case TransactionType.expense:
          return category.categoryType == CategoryType.expense;
        case TransactionType.transfer:
          return false; // No categories for transfers
      }
    }).toList();

    return DropdownButtonFormField<Category>(
      value: _selectedCategory,
      decoration: const InputDecoration(
        labelText: 'Category',
        border: OutlineInputBorder(),
      ),
      items: filteredCategories.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Color(category.colorValue),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    category.icon ?? '📁',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Flexible(
                child: Text(
                  category.name,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (category) {
        setState(() {
          _selectedCategory = category;
        });
      },
      validator: (value) {
        if (_selectedType != TransactionType.transfer && value == null) {
          return 'Please select a category';
        }
        return null;
      },
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      CreateTransactionInput input;

      if (_selectedType == TransactionType.transfer) {
        // Create transfer with manual journal lines
        input = CreateTransactionInput(
          description: _descriptionController.text.trim(),
          amount: amount,
          date: _selectedDate,
          notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
          journalLines: [
            JournalLineInput(
              accountId: _selectedAccount!.id,
              amount: amount,
              type: TransactionEntryType.credit, // Money leaving the account
            ),
            JournalLineInput(
              accountId: _selectedToAccount!.id,
              amount: amount,
              type: TransactionEntryType.debit, // Money entering the account
            ),
          ],
        );
      } else {
        // Create simple income/expense transaction
        input = CreateTransactionInput(
          description: _descriptionController.text.trim(),
          amount: amount,
          date: _selectedDate,
          notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
          accountId: _selectedAccount!.id,
          categoryId: _selectedCategory!.id,
          type: _selectedType,
        );
      }

      await ref.read(transactionsProvider().notifier).addTransaction(input);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving transaction: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 