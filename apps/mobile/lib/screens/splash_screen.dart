import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:budapp/providers/auth_provider.dart';
import 'package:budapp/config/design_tokens.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Delay for a short time to show the splash screen
    Timer(const Duration(seconds: 1), () {
      _checkAuthAndNavigate();
    });
  }

  void _checkAuthAndNavigate() {
    if (!mounted) return;
    final authState = ref.read(authProvider);

    if (authState.status == AuthStatus.authenticated) {
      if (!mounted) return;
      context.go('/home');
    } else if (authState.status == AuthStatus.unauthenticated) {
      if (!mounted) return;
      context.go('/login');
    } else if (authState.status == AuthStatus.error) {
      if (!mounted) return;
      context.go('/login');
    } else if (authState.status == AuthStatus.initial) {
      // If still initializing, wait a bit more and check again
      Timer(const Duration(seconds: 2), () {
        if (!mounted) return;
        _checkAuthAndNavigate();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashBackground,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.splashBackground,
              AppColors.primaryDark,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Icon with elevated container
              Container(
                padding: const EdgeInsets.all(AppSpacing.xl),
                decoration: BoxDecoration(
                  color: AppColors.onPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.xxl),
                  border: Border.all(
                    color: AppColors.onPrimary.withValues(alpha: 0.2),
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                  child: Image.asset(
                    'assets/icon/budapp_icon.png',
                    width: 80,
                    height: 80,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback to the wallet icon if the image fails to load
                      return const Icon(
                        Icons.account_balance_wallet_rounded,
                        size: 80,
                        color: AppColors.onPrimary,
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: AppSpacing.xl),
              
              // App Name
              Text(
                'BudApp',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  color: AppColors.onPrimary,
                  fontWeight: AppTypography.fontWeightBold,
                  fontSize: AppTypography.fontSizeXxxl,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              
              // App Tagline
              Text(
                'Personal Finance Management',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.onPrimary.withValues(alpha: 0.8),
                  fontWeight: AppTypography.fontWeightMedium,
                ),
              ),
              const SizedBox(height: AppSpacing.xxl),
              
              // Loading Indicator
              const SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.onPrimary),
                  strokeWidth: 3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
