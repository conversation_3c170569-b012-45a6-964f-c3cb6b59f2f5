import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/providers/accounts_provider.dart';
import 'package:budapp/providers/transactions_provider.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/models/transaction.dart';
import 'package:budapp/utils/account_utils.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final accountsAsync = ref.watch(accountsListProvider);
    final recentTransactionsAsync = ref.watch(filteredTransactionsProvider(
      const TransactionFilter(
        // Get recent transactions (last 30 days)
        startDate: null, // Will be handled by provider to get recent
      ),
    ));

    return Scaffold(
      appBar: AppBar(
        title: const Text('BudApp'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {},
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.wait([
            ref.refresh(accountsListProvider.future),
            ref.read(transactionsProvider().notifier).refresh(),
          ]);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(context),
              const SizedBox(height: AppSpacing.lg),
              _buildAccountsCard(context, accountsAsync),
              const SizedBox(height: AppSpacing.lg),
              _buildRecentTransactionsCard(context, recentTransactionsAsync),
              const SizedBox(height: AppSpacing.lg),
              _buildBudgetCard(context),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/add-transaction');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome back!',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: AppTypography.fontWeightBold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Here\'s your financial overview',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAccountsCard(BuildContext context, AsyncValue<List<Account>> accountsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Accounts',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.go('/accounts'),
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            accountsAsync.when(
              data: (accounts) {
                if (accounts.isEmpty) {
                  return _buildEmptyAccountsState(context);
                }
                
                // Calculate total balance
                final totalBalance = accounts.fold<double>(
                  0.0,
                  (sum, account) => sum + account.balance,
                );
                
                return Column(
                  children: [
                    _buildTotalBalanceCard(context, totalBalance),
                    const SizedBox(height: AppSpacing.md),
                    ...accounts.take(3).map((account) => Padding(
                      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                      child: _buildAccountItem(context, account),
                    )),
                    if (accounts.length > 3)
                      Padding(
                        padding: const EdgeInsets.only(top: AppSpacing.sm),
                        child: TextButton(
                          onPressed: () => context.go('/accounts'),
                          child: Text('View ${accounts.length - 3} more accounts'),
                        ),
                      ),
                  ],
                );
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppSpacing.lg),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stack) => _buildErrorState(context, 'Failed to load accounts'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalBalanceCard(BuildContext context, double totalBalance) {
    final isNegative = totalBalance < 0;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total Balance',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            '${isNegative ? '-' : ''}\$${totalBalance.abs().toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: AppTypography.fontWeightBold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountItem(BuildContext context, Account account) {
    final isNegative = account.balance < 0;
    final color = _getAccountTypeColor(account.type);
    
    return InkWell(
      onTap: () => context.push('/account/${account.id}/transactions?name=${Uri.encodeComponent(account.name)}'),
      borderRadius: BorderRadius.circular(AppBorderRadius.md),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
              ),
              child: Icon(
                _getAccountTypeIcon(account.type),
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    account.name,
                    style: const TextStyle(
                      fontWeight: AppTypography.fontWeightMedium,
                    ),
                  ),
                  Text(
                    account.type.displayName,
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: AppTypography.fontSizeSm,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isNegative ? '-' : ''}\$${account.balance.abs().toStringAsFixed(2)}',
                  style: TextStyle(
                    color: isNegative ? AppColors.error : AppColors.success,
                    fontWeight: AppTypography.fontWeightSemiBold,
                    fontSize: AppTypography.fontSizeMd,
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyAccountsState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No accounts yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Add your first account to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return AppColors.primary;
      case AccountType.savings:
        return AppColors.success;
      case AccountType.creditCard:
        return AppColors.warning;
      case AccountType.investment:
        return AppColors.tertiary;
      case AccountType.cash:
        return Colors.green;
      case AccountType.loan:
        return AppColors.error;
      case AccountType.asset:
        return Colors.teal;
      case AccountType.liability:
        return Colors.red;
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.payments;
      case AccountType.loan:
        return Icons.money_off;
      case AccountType.asset:
        return Icons.home;
      case AccountType.liability:
        return Icons.warning;
    }
  }

  Widget _buildRecentTransactionsCard(BuildContext context, AsyncValue<List<Transaction>> recentTransactionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.go('/transactions'),
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            recentTransactionsAsync.when(
              data: (transactions) {
                if (transactions.isEmpty) {
                  return _buildEmptyTransactionsState(context);
                }
                
                return Column(
                  children: [
                    ...transactions.take(3).map((transaction) => Padding(
                      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                      child: _buildTransactionItem(context, transaction),
                    )),
                    if (transactions.length > 3)
                      Padding(
                        padding: const EdgeInsets.only(top: AppSpacing.sm),
                        child: TextButton(
                          onPressed: () => context.go('/transactions'),
                          child: Text('View ${transactions.length - 3} more transactions'),
                        ),
                      ),
                  ],
                );
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppSpacing.lg),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stack) => _buildErrorState(context, 'Failed to load transactions'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(BuildContext context, Transaction transaction) {
    final isIncome = transaction.type == TransactionType.income;
    final isExpense = transaction.type == TransactionType.expense;
    
    return InkWell(
      onTap: () => context.push('/transaction/${transaction.id}'),
      borderRadius: BorderRadius.circular(AppBorderRadius.md),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: _getTransactionTypeColor(transaction.type).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
              ),
              child: Icon(
                _getTransactionTypeIcon(transaction.type),
                color: _getTransactionTypeColor(transaction.type),
                size: 24,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.description ?? '',
                    style: const TextStyle(
                      fontWeight: AppTypography.fontWeightMedium,
                    ),
                  ),
                  Text(
                    _formatTransactionDate(transaction.date),
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: AppTypography.fontSizeSm,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              '${isExpense ? '-' : isIncome ? '+' : ''}\$${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                color: isExpense ? AppColors.error : isIncome ? AppColors.success : AppColors.primary,
                fontWeight: AppTypography.fontWeightSemiBold,
                fontSize: AppTypography.fontSizeMd,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyTransactionsState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No transactions yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Add your first transaction to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            const Icon(
              Icons.error,
              size: 48,
              color: AppColors.error,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Monthly Budget',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                  ),
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text('Details'),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            _buildBudgetItem('Groceries', 300.00, 250.00, AppColors.warning),
            const SizedBox(height: AppSpacing.sm),
            _buildBudgetItem('Entertainment', 200.00, 150.00, AppColors.tertiary),
            const SizedBox(height: AppSpacing.sm),
            _buildBudgetItem('Transportation', 150.00, 100.00, AppColors.primary),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetItem(
    String category,
    double total,
    double spent,
    Color color,
  ) {
    final percentage = (spent / total).clamp(0.0, 1.0);
    final isOverBudget = spent > total;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              category,
              style: const TextStyle(
                fontWeight: AppTypography.fontWeightMedium,
                fontSize: AppTypography.fontSizeMd,
              ),
            ),
            Text(
              '\$${spent.toStringAsFixed(0)} / \$${total.toStringAsFixed(0)}',
              style: TextStyle(
                color: isOverBudget ? AppColors.error : AppColors.textSecondary,
                fontSize: AppTypography.fontSizeSm,
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: AppColors.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(
            isOverBudget ? AppColors.error : color,
          ),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
      ],
    );
  }

  Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return AppColors.success;
      case TransactionType.expense:
        return AppColors.error;
      case TransactionType.transfer:
        return AppColors.primary;
    }
  }

  IconData _getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.trending_up;
      case TransactionType.expense:
        return Icons.trending_down;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  String _formatTransactionDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }
}

