import 'package:budapp/models/category.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:budapp/models/transaction.dart';
import 'package:budapp/providers/transactions_provider.dart';
import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/utils/account_utils.dart';
import 'package:budapp/utils/category_utils.dart';

class EditTransactionScreen extends ConsumerStatefulWidget {
  final String transactionId;

  const EditTransactionScreen({
    super.key,
    required this.transactionId,
  });

  @override
  ConsumerState<EditTransactionScreen> createState() => _EditTransactionScreenState();
}

class _EditTransactionScreenState extends ConsumerState<EditTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime? _selectedDate;
  bool _isLoading = false;
  Transaction? _originalTransaction;

  @override
  void dispose() {
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactionAsync = ref.watch(transactionDetailProvider(widget.transactionId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Transaction'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: transactionAsync.when(
        data: (transaction) => transaction != null
            ? _buildEditForm(context, transaction)
            : _buildNotFoundState(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(context, error),
      ),
    );
  }

  Widget _buildEditForm(BuildContext context, Transaction transaction) {
    // Initialize form data if not already done
    if (_originalTransaction == null) {
      _originalTransaction = transaction;
      // Null-safe initialization of controllers
      _descriptionController.text = transaction.description ?? '';
      _notesController.text = transaction.notes ?? '';
      _selectedDate = transaction.date;
    }

    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(AppSpacing.md),
        children: [
          // Transaction Type Display (Read-only)
          _buildTransactionTypeDisplay(transaction),
          const SizedBox(height: AppSpacing.lg),

          // Description Field
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Enter transaction description',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Description is required';
              }
              return null;
            },
          ),
          const SizedBox(height: AppSpacing.md),

          // Amount Display (Read-only for now)
          _buildAmountDisplay(transaction),
          const SizedBox(height: AppSpacing.md),

          // Date Field
          InkWell(
            onTap: _selectDate,
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Date',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              child: Text(
                DateFormat('MMM d, y').format(_selectedDate!),
              ),
            ),
          ),
          const SizedBox(height: AppSpacing.md),

          // Account Information (Read-only)
          _buildAccountsDisplay(transaction),
          const SizedBox(height: AppSpacing.md),

          // Category Information (Read-only for transfers)
          if (transaction.type != TransactionType.transfer)
            _buildCategoryDisplay(transaction),
          
          if (transaction.type != TransactionType.transfer)
            const SizedBox(height: AppSpacing.md),

          // Notes Field
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Notes (Optional)',
              hintText: 'Add any additional notes',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: AppSpacing.md),

          // Status Selection
          _buildStatusSelection(transaction),
          const SizedBox(height: AppSpacing.xl),

          // Save Button
          ElevatedButton(
            onPressed: _isLoading ? null : _saveTransaction,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
            ),
            child: _isLoading
                ? const CircularProgressIndicator()
                : const Text('Save Changes'),
          ),

          const SizedBox(height: AppSpacing.md),

          // Info Card
          Card(
            color: Colors.blue.shade50,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue.shade700),
                      const SizedBox(width: AppSpacing.sm),
                      Text(
                        'Edit Limitations',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    'For financial integrity, you can only edit the description, date, notes, and status of a transaction. To change amounts or accounts, please delete this transaction and create a new one.',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionTypeDisplay(Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: _getTransactionColor(transaction.type).withValues(alpha: 0.1),
              child: Text(
                transaction.type.icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Transaction Type',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  transaction.type.displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountDisplay(Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            Icon(
              Icons.attach_money,
              color: Colors.grey[600],
            ),
            const SizedBox(width: AppSpacing.md),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Amount',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  _formatTransactionAmount(transaction),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: _getAmountColor(transaction),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountsDisplay(Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              transaction.type == TransactionType.transfer ? 'Accounts' : 'Account',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            ...transaction.journalLines.map((line) => _buildAccountLineItem(line)),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountLineItem(JournalLine line) {
    final isDebit = line.type == TransactionEntryType.debit;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isDebit ? Colors.green : Colors.red,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          if (line.account != null) ...[
            Text(
              line.account!.type.icon,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: AppSpacing.sm),
          ],
          Expanded(
            child: Text(
              // Null-safe account name with fallback
              line.account?.name ?? 'Unknown Account',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            '${isDebit ? '+' : '-'}${NumberFormat.currency(symbol: '\$').format(line.amount)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isDebit ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDisplay(Transaction transaction) {
    // Null-safe category display with fallback
    final categoryLine = transaction.journalLines
        .firstWhere((line) => line.category != null, orElse: () => transaction.journalLines.first);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            if (categoryLine.category != null) ...[
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Color(categoryLine.category?.colorValue ?? 0xFFCCCCCC),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    categoryLine.category?.icon ?? '📁',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Category',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    categoryLine.category?.name ?? 'Uncategorized',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ] else ...[
              Icon(
                Icons.category_outlined,
                color: Colors.grey[600],
              ),
              const SizedBox(width: AppSpacing.md),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Category',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    'No category assigned',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSelection(Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Wrap(
              spacing: AppSpacing.sm,
              children: TransactionStatus.values.map((status) {
                final isSelected = transaction.status == status;
                return ChoiceChip(
                  label: Text(status.displayName),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _originalTransaction = _originalTransaction!.copyWith(status: status);
                      });
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Transaction not found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Error loading transaction',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate!,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate() || _originalTransaction == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final input = UpdateTransactionInput(
        description: _descriptionController.text.trim(),
        date: _selectedDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        status: _originalTransaction!.status,
      );

      await ref.read(transactionsProvider().notifier).updateTransaction(
        widget.transactionId,
        input,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating transaction: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  Color _getAmountColor(Transaction transaction) {
    switch (transaction.type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  String _formatTransactionAmount(Transaction transaction) {
    final sign = transaction.type == TransactionType.income ? '+' : 
                 transaction.type == TransactionType.expense ? '-' : '';
    return '$sign${NumberFormat.currency(symbol: '\$').format(transaction.amount)}';
  }
} 