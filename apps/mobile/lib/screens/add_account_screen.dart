import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/providers/accounts_provider.dart';
import 'package:budapp/config/design_tokens.dart';

class AddAccountScreen extends ConsumerStatefulWidget {
  const AddAccountScreen({super.key});

  @override
  ConsumerState<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends ConsumerState<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _initialBalanceController = TextEditingController();
  final _notesController = TextEditingController();

  AccountType _selectedAccountType = AccountType.checking;
  String _selectedCurrency = 'USD';
  bool _includeInNetWorth = true;
  bool _isLoading = false;

  // Account type configurations with icons and descriptions
  static const Map<AccountType, Map<String, dynamic>> _accountTypeConfig = {
    AccountType.checking: {
      'icon': Icons.account_balance,
      'label': 'Checking Account',
      'description': 'For everyday spending and transactions',
      'color': Colors.blue,
    },
    AccountType.savings: {
      'icon': Icons.savings,
      'label': 'Savings Account',
      'description': 'For saving money and earning interest',
      'color': Colors.green,
    },
    AccountType.creditCard: {
      'icon': Icons.credit_card,
      'label': 'Credit Card',
      'description': 'For credit purchases and building credit',
      'color': Colors.orange,
    },
    AccountType.cash: {
      'icon': Icons.money,
      'label': 'Cash',
      'description': 'Physical cash on hand',
      'color': Colors.teal,
    },
    AccountType.investment: {
      'icon': Icons.trending_up,
      'label': 'Investment Account',
      'description': 'For stocks, bonds, and other investments',
      'color': Colors.purple,
    },
    AccountType.loan: {
      'icon': Icons.account_balance_wallet,
      'label': 'Loan',
      'description': 'Money you owe to others',
      'color': Colors.red,
    },
    AccountType.asset: {
      'icon': Icons.home,
      'label': 'Asset',
      'description': 'Valuable items you own',
      'color': Colors.indigo,
    },
    AccountType.liability: {
      'icon': Icons.receipt_long,
      'label': 'Liability',
      'description': 'Debts and obligations',
      'color': Colors.deepOrange,
    },
  };

  @override
  void dispose() {
    _nameController.dispose();
    _initialBalanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Account'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveAccount,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppSpacing.md),
          children: [
            _buildAccountTypeSection(),
            const SizedBox(height: AppSpacing.lg),
            _buildAccountDetailsSection(),
            const SizedBox(height: AppSpacing.lg),
            _buildInitialBalanceSection(),
            const SizedBox(height: AppSpacing.lg),
            _buildAdditionalOptionsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Choose the type that best describes this account',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            Wrap(
              spacing: AppSpacing.sm,
              runSpacing: AppSpacing.sm,
              children: AccountType.values.map((type) {
                final config = _accountTypeConfig[type]!;
                final isSelected = _selectedAccountType == type;
                
                return FilterChip(
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedAccountType = type;
                      });
                    }
                  },
                  avatar: Icon(
                    config['icon'] as IconData,
                    size: 18,
                    color: isSelected ? Colors.white : config['color'],
                  ),
                  label: Text(config['label'] as String),
                  backgroundColor: Colors.grey[100],
                  selectedColor: config['color'],
                  checkmarkColor: Colors.white,
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                );
              }).toList(),
            ),
            if (_accountTypeConfig[_selectedAccountType] != null) ...[
              const SizedBox(height: AppSpacing.sm),
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: _accountTypeConfig[_selectedAccountType]!['color'].withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  border: Border.all(
                    color: _accountTypeConfig[_selectedAccountType]!['color'].withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: _accountTypeConfig[_selectedAccountType]!['color'],
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Expanded(
                      child: Text(
                        _accountTypeConfig[_selectedAccountType]!['description'] as String,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _accountTypeConfig[_selectedAccountType]!['color'],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAccountDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Account Name',
                hintText: 'e.g., Chase Checking, Emergency Fund',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Account name is required';
                }
                if (value.trim().length > 255) {
                  return 'Account name must be less than 255 characters';
                }
                return null;
              },
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: AppSpacing.md),
            DropdownButtonFormField<String>(
              value: _selectedCurrency,
              decoration: const InputDecoration(
                labelText: 'Currency',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'USD', child: Text('USD - US Dollar')),
                DropdownMenuItem(value: 'EUR', child: Text('EUR - Euro')),
                DropdownMenuItem(value: 'GBP', child: Text('GBP - British Pound')),
                DropdownMenuItem(value: 'CAD', child: Text('CAD - Canadian Dollar')),
                DropdownMenuItem(value: 'AUD', child: Text('AUD - Australian Dollar')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCurrency = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialBalanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Initial Balance',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Enter the current balance of this account',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            TextFormField(
              controller: _initialBalanceController,
              decoration: InputDecoration(
                labelText: 'Amount',
                hintText: '0.00',
                prefixText: '\$',
                border: const OutlineInputBorder(),
                helperText: _getBalanceHelperText(),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return null; // Initial balance is optional, defaults to 0
                }
                final amount = double.tryParse(value);
                if (amount == null) {
                  return 'Please enter a valid amount';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalOptionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Options',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            SwitchListTile(
              title: const Text('Include in Net Worth'),
              subtitle: const Text('Include this account when calculating your total net worth'),
              value: _includeInNetWorth,
              onChanged: (value) {
                setState(() {
                  _includeInNetWorth = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
            const SizedBox(height: AppSpacing.md),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Add any additional notes about this account',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              maxLength: 1000,
              validator: (value) {
                if (value != null && value.length > 1000) {
                  return 'Notes must be less than 1000 characters';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getBalanceHelperText() {
    switch (_selectedAccountType) {
      case AccountType.creditCard:
      case AccountType.loan:
      case AccountType.liability:
        return 'Enter the amount you owe (will be shown as negative)';
      case AccountType.checking:
      case AccountType.savings:
        return 'Enter your current account balance';
      case AccountType.cash:
        return 'Enter the amount of cash you have';
      case AccountType.investment:
        return 'Enter the current value of your investments';
      case AccountType.asset:
        return 'Enter the estimated value of this asset';
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Parse initial balance, default to 0 if empty
      double initialBalance = 0.0;
      if (_initialBalanceController.text.trim().isNotEmpty) {
        initialBalance = double.parse(_initialBalanceController.text.trim());
      }

      // For liability accounts (credit cards, loans), make the balance negative
      if (_selectedAccountType == AccountType.creditCard ||
          _selectedAccountType == AccountType.loan ||
          _selectedAccountType == AccountType.liability) {
        initialBalance = -initialBalance.abs();
      }

      // Get icon and color from account type configuration
      final accountConfig = _accountTypeConfig[_selectedAccountType]!;
      final iconName = accountConfig['icon'].toString().split('.').last; // Convert IconData to string
      final colorHex = '#${(accountConfig['color'] as Color).toARGB32().toRadixString(16).substring(2).toUpperCase()}';

      final input = CreateAccountInput(
        name: _nameController.text.trim(),
        type: _selectedAccountType,
        currency: _selectedCurrency,
        initialBalance: initialBalance,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        icon: iconName,
        color: colorHex,
        includeInNetWorth: _includeInNetWorth,
        displayOrder: 0, // Will be handled by backend
      );

      final account = await ref.read(accountsProvider.notifier).createAccount(input);

      if (account != null && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Account "${account.name}" created successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to accounts screen
        context.pop();
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to create account. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 