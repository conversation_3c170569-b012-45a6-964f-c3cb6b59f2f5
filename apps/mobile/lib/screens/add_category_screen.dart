import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/utils/category_utils.dart';
import 'package:budapp/providers/category_provider.dart';

enum CategoryType { income, expense }

class AddCategoryScreen extends ConsumerStatefulWidget {
  const AddCategoryScreen({super.key});

  @override
  ConsumerState<AddCategoryScreen> createState() => _AddCategoryScreenState();
}

class _AddCategoryScreenState extends ConsumerState<AddCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  
  CategoryType _selectedCategoryType = CategoryType.expense;
  String? _selectedIcon;
  Color? _selectedColor;
  bool _isLoading = false;

  // Predefined icons for categories
  static const List<Map<String, dynamic>> _categoryIcons = [
    {'icon': Icons.home, 'name': 'home'},
    {'icon': Icons.restaurant, 'name': 'restaurant'},
    {'icon': Icons.directions_car, 'name': 'car'},
    {'icon': Icons.shopping_cart, 'name': 'shopping-cart'},
    {'icon': Icons.local_gas_station, 'name': 'gas'},
    {'icon': Icons.medical_services, 'name': 'medical'},
    {'icon': Icons.school, 'name': 'education'},
    {'icon': Icons.movie, 'name': 'entertainment'},
    {'icon': Icons.fitness_center, 'name': 'fitness'},
    {'icon': Icons.spa, 'name': 'personal-care'},
    {'icon': Icons.flight, 'name': 'travel'},
    {'icon': Icons.card_giftcard, 'name': 'gifts'},
    {'icon': Icons.pets, 'name': 'pets'},
    {'icon': Icons.phone, 'name': 'phone'},
    {'icon': Icons.electrical_services, 'name': 'utilities'},
    {'icon': Icons.security, 'name': 'insurance'},
    {'icon': Icons.savings, 'name': 'savings'},
    {'icon': Icons.trending_up, 'name': 'investments'},
    {'icon': Icons.work, 'name': 'salary'},
    {'icon': Icons.business, 'name': 'business'},
    {'icon': Icons.account_balance, 'name': 'bank'},
    {'icon': Icons.monetization_on, 'name': 'money'},
    {'icon': Icons.more_horiz, 'name': 'other'},
  ];

  // Predefined colors for categories
  static const List<Color> _categoryColors = [
    Color(0xFF2196F3), // Blue
    Color(0xFF4CAF50), // Green
    Color(0xFFF44336), // Red
    Color(0xFFFF9800), // Orange
    Color(0xFF9C27B0), // Purple
    Color(0xFF00BCD4), // Cyan
    Color(0xFF795548), // Brown
    Color(0xFF607D8B), // Blue Grey
    Color(0xFFE91E63), // Pink
    Color(0xFF3F51B5), // Indigo
    Color(0xFF009688), // Teal
    Color(0xFFFF5722), // Deep Orange
    Color(0xFF8BC34A), // Light Green
    Color(0xFFFFEB3B), // Yellow
    Color(0xFF673AB7), // Deep Purple
    Color(0xFF9E9E9E), // Grey
  ];

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Category'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveCategory,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppSpacing.md),
          children: [
            _buildCategoryTypeSection(),
            const SizedBox(height: AppSpacing.lg),
            _buildCategoryDetailsSection(),
            const SizedBox(height: AppSpacing.lg),
            _buildIconSelectionSection(),
            const SizedBox(height: AppSpacing.lg),
            _buildColorSelectionSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Row(
              children: [
                Expanded(
                  child: _buildTypeOption(
                    CategoryType.expense,
                    'Expense',
                    Icons.remove_circle_outline,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: _buildTypeOption(
                    CategoryType.income,
                    'Income',
                    Icons.add_circle_outline,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeOption(CategoryType type, String label, IconData icon, Color color) {
    final isSelected = _selectedCategoryType == type;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategoryType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isSelected ? color : Colors.grey[600],
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              label,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? color : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Category Name',
                hintText: 'Enter category name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Category name is required';
                }
                if (value.trim().length > 255) {
                  return 'Category name must be less than 255 characters';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Icon',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 6,
                crossAxisSpacing: AppSpacing.sm,
                mainAxisSpacing: AppSpacing.sm,
                childAspectRatio: 1,
              ),
              itemCount: _categoryIcons.length,
              itemBuilder: (context, index) {
                final iconData = _categoryIcons[index];
                final isSelected = _selectedIcon == iconData['name'];
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIcon = iconData['name'];
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? AppColors.primary.withOpacity(0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? AppColors.primary : Colors.grey[300]!,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      iconData['icon'],
                      size: 24,
                      color: isSelected ? AppColors.primary : Colors.grey[600],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Color',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 8,
                crossAxisSpacing: AppSpacing.sm,
                mainAxisSpacing: AppSpacing.sm,
                childAspectRatio: 1,
              ),
              itemCount: _categoryColors.length,
              itemBuilder: (context, index) {
                final color = _categoryColors[index];
                final isSelected = _selectedColor == color;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.black : Colors.grey[300]!,
                        width: isSelected ? 3 : 1,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _saveCategory() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedIcon == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an icon')),
      );
      return;
    }

    if (_selectedColor == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a color')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Convert color to hex string for storage
      final colorHex = CategoryUtils.getHexFromColor(_selectedColor!);
      
      // Create category using the provider
      await ref.read(categoryNotifierProvider.notifier).createCategory(
        name: _nameController.text.trim(),
        type: _selectedCategoryType.name,
        icon: _selectedIcon!,
        color: colorHex,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Category created successfully!')),
        );
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating category: $error')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 