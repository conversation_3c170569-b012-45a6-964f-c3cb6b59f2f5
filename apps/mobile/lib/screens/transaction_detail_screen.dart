import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:budapp/models/transaction.dart';
import 'package:budapp/providers/transactions_provider.dart';
import 'package:budapp/config/design_tokens.dart';

class TransactionDetailScreen extends ConsumerWidget {
  final String transactionId;

  const TransactionDetailScreen({
    super.key,
    required this.transactionId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionAsync = ref.watch(transactionDetailProvider(transactionId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              context.push('/edit-transaction/$transactionId');
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'delete':
                  await _showDeleteConfirmation(context, ref);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: transactionAsync.when(
        data: (transaction) => transaction != null
            ? _buildTransactionDetails(context, transaction)
            : _buildNotFoundState(context),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(context, error),
      ),
    );
  }

  Widget _buildTransactionDetails(BuildContext context, Transaction transaction) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Transaction Header
          _buildTransactionHeader(context, transaction),
          const SizedBox(height: AppSpacing.lg),

          // Transaction Info
          _buildTransactionInfo(context, transaction),
          const SizedBox(height: AppSpacing.lg),

          // Journal Lines (Double-Entry Details)
          _buildJournalLines(context, transaction),
          
          // Notes (if any)
          // Safely handle nullable notes
          if ((transaction.notes ?? '').isNotEmpty) ...[
            const SizedBox(height: AppSpacing.lg),
            _buildNotesSection(context, transaction),
          ],

          // Recurring Pattern (if any)
          // Safely handle nullable recurringPattern
          if (transaction.isRecurring && transaction.recurringPattern != null) ...[
            const SizedBox(height: AppSpacing.lg),
            _buildRecurringSection(context, transaction),
          ],

          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }

  Widget _buildTransactionHeader(BuildContext context, Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getTransactionColor(transaction.type).withValues(alpha: 0.1),
                  radius: 24,
                  child: Text(
                    transaction.type.icon,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        transaction.description ?? '',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        transaction.type.displayName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _formatTransactionAmount(transaction),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getAmountColor(transaction),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.xs,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(transaction.status).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                      ),
                      child: Text(
                        transaction.status.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: _getStatusColor(transaction.status),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionInfo(BuildContext context, Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transaction Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            _buildInfoRow('Date', DateFormat('EEEE, MMMM d, y').format(transaction.date)),
            _buildInfoRow('Time', DateFormat('h:mm a').format(transaction.date)),
            _buildInfoRow('Amount', NumberFormat.currency(symbol: '\$').format(transaction.amount)),
            _buildInfoRow('Status', transaction.status.displayName),
            if (transaction.isRecurring)
              _buildInfoRow('Recurring', 'Yes'),
            _buildInfoRow('Created', DateFormat('MMM d, y h:mm a').format(transaction.createdAt)),
            if (transaction.updatedAt != transaction.createdAt)
              _buildInfoRow('Updated', DateFormat('MMM d, y h:mm a').format(transaction.updatedAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJournalLines(BuildContext context, Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Journal Entries',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Tooltip(
                  message: 'Double-entry accounting ensures every transaction affects at least two accounts',
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            ...transaction.journalLines.map((line) => _buildJournalLineItem(context, line)),
            const Divider(),
            _buildJournalLineSummary(context, transaction.journalLines),
          ],
        ),
      ),
    );
  }

  Widget _buildJournalLineItem(BuildContext context, JournalLine line) {
    final isDebit = line.type == TransactionEntryType.debit;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isDebit ? Colors.green : Colors.red,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  // Null-safe account name with fallback
                  line.account?.name ?? 'Unknown Account',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (line.category != null)
                  Text(
                    // Null-safe category name with fallback
                    line.category?.name ?? 'Uncategorized',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                if (line.notes != null && line.notes!.isNotEmpty)
                  Text(
                    line.notes!,
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                line.type.name.toUpperCase(),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isDebit ? Colors.green : Colors.red,
                ),
              ),
              Text(
                NumberFormat.currency(symbol: '\$').format(line.amount),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isDebit ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJournalLineSummary(BuildContext context, List<JournalLine> lines) {
    final totalDebits = lines
        .where((line) => line.type == TransactionEntryType.debit)
        .fold<double>(0, (sum, line) => sum + line.amount);
    
    final totalCredits = lines
        .where((line) => line.type == TransactionEntryType.credit)
        .fold<double>(0, (sum, line) => sum + line.amount);

    final isBalanced = (totalDebits - totalCredits).abs() < 0.01;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Balance Check',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Row(
          children: [
            Icon(
              isBalanced ? Icons.check_circle : Icons.error,
              size: 16,
              color: isBalanced ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 4),
            Text(
              isBalanced ? 'Balanced' : 'Unbalanced',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: isBalanced ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotesSection(BuildContext context, Transaction transaction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              // Null-safe notes display
              transaction.notes ?? '',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecurringSection(BuildContext context, Transaction transaction) {
    // Null-safe recurringPattern usage with fallback
    final pattern = transaction.recurringPattern;
    if (pattern == null) {
      return const SizedBox.shrink();
    }
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recurring Pattern',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            _buildInfoRow('Type', pattern.type.name.toUpperCase()),
            _buildInfoRow('Interval', 'Every ${pattern.interval} ${pattern.type.name.toLowerCase()}(s)'),
            if (pattern.endDate != null)
              _buildInfoRow('End Date', DateFormat('MMM d, y').format(pattern.endDate!)),
            if (pattern.occurrences != null)
              _buildInfoRow('Occurrences', '${pattern.occurrences} times'),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Transaction not found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Error loading transaction',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: const Text(
          'Are you sure you want to delete this transaction? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(transactionsProvider().notifier).deleteTransaction(transactionId);
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Transaction deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          context.pop();
        }
      } catch (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting transaction: $error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  Color _getAmountColor(Transaction transaction) {
    switch (transaction.type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  Color _getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.pending:
        return Colors.orange;
      case TransactionStatus.completed:
        return Colors.green;
      case TransactionStatus.cancelled:
        return Colors.red;
      case TransactionStatus.reconciled:
        return Colors.blue;
    }
  }

  String _formatTransactionAmount(Transaction transaction) {
    final sign = transaction.type == TransactionType.income ? '+' : 
                 transaction.type == TransactionType.expense ? '-' : '';
    return '$sign${NumberFormat.currency(symbol: '\$').format(transaction.amount)}';
  }
} 