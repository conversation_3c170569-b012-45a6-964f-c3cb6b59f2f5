import 'package:json_annotation/json_annotation.dart';

part 'category.g.dart';

@JsonSerializable()
class Category {
  final String id;
  final String name;
  @JsonKey(fromJson: CategoryTypeConverter.fromJson, toJson: CategoryTypeConverter.toJson)
  final CategoryType type;
  final String? parentId;
  final String? icon;
  final String? color;
  final bool isDefault;
  final bool isSystem;
  final bool isArchived;
  final int displayOrder;
  @<PERSON><PERSON><PERSON><PERSON>(fromJson: _parseDateTime)
  final DateTime createdAt;
  @JsonKey(fromJson: _parseDateTime)
  final DateTime updatedAt;
  
  // Optional fields for hierarchical display
  final List<Category>? children;
  final Category? parent;

  const Category({
    required this.id,
    required this.name,
    required this.type,
    this.parentId,
    this.icon,
    this.color,
    required this.isDefault,
    required this.isSystem,
    required this.isArchived,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
    this.children,
    this.parent,
  });

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);


  Category copyWith({
    String? id,
    String? name,
    CategoryType? type,
    String? parentId,
    String? icon,
    String? color,
    bool? isDefault,
    bool? isSystem,
    bool? isArchived,
    int? displayOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<Category>? children,
    Category? parent,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      parentId: parentId ?? this.parentId,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isDefault: isDefault ?? this.isDefault,
      isSystem: isSystem ?? this.isSystem,
      isArchived: isArchived ?? this.isArchived,
      displayOrder: displayOrder ?? this.displayOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      children: children ?? this.children,
      parent: parent ?? this.parent,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Category(id: $id, name: $name, type: $type, icon: $icon, color: $color)';
  }

  // Helper getters
  bool get isIncome => type == CategoryType.income;
  bool get isExpense => type == CategoryType.expense;
  bool get hasParent => parentId != null;
  bool get hasChildren => children != null && children!.isNotEmpty;
  bool get isUserCreated => !isSystem && !isDefault;
}

extension CategoryEnumHelpers on Category {
 CategoryType get categoryType => type;

 int get colorValue {
   if (color == null) return 0xFF6B7280; // Default gray color

   String colorStr = color!;
   if (colorStr.startsWith('#')) {
     colorStr = colorStr.substring(1);
   }
   if (colorStr.length == 6) {
     colorStr = 'FF$colorStr'; // Add alpha if missing
   }

   return int.tryParse(colorStr, radix: 16) ?? 0xFF6B7280;
 }
}

enum CategoryType {
 @JsonValue('INCOME')
 income,
 @JsonValue('EXPENSE')
 expense,
}

class CategoryTypeConverter {
  static CategoryType fromJson(String value) {
    switch (value.toUpperCase()) {
      case 'INCOME':
        return CategoryType.income;
      case 'EXPENSE':
        return CategoryType.expense;
      default:
        throw ArgumentError('Unknown CategoryType: $value');
    }
  }

  static String toJson(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return 'INCOME';
      case CategoryType.expense:
        return 'EXPENSE';
    }
  }
}

// Helper function for robust date parsing
DateTime _parseDateTime(dynamic dateValue) {
  if (dateValue == null) {
    return DateTime.now();
  }
  
  if (dateValue is String) {
    // Try parsing as ISO string first
    try {
      return DateTime.parse(dateValue);
    } catch (e) {
      // If that fails, try parsing as timestamp
      final timestamp = int.tryParse(dateValue);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      // Fallback to current time
      return DateTime.now();
    }
  }
  
  if (dateValue is int) {
    return DateTime.fromMillisecondsSinceEpoch(dateValue);
  }
  
  // Fallback to current time
  return DateTime.now();
} 