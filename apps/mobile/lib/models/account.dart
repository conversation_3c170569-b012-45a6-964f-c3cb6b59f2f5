import 'package:freezed_annotation/freezed_annotation.dart';

part 'account.freezed.dart';
part 'account.g.dart';

enum AccountType {
  @JsonValue('CHECKING')
  checking,
  @JsonValue('SAVINGS')
  savings,
  @JsonValue('CREDIT_CARD')
  creditCard,
  @JsonValue('CASH')
  cash,
  @JsonValue('INVESTMENT')
  investment,
  @JsonValue('LOAN')
  loan,
  @JsonValue('ASSET')
  asset,
  @JsonValue('LIABILITY')
  liability,
}

@freezed
class Account with _$Account {
  const factory Account({
    required String id,
    required String userId,
    required String name,
    required AccountType type,
    required String currency,
    required double initialBalance,
    required double currentBalance,
    required bool isArchived,
    String? notes,
    String? icon,
    String? color,
    required bool includeInNetWorth,
    required int displayOrder,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _Account;

  factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);
}
@freezed
class MiniAccount with _$MiniAccount {
  const factory MiniAccount({
    required String id,
    required String name,
    required AccountType type,
  }) = _MiniAccount;

  factory MiniAccount.fromJson(Map<String, dynamic> json) => _$MiniAccountFromJson(json);
}

@freezed
class CreateAccountInput with _$CreateAccountInput {
  const factory CreateAccountInput({
    required String name,
    required AccountType type,
    @Default('USD') String currency,
    @Default(0.0) double initialBalance,
    String? notes,
    String? icon,
    String? color,
    @Default(true) bool includeInNetWorth,
    @Default(0) int displayOrder,
  }) = _CreateAccountInput;

  factory CreateAccountInput.fromJson(Map<String, dynamic> json) => _$CreateAccountInputFromJson(json);
}

@freezed
class UpdateAccountInput with _$UpdateAccountInput {
  const factory UpdateAccountInput({
    String? name,
    AccountType? type,
    String? currency,
    String? notes,
    String? icon,
    String? color,
    bool? includeInNetWorth,
    int? displayOrder,
    bool? isArchived,
  }) = _UpdateAccountInput;

  factory UpdateAccountInput.fromJson(Map<String, dynamic> json) => _$UpdateAccountInputFromJson(json);
} 