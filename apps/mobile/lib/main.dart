import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:budapp/config/app_router.dart';
import 'package:budapp/config/app_theme.dart';
import 'package:budapp/providers/graphql_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GraphQL
  await initHiveForFlutter();

  runApp(
    const ProviderScope(
      child: BudApp(),
    ),
  );
}

class BudApp extends ConsumerWidget {
  const BudApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final graphQLClient = ref.watch(graphQLClientNotifierProvider);

    return GraphQLProvider(
      client: graphQLClient,
      child: MaterialApp.router(
        title: 'BudApp',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        routerConfig: router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
