import 'package:flutter/material.dart';

/// A reusable error banner widget that displays error messages
/// with appropriate styling and actions.
class ErrorBanner extends StatelessWidget {
  /// The error message to display
  final String message;

  /// Optional callback for retry action
  final VoidCallback? onRetry;

  /// Optional callback for dismiss action
  final VoidCallback? onDismiss;

  /// Whether to automatically dismiss the error after a certain duration
  final bool autoDismiss;

  /// Duration after which the error will be automatically dismissed
  final Duration autoDismissDuration;

  /// Creates an error banner with the given message and optional actions
  const ErrorBanner({
    super.key,
    required this.message,
    this.onRetry,
    this.onDismiss,
    this.autoDismiss = false,
    this.autoDismissDuration = const Duration(seconds: 5),
  });

  @override
  Widget build(BuildContext context) {
    // Auto-dismiss logic
    if (autoDismiss && onDismiss != null) {
      Future.delayed(autoDismissDuration, () {
        onDismiss!();
      });
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
              const SizedBox(width: 12.0),
              Expanded(
                child: Text(
                  message,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  icon: const Icon(Icons.close),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  color: Theme.of(context).colorScheme.onErrorContainer,
                  onPressed: onDismiss,
                ),
            ],
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 8.0),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: onRetry,
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.onErrorContainer,
                  backgroundColor: Theme.of(context).colorScheme.error.withValues(alpha: 0.2),
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                ),
                child: const Text('Retry'),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget that displays an error banner that can be dismissed
class DismissibleErrorBanner extends StatefulWidget {
  /// The error message to display
  final String? errorMessage;

  /// Optional callback for retry action
  final VoidCallback? onRetry;

  /// Optional callback when the error is dismissed
  final VoidCallback? onDismissed;

  /// Whether to automatically dismiss the error after a certain duration
  final bool autoDismiss;

  /// Duration after which the error will be automatically dismissed
  final Duration autoDismissDuration;

  /// Creates a dismissible error banner
  const DismissibleErrorBanner({
    super.key,
    this.errorMessage,
    this.onRetry,
    this.onDismissed,
    this.autoDismiss = false,
    this.autoDismissDuration = const Duration(seconds: 5),
  });

  @override
  State<DismissibleErrorBanner> createState() => _DismissibleErrorBannerState();
}

class _DismissibleErrorBannerState extends State<DismissibleErrorBanner> {
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _updateVisibility();
  }

  @override
  void didUpdateWidget(DismissibleErrorBanner oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.errorMessage != oldWidget.errorMessage) {
      _updateVisibility();
    }
  }

  void _updateVisibility() {
    setState(() {
      _isVisible = widget.errorMessage != null && widget.errorMessage!.isNotEmpty;
    });
  }

  void _dismiss() {
    setState(() {
      _isVisible = false;
    });
    if (widget.onDismissed != null) {
      widget.onDismissed!();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible || widget.errorMessage == null) {
      return const SizedBox.shrink();
    }

    return AnimatedOpacity(
      opacity: _isVisible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: ErrorBanner(
          message: widget.errorMessage!,
          onRetry: widget.onRetry,
          onDismiss: _dismiss,
          autoDismiss: widget.autoDismiss,
          autoDismissDuration: widget.autoDismissDuration,
        ),
      ),
    );
  }
}

/// Extension methods for parsing error messages from GraphQL and other sources
extension ErrorMessageParser on String {
  /// Extracts a user-friendly error message from a GraphQL error string
  String toUserFriendlyMessage() {
    // Handle common authentication errors
    if (contains('INVALID_CREDENTIALS')) {
      return 'Invalid email or password. Please try again.';
    } else if (contains('EMAIL_ALREADY_EXISTS')) {
      return 'An account with this email already exists.';
    } else if (contains('WEAK_PASSWORD')) {
      return 'Password is too weak. Please use a stronger password.';
    } else if (contains('TOKEN_EXPIRED')) {
      return 'Your session has expired. Please log in again.';
    } else if (contains('UNAUTHORIZED')) {
      return 'You are not authorized to perform this action.';
    } else if (contains('NETWORK_ERROR')) {
      return 'Network error. Please check your connection and try again.';
    }

    // Return a cleaned-up version of the original message
    // Remove Exception: prefix if present
    final cleanMessage = replaceAll(RegExp(r'^Exception: '), '');

    // If it's a long message, truncate it
    if (cleanMessage.length > 100) {
      return '${cleanMessage.substring(0, 100)}...';
    }

    return cleanMessage;
  }
}
