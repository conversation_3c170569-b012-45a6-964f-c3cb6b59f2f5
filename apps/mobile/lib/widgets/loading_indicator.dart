import 'package:flutter/material.dart';

/// A reusable loading indicator widget with different styles
class LoadingIndicator extends StatelessWidget {
  /// The size of the loading indicator
  final double size;

  /// The color of the loading indicator (if null, uses primary color)
  final Color? color;

  /// The style of the loading indicator
  final LoadingIndicatorStyle style;

  /// The text to display below the loading indicator
  final String? text;

  /// Whether to show the loading indicator in an overlay
  final bool overlay;

  /// The opacity of the overlay background
  final double overlayOpacity;

  /// Creates a loading indicator with the given parameters
  const LoadingIndicator({
    super.key,
    this.size = 40.0,
    this.color,
    this.style = LoadingIndicatorStyle.circular,
    this.text,
    this.overlay = false,
    this.overlayOpacity = 0.7,
  });

  @override
  Widget build(BuildContext context) {
    final loadingWidget = _buildLoadingWidget(context);

    if (overlay) {
      return Stack(
        children: [
          Positioned.fill(
            child: Container(
              color: Colors.black.withValues(alpha: overlayOpacity),
              child: Center(
                child: loadingWidget,
              ),
            ),
          ),
        ],
      );
    }

    return loadingWidget;
  }

  Widget _buildLoadingWidget(BuildContext context) {
    final indicatorColor = color ?? Theme.of(context).colorScheme.primary;

    Widget indicator;

    switch (style) {
      case LoadingIndicatorStyle.circular:
        indicator = SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: size / 10,
            color: indicatorColor,
          ),
        );
        break;
      case LoadingIndicatorStyle.linear:
        indicator = SizedBox(
          width: size * 5,
          child: LinearProgressIndicator(
            minHeight: size / 5,
            color: indicatorColor,
            backgroundColor: indicatorColor.withValues(alpha: 0.2),
          ),
        );
        break;
      case LoadingIndicatorStyle.dots:
        indicator = _buildDotsIndicator(context, indicatorColor);
        break;
    }

    if (text != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          indicator,
          const SizedBox(height: 16),
          Text(
            text!,
            style: TextStyle(
              color: overlay ? Colors.white : Theme.of(context).textTheme.bodyMedium?.color,
              fontSize: 14,
            ),
          ),
        ],
      );
    }

    return indicator;
  }

  Widget _buildDotsIndicator(BuildContext context, Color color) {
    return SizedBox(
      width: size * 3,
      height: size,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          3,
          (index) => _DotPulse(
            size: size / 3,
            color: color,
            delay: Duration(milliseconds: 300 * index),
          ),
        ),
      ),
    );
  }
}

/// The style of the loading indicator
enum LoadingIndicatorStyle {
  /// A circular progress indicator
  circular,

  /// A linear progress indicator
  linear,

  /// A pulsing dots indicator
  dots,
}

/// A pulsing dot animation for the dots loading indicator
class _DotPulse extends StatefulWidget {
  final double size;
  final Color color;
  final Duration delay;

  const _DotPulse({
    required this.size,
    required this.color,
    required this.delay,
  });

  @override
  State<_DotPulse> createState() => _DotPulseState();
}

class _DotPulseState extends State<_DotPulse> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              color: widget.color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }
}

/// A button with a loading state
class LoadingButton extends StatelessWidget {
  /// The text to display on the button
  final String text;

  /// The callback when the button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// The style of the button
  final ButtonStyle? style;

  /// The loading indicator size
  final double loadingIndicatorSize;

  /// Creates a button with a loading state
  const LoadingButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.style,
    this.loadingIndicatorSize = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? SizedBox(
              height: loadingIndicatorSize,
              width: loadingIndicatorSize,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            )
          : Text(text),
    );
  }
}
