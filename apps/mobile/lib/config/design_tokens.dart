// Generated file - do not edit manually
// Generated from packages/design/src/tokens.ts

import 'package:flutter/material.dart';

class AppColors {
  // Primary colors (Teal #01a2a1)
  static const primary = Color(0xFF01A2A1);
  static const primaryLight = Color(0xFF4DD4D3);
  static const primaryDark = Color(0xFF007372);
  static const onPrimary = Color(0xFFFFFFFF);
  static const primaryContainer = Color(0xFFB2DFDE);
  static const onPrimaryContainer = Color(0xFF002020);

  // Secondary colors (Coral Red #fc2f20 as accent)
  static const secondary = Color(0xFFFC2F20);
  static const secondaryLight = Color(0xFFFF6B5B);
  static const secondaryDark = Color(0xFFC30000);
  static const onSecondary = Color(0xFFFFFFFF);
  static const secondaryContainer = Color(0xFFFFDAD6);
  static const onSecondaryContainer = Color(0xFF410002);

  // Tertiary colors (Complementary warm tone)
  static const tertiary = Color(0xFFFF8F00);
  static const tertiaryLight = Color(0xFFFFBF47);
  static const tertiaryDark = Color(0xFFC56000);
  static const onTertiary = Color(0xFFFFFFFF);
  static const tertiaryContainer = Color(0xFFFFE0B2);
  static const onTertiaryContainer = Color(0xFF2A1800);

  // Error colors
  static const error = Color(0xFFBA1A1A);
  static const errorLight = Color(0xFFFFB4AB);
  static const errorDark = Color(0xFF93000A);
  static const onError = Color(0xFFFFFFFF);
  static const errorContainer = Color(0xFFFFDAD6);
  static const onErrorContainer = Color(0xFF410002);

  // Background colors - Light theme
  static const background = Color(0xFFFAFDFD);
  static const onBackground = Color(0xFF191C1C);
  static const surface = Color(0xFFFAFDFD);
  static const onSurface = Color(0xFF191C1C);
  static const surfaceVariant = Color(0xFFDAE5E4);
  static const onSurfaceVariant = Color(0xFF3F4948);
  static const surfaceTint = Color(0xFF01A2A1);

  // Background colors - Dark theme
  static const backgroundDark = Color(0xFF0F1415);
  static const onBackgroundDark = Color(0xFFDDE3E3);
  static const surfaceDark = Color(0xFF0F1415);
  static const onSurfaceDark = Color(0xFFDDE3E3);
  static const surfaceVariantDark = Color(0xFF3F4948);
  static const onSurfaceVariantDark = Color(0xFFBEC9C8);

  // Outline colors
  static const outline = Color(0xFF6F7978);
  static const outlineVariant = Color(0xFFBEC9C8);
  static const outlineDark = Color(0xFF899392);
  static const outlineVariantDark = Color(0xFF3F4948);

  // Text colors (derived from surface colors)
  static const textPrimary = Color(0xFF191C1C);
  static const textSecondary = Color(0xFF5F6363);
  static const textDisabled = Color(0xFF9E9E9E);
  static const textHint = Color(0xFF9E9E9E);
  static const textPrimaryDark = Color(0xFFDDE3E3);
  static const textSecondaryDark = Color(0xFFBEC9C8);

  // Splash screen background
  static const splashBackground = Color(0xFF01A2A1);

  // Success colors (for financial positive indicators)
  static const success = Color(0xFF2E7D32);
  static const successLight = Color(0xFF66BB6A);
  static const successDark = Color(0xFF1B5E20);
  static const onSuccess = Color(0xFFFFFFFF);

  // Warning colors (for financial alerts)
  static const warning = Color(0xFFED6C02);
  static const warningLight = Color(0xFFFFB74D);
  static const warningDark = Color(0xFFE65100);
  static const onWarning = Color(0xFFFFFFFF);

  // Info colors (for neutral financial information)
  static const info = Color(0xFF0288D1);
  static const infoLight = Color(0xFF64B5F6);
  static const infoDark = Color(0xFF01579B);
  static const onInfo = Color(0xFFFFFFFF);
}

class AppSpacing {
  static const xs = 4.0;
  static const sm = 8.0;
  static const md = 16.0;
  static const lg = 24.0;
  static const xl = 32.0;
  static const xxl = 48.0;
}

class AppTypography {
  static const fontFamily = 'Inter';

  static const fontSizeXs = 12.0;
  static const fontSizeSm = 14.0;
  static const fontSizeMd = 16.0;
  static const fontSizeLg = 18.0;
  static const fontSizeXl = 20.0;
  static const fontSizeXxl = 24.0;
  static const fontSizeXxxl = 30.0;

  static const fontWeightRegular = FontWeight.w400;
  static const fontWeightMedium = FontWeight.w500;
  static const fontWeightSemiBold = FontWeight.w600;
  static const fontWeightBold = FontWeight.w700;
}

class AppBorderRadius {
  static const xs = 2.0;
  static const sm = 4.0;
  static const md = 8.0;
  static const lg = 12.0;
  static const xl = 16.0;
  static const xxl = 24.0;
  static const round = 9999.0;
}
