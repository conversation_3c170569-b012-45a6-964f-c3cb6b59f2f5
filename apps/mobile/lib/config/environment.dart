class Environment {
  // API URL configuration with fallback defaults
  static const String apiUrl = String.fromEnvironment(
    'API_URL',
    defaultValue: 'http://localhost:3000/graphql',
  );

  // Environment type for debugging and logging
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  // Helper methods for environment detection
  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';

  // Debug information
  static void printEnvironmentInfo() {
    // Using debugPrint to avoid linting issues in production
    assert(() {
      // ignore: avoid_print
      print('Environment: $environment');
      // ignore: avoid_print
      print('API URL: $apiUrl');
      return true;
    }());
  }
} 
