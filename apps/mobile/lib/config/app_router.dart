import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:budapp/providers/auth_provider.dart';
import 'package:budapp/screens/accounts_screen.dart';
import 'package:budapp/screens/add_account_screen.dart';
import 'package:budapp/screens/add_category_screen.dart';
import 'package:budapp/screens/add_transaction_screen.dart';
import 'package:budapp/screens/categories_screen.dart';
import 'package:budapp/screens/edit_transaction_screen.dart';
import 'package:budapp/screens/home_screen.dart';
import 'package:budapp/screens/login_screen.dart';
import 'package:budapp/screens/profile_screen.dart';
import 'package:budapp/screens/register_screen.dart';
import 'package:budapp/screens/reports_screen.dart';
import 'package:budapp/screens/splash_screen.dart';
import 'package:budapp/screens/transaction_detail_screen.dart';
import 'package:budapp/screens/transactions_screen.dart';
import 'package:budapp/widgets/main_navigation_shell.dart';
import 'package:budapp/models/transaction.dart';

part 'app_router.g.dart';

@riverpod
GoRouter appRouter(Ref ref) {
  final authState = ref.watch(authProvider);
  final isAuthenticated = authState.status == AuthStatus.authenticated;
  final isInitializing = authState.status == AuthStatus.initial;

  return GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterScreen(),
      ),
      // Transaction routes (outside shell)
      GoRoute(
        path: '/add-transaction',
        builder: (context, state) => const AddTransactionScreen(),
      ),
      GoRoute(
        path: '/transaction/:id',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return TransactionDetailScreen(transactionId: id);
        },
      ),
      GoRoute(
        path: '/edit-transaction/:id',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return EditTransactionScreen(transactionId: id);
        },
      ),
      // Filtered transaction routes
      GoRoute(
        path: '/account/:accountId/transactions',
        builder: (context, state) {
          final accountId = state.pathParameters['accountId']!;
          final accountName = state.uri.queryParameters['name'];
          return TransactionsScreen(
            initialFilter: TransactionFilter(accountId: accountId),
            customTitle: accountName != null ? '$accountName Transactions' : 'Account Transactions',
          );
        },
      ),
      GoRoute(
        path: '/category/:categoryId/transactions',
        builder: (context, state) {
          final categoryId = state.pathParameters['categoryId']!;
          final categoryName = state.uri.queryParameters['name'];
          return TransactionsScreen(
            initialFilter: TransactionFilter(categoryId: categoryId),
            customTitle: categoryName != null ? '$categoryName Transactions' : 'Category Transactions',
          );
        },
      ),
      // Shell route with persistent bottom navigation
      ShellRoute(
        builder: (context, state, child) {
          return MainNavigationShell(child: child);
        },
        routes: [
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: '/accounts',
            builder: (context, state) => const AccountsScreen(),
            routes: [
              GoRoute(
                path: '/add',
                builder: (context, state) => const AddAccountScreen(),
              ),
            ],
          ),
          GoRoute(
            path: '/categories',
            builder: (context, state) => const CategoriesScreen(),
            routes: [
              GoRoute(
                path: '/add',
                builder: (context, state) => const AddCategoryScreen(),
              ),
            ],
          ),
          GoRoute(
            path: '/transactions',
            builder: (context, state) => const TransactionsScreen(),
          ),
          GoRoute(
            path: '/reports',
            builder: (context, state) => const ReportsScreen(),
          ),
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),
    ],
    redirect: (context, state) {
      // Handle authentication redirects
      final isLoggingIn = state.matchedLocation == '/login';
      final isRegistering = state.matchedLocation == '/register';
      final isSplash = state.matchedLocation == '/';
      final isAuthScreen = isLoggingIn || isRegistering;

      // Let the splash screen handle initial navigation
      if (isSplash) {
        return null;
      }

      // Don't redirect while initializing auth state
      if (isInitializing) {
        return null;
      }

      // If user is not authenticated and not on auth screen, redirect to login
      if (!isAuthenticated && !isAuthScreen) {
        return '/login';
      }

      // If user is authenticated and on auth screen, redirect to home
      if (isAuthenticated && isAuthScreen) {
        return '/home';
      }

      // No redirect needed
      return null;
    },
    refreshListenable: GoRouterRefreshStream(ref.read(authProvider.notifier).stream),
  );
}

// Helper class to convert Stream to Listenable for GoRouter refreshListenable
class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners();
    _subscription = stream.asBroadcastStream().listen(
          (dynamic _) => notifyListeners(),
        );
  }

  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
