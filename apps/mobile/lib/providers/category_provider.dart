import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:budapp/providers/graphql_provider.dart';
import 'package:budapp/models/category.dart';

part 'category_provider.g.dart';

// GraphQL queries and mutations
const String _getCategoriesQuery = '''
  query GetCategories(\$filter: CategoryFilterInput) {
    categories(filter: \$filter) {
      id
      name
      type
      parentId
      icon
      color
      isDefault
      isSystem
      isArchived
      displayOrder
      createdAt
      updatedAt
    }
  }
''';

const String _createCategoryMutation = '''
  mutation CreateCategory(\$input: CreateCategoryInput!) {
    createCategory(input: \$input) {
      id
      name
      type
      parentId
      icon
      color
      isDefault
      isSystem
      isArchived
      displayOrder
      createdAt
      updatedAt
    }
  }
''';

const String _updateCategoryMutation = '''
  mutation UpdateCategory(\$id: ID!, \$input: UpdateCategoryInput!) {
    updateCategory(id: \$id, input: \$input) {
      id
      name
      type
      parentId
      icon
      color
      isDefault
      isSystem
      isArchived
      displayOrder
      createdAt
      updatedAt
    }
  }
''';

const String _deleteCategoryMutation = '''
  mutation DeleteCategory(\$id: ID!) {
    deleteCategory(id: \$id)
  }
''';

@riverpod
class CategoryNotifier extends _$CategoryNotifier {
  @override
  Future<List<Category>> build() async {
    return await _fetchCategories();
  }

  Future<List<Category>> _fetchCategories({Map<String, dynamic>? filter}) async {
    final client = ref.read(graphQLClientProvider);
    
    final QueryOptions options = QueryOptions(
      document: gql(_getCategoriesQuery),
      variables: filter != null ? {'filter': filter} : {},
      fetchPolicy: FetchPolicy.networkOnly,
    );

    final QueryResult result = await client.query(options);

    if (result.hasException) {
      throw Exception('Failed to fetch categories: ${result.exception}');
    }

    final List<dynamic> categoriesData = result.data?['categories'] ?? [];
    return categoriesData.map((data) => Category.fromJson(data)).toList();
  }

  Future<void> createCategory({
    required String name,
    required String type,
    String? parentId,
    String? icon,
    String? color,
    int displayOrder = 0,
  }) async {
    final client = ref.read(graphQLClientProvider);
    
    final MutationOptions options = MutationOptions(
      document: gql(_createCategoryMutation),
      variables: {
        'input': {
          'name': name,
          'type': type,
          if (parentId != null) 'parentId': parentId,
          if (icon != null) 'icon': icon,
          if (color != null) 'color': color,
          'displayOrder': displayOrder,
        },
      },
    );

    final QueryResult result = await client.mutate(options);

    if (result.hasException) {
      throw Exception('Failed to create category: ${result.exception}');
    }

    // Refresh the categories list
    ref.invalidateSelf();
  }

  Future<void> updateCategory({
    required String id,
    String? name,
    String? type,
    String? parentId,
    String? icon,
    String? color,
    int? displayOrder,
    bool? isArchived,
  }) async {
    final client = ref.read(graphQLClientProvider);
    
    final Map<String, dynamic> input = {};
    if (name != null) input['name'] = name;
    if (type != null) input['type'] = type;
    if (parentId != null) input['parentId'] = parentId;
    if (icon != null) input['icon'] = icon;
    if (color != null) input['color'] = color;
    if (displayOrder != null) input['displayOrder'] = displayOrder;
    if (isArchived != null) input['isArchived'] = isArchived;

    final MutationOptions options = MutationOptions(
      document: gql(_updateCategoryMutation),
      variables: {
        'id': id,
        'input': input,
      },
    );

    final QueryResult result = await client.mutate(options);

    if (result.hasException) {
      throw Exception('Failed to update category: ${result.exception}');
    }

    // Refresh the categories list
    ref.invalidateSelf();
  }

  Future<void> deleteCategory(String id) async {
    final client = ref.read(graphQLClientProvider);
    
    final MutationOptions options = MutationOptions(
      document: gql(_deleteCategoryMutation),
      variables: {'id': id},
    );

    final QueryResult result = await client.mutate(options);

    if (result.hasException) {
      throw Exception('Failed to delete category: ${result.exception}');
    }

    // Refresh the categories list
    ref.invalidateSelf();
  }

  Future<void> refreshCategories() async {
    ref.invalidateSelf();
  }

  Future<List<Category>> getCategoriesByType(String type) async {
    return await _fetchCategories(filter: {'type': type});
  }
}

// Provider for expense categories
@riverpod
Future<List<Category>> expenseCategories(Ref ref) async {
  final categoryNotifier = ref.watch(categoryNotifierProvider.notifier);
  return await categoryNotifier.getCategoriesByType('expense');
}

// Provider for income categories
@riverpod
Future<List<Category>> incomeCategories(Ref ref) async {
  final categoryNotifier = ref.watch(categoryNotifierProvider.notifier);
  return await categoryNotifier.getCategoriesByType('income');
} 