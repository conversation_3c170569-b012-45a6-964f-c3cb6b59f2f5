import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:budapp/models/transaction.dart';
import 'package:budapp/services/transactions_service.dart';
import 'package:budapp/providers/graphql_provider.dart';

part 'transactions_provider.g.dart';

// Transaction service provider
@riverpod
TransactionsService transactionsService(Ref ref) {
  final client = ref.watch(graphQLClientProvider);
  return TransactionsService(client);
}

// Transactions list provider
@riverpod
class Transactions extends _$Transactions {
  @override
  Future<List<Transaction>> build({
    TransactionFilter? filter,
    int limit = 50,
    int offset = 0,
  }) async {
    final service = ref.read(transactionsServiceProvider);
    return service.getTransactions(
      filter: filter,
      limit: limit,
      offset: offset,
    );
  }

  /// Refresh the transactions list
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }

  /// Add a new transaction
  Future<Transaction> addTransaction(CreateTransactionInput input) async {
    final service = ref.read(transactionsServiceProvider);
    final newTransaction = await service.createTransaction(input);
    
    // Refresh the list to include the new transaction
    await refresh();
    
    return newTransaction;
  }

  /// Update an existing transaction
  Future<Transaction> updateTransaction(
    String id,
    UpdateTransactionInput input,
  ) async {
    final service = ref.read(transactionsServiceProvider);
    final updatedTransaction = await service.updateTransaction(id, input);
    
    // Refresh the list to reflect the changes
    await refresh();
    
    return updatedTransaction;
  }

  /// Delete a transaction
  Future<bool> deleteTransaction(String id) async {
    final service = ref.read(transactionsServiceProvider);
    final success = await service.deleteTransaction(id);
    
    if (success) {
      // Refresh the list to remove the deleted transaction
      await refresh();
    }
    
    return success;
  }
}

// Individual transaction provider
@riverpod
class TransactionDetail extends _$TransactionDetail {
  @override
  Future<Transaction?> build(String id) async {
    final service = ref.read(transactionsServiceProvider);
    return service.getTransaction(id);
  }

  /// Refresh the transaction details
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build(id));
  }
}

// Recent transactions provider (for dashboard/home screen)
@riverpod
Future<List<Transaction>> recentTransactions(Ref ref) async {
  final service = ref.read(transactionsServiceProvider);
  return service.getTransactions(limit: 10);
}

// Transaction statistics provider
@riverpod
class TransactionStats extends _$TransactionStats {
  @override
  Future<TransactionStatistics> build({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final service = ref.read(transactionsServiceProvider);
    
    // Get transactions for the specified period
    final filter = TransactionFilter(
      startDate: startDate,
      endDate: endDate,
    );
    
    final transactions = await service.getTransactions(
      filter: filter,
      limit: 1000, // Get all transactions for stats
    );
    
    return _calculateStatistics(transactions);
  }

  /// Refresh the statistics
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }

  TransactionStatistics _calculateStatistics(List<Transaction> transactions) {
    double totalIncome = 0;
    double totalExpenses = 0;
    int transactionCount = transactions.length;
    
    for (final transaction in transactions) {
      switch (transaction.type) {
        case TransactionType.income:
          totalIncome += transaction.amount;
          break;
        case TransactionType.expense:
          totalExpenses += transaction.amount;
          break;
        case TransactionType.transfer:
          // Transfers don't affect income/expense totals
          break;
      }
    }
    
    return TransactionStatistics(
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netAmount: totalIncome - totalExpenses,
      transactionCount: transactionCount,
    );
  }
}

// Transaction statistics model
class TransactionStatistics {
  final double totalIncome;
  final double totalExpenses;
  final double netAmount;
  final int transactionCount;

  const TransactionStatistics({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netAmount,
    required this.transactionCount,
  });
}

// Filtered transactions provider
@riverpod
class FilteredTransactions extends _$FilteredTransactions {
  @override
  Future<List<Transaction>> build(TransactionFilter filter) async {
    final service = ref.read(transactionsServiceProvider);
    return service.getTransactions(filter: filter);
  }

  /// Update the filter and refresh
  Future<void> updateFilter(TransactionFilter newFilter) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build(newFilter));
  }
} 