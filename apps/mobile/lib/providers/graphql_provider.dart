import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:budapp/providers/auth_provider.dart';
import 'package:budapp/config/environment.dart';

part 'graphql_provider.g.dart';

@riverpod
GraphQLClient graphQLClient(Ref ref) {
  final secureStorage = ref.read(secureStorageServiceProvider);

  // Print environment info in debug mode
  if (kDebugMode) {
    Environment.printEnvironmentInfo();
  }

  final HttpLink httpLink = HttpLink(Environment.apiUrl);

  final AuthLink authLink = AuthLink(
    getToken: () async {
      final token = await secureStorage.getToken();
      if (token != null) {
        return 'Bearer $token';
      }
      return null;
    },
  );

  final ErrorLink errorLink = ErrorLink(
    onGraphQLError: (request, forward, response) {
      // Handle GraphQL errors
      if (kDebugMode) {
        print('GraphQL Error: ${response.errors}');

        // Print detailed error information for debugging
        for (final error in response.errors ?? []) {
          print('  Message: ${error.message}');
          print('  Path: ${error.path}');
          print('  Extensions: ${error.extensions}');
          print('  Locations: ${error.locations}');
        }
      }

      // Check for authentication errors
      final hasAuthError = response.errors?.any((error) =>
        error.extensions?['code'] == 'UNAUTHORIZED' ||
        error.extensions?['code'] == 'INVALID_TOKEN' ||
        error.extensions?['code'] == 'TOKEN_EXPIRED'
      ) ?? false;

      if (hasAuthError) {
        // TODO: Handle token refresh or logout if needed
      }

      return forward(request);
    },
    onException: (request, forward, exception) {
      // Handle network exceptions
      if (kDebugMode) {
        print('Network Exception: $exception');
        print('  Request: ${request.operation.operationName}');
        // Note: variables are not directly accessible in this version of gql_exec
      }

      // Add network error information to the response
      final errorExtensions = {
        'code': 'NETWORK_ERROR',
        'details': exception.toString(),
      };

      // Create a custom error response
      final customError = GraphQLError(
        message: 'Network error: Please check your connection',
        extensions: errorExtensions,
      );

      // Return a custom response with the network error
      final customResponse = Response(
        data: null,
        errors: [customError],
        response: const {},
        context: const Context(),
      );

      return Stream.value(customResponse);
    },
  );

  final Link link = errorLink.concat(authLink).concat(httpLink);

  return GraphQLClient(
    link: link,
    cache: GraphQLCache(
      store: InMemoryStore(),
      // Disable normalization to avoid PartialDataException issues
      // This is safer for complex nested data structures
      dataIdFromObject: (object) => null,
    ),
    defaultPolicies: DefaultPolicies(
      query: Policies(
        fetch: FetchPolicy.cacheAndNetwork,
        error: ErrorPolicy.all,
      ),
      mutate: Policies(
        fetch: FetchPolicy.networkOnly,
        error: ErrorPolicy.all,
      ),
      subscribe: Policies(
        fetch: FetchPolicy.cacheAndNetwork,
        error: ErrorPolicy.all,
      ),
    ),
  );
}

@riverpod
ValueNotifier<GraphQLClient> graphQLClientNotifier(Ref ref) {
  final client = ref.watch(graphQLClientProvider);
  return ValueNotifier<GraphQLClient>(client);
}

/// Clear the GraphQL cache to resolve cache-related issues
@riverpod
Future<void> clearGraphQLCache(Ref ref) async {
  final client = ref.read(graphQLClientProvider);
  client.cache.store.reset();
}
