import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:budapp/models/user.dart';
import 'package:budapp/services/auth_service.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:budapp/providers/graphql_provider.dart';

part 'auth_provider.g.dart';

enum AuthStatus {
  initial,
  unauthenticated,
  authenticating,
  authenticated,
  error,
}

@immutable
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? errorMessage;

  const AuthState({
    required this.status,
    this.user,
    this.errorMessage,
  });

  const AuthState.initial()
      : status = AuthStatus.initial,
        user = null,
        errorMessage = null;

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage,
    );
  }
}

@riverpod
class Auth extends _$Auth {
  late AuthService _authService;
  final StreamController<AuthState> _controller = StreamController<AuthState>.broadcast();

  // Expose a stream of auth state changes
  Stream<AuthState> get stream => _controller.stream;

  @override
  AuthState build() {
    final secureStorage = ref.read(secureStorageServiceProvider);
    final client = ref.read(graphQLClientProvider);
    _authService = AuthService(secureStorage: secureStorage, client: client);

    _initializeAuthState();

    ref.onDispose(() {
      _controller.close();
    });

    return const AuthState.initial();
  }

  @override
  set state(AuthState value) {
    super.state = value;
    if (!_controller.isClosed) {
      _controller.add(value);
    }
  }

  Future<void> _initializeAuthState() async {
    try {
      // Simulate a short delay to ensure the splash screen is shown
      await Future.delayed(const Duration(milliseconds: 500));

      final isLoggedIn = await _authService.isLoggedIn();

      if (isLoggedIn) {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          state = AuthState(status: AuthStatus.authenticated, user: user);
        } else {
          state = const AuthState(status: AuthStatus.unauthenticated);
        }
      } else {
        state = const AuthState(status: AuthStatus.unauthenticated);
      }
    } catch (e) {
      state = AuthState(
        status: AuthStatus.error,
        errorMessage: 'Failed to initialize auth state: $e',
      );
    }
  }

  Future<void> register({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) async {
    try {
      state = state.copyWith(status: AuthStatus.authenticating);

      final authPayload = await _authService.register(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
      );

      state = AuthState(
        status: AuthStatus.authenticated,
        user: authPayload.user,
      );
    } catch (e) {
      state = AuthState(
        status: AuthStatus.error,
        errorMessage: 'Registration failed: $e',
      );
    }
  }

  Future<void> login({
    required String email,
    required String password,
  }) async {
    try {
      state = state.copyWith(status: AuthStatus.authenticating);

      final authPayload = await _authService.login(
        email: email,
        password: password,
      );

      state = AuthState(
        status: AuthStatus.authenticated,
        user: authPayload.user,
      );
    } catch (e) {
      state = AuthState(
        status: AuthStatus.error,
        errorMessage: 'Login failed: $e',
      );
    }
  }

  Future<void> loginWithGoogle() async {
    try {
      state = state.copyWith(status: AuthStatus.authenticating);

      final authPayload = await _authService.loginWithGoogle();

      state = AuthState(
        status: AuthStatus.authenticated,
        user: authPayload.user,
      );
    } catch (e) {
      state = AuthState(
        status: AuthStatus.error,
        errorMessage: 'Google login failed: $e',
      );
    }
  }

  Future<void> logout() async {
    try {
      await _authService.logout();
      state = const AuthState(status: AuthStatus.unauthenticated);
    } catch (e) {
      state = AuthState(
        status: AuthStatus.error,
        errorMessage: 'Logout failed: $e',
      );
    }
  }

  Future<void> refreshToken() async {
    try {
      final authPayload = await _authService.refreshToken();

      state = AuthState(
        status: AuthStatus.authenticated,
        user: authPayload.user,
      );
    } catch (e) {
      // If token refresh fails, log the user out
      await logout();
    }
  }

  Future<String?> getToken() async {
    return await _authService.getToken();
  }
}

@riverpod
SecureStorageService secureStorageService(Ref ref) {
  return SecureStorageService();
}
