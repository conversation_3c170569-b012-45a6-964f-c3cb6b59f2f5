import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/services/accounts_service.dart';
import 'package:budapp/providers/graphql_provider.dart';

part 'accounts_provider.g.dart';

enum AccountsStatus {
  initial,
  loading,
  loaded,
  error,
}

@immutable
class AccountsState {
  final AccountsStatus status;
  final List<Account> accounts;
  final String? errorMessage;

  const AccountsState({
    required this.status,
    required this.accounts,
    this.errorMessage,
  });

  const AccountsState.initial()
      : status = AccountsStatus.initial,
        accounts = const [],
        errorMessage = null;

  AccountsState copyWith({
    AccountsStatus? status,
    List<Account>? accounts,
    String? errorMessage,
  }) {
    return AccountsState(
      status: status ?? this.status,
      accounts: accounts ?? this.accounts,
      errorMessage: errorMessage,
    );
  }

  // Helper getters for calculated values
  double get totalAssets {
    return accounts
        .where((account) => 
            account.includeInNetWorth && 
            !account.isArchived &&
            account.currentBalance >= 0)
        .fold(0.0, (sum, account) => sum + account.currentBalance);
  }

  double get totalLiabilities {
    return accounts
        .where((account) => 
            account.includeInNetWorth && 
            !account.isArchived &&
            account.currentBalance < 0)
        .fold(0.0, (sum, account) => sum + account.currentBalance.abs());
  }

  double get netWorth => totalAssets - totalLiabilities;

  List<Account> get bankAccounts {
    return accounts
        .where((account) => 
            !account.isArchived &&
            (account.type == AccountType.checking || account.type == AccountType.savings))
        .toList()
        ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
  }

  List<Account> get creditCards {
    return accounts
        .where((account) => 
            !account.isArchived &&
            account.type == AccountType.creditCard)
        .toList()
        ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
  }

  List<Account> get investmentAccounts {
    return accounts
        .where((account) => 
            !account.isArchived &&
            account.type == AccountType.investment)
        .toList()
        ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
  }
}

@riverpod
class Accounts extends _$Accounts {
  late AccountsService _accountsService;
  final StreamController<AccountsState> _controller = StreamController<AccountsState>.broadcast();

  // Expose a stream of accounts state changes
  Stream<AccountsState> get stream => _controller.stream;

  @override
  AccountsState build() {
    final client = ref.read(graphQLClientProvider);
    _accountsService = AccountsService(client: client);

    ref.onDispose(() {
      _controller.close();
    });

    return const AccountsState.initial();
  }

  @override
  set state(AccountsState value) {
    super.state = value;
    if (!_controller.isClosed) {
      _controller.add(value);
    }
  }

  Future<void> loadAccounts() async {
    try {
      state = state.copyWith(status: AccountsStatus.loading);

      final accounts = await _accountsService.getAccounts();

      state = AccountsState(
        status: AccountsStatus.loaded,
        accounts: accounts,
      );
    } catch (e) {
      state = AccountsState(
        status: AccountsStatus.error,
        accounts: state.accounts,
        errorMessage: 'Failed to load accounts: $e',
      );
    }
  }

  Future<Account?> getAccount(String id) async {
    try {
      return await _accountsService.getAccount(id);
    } catch (e) {
      state = state.copyWith(
        status: AccountsStatus.error,
        errorMessage: 'Failed to load account: $e',
      );
      return null;
    }
  }

  Future<Account?> createAccount(CreateAccountInput input) async {
    try {
      final account = await _accountsService.createAccount(input);

      // Add the new account to the current list
      final updatedAccounts = [...state.accounts, account];
      state = state.copyWith(
        accounts: updatedAccounts,
        status: AccountsStatus.loaded,
      );

      return account;
    } catch (e) {
      state = state.copyWith(
        status: AccountsStatus.error,
        errorMessage: 'Failed to create account: $e',
      );
      return null;
    }
  }

  Future<Account?> updateAccount(String id, UpdateAccountInput input) async {
    try {
      final updatedAccount = await _accountsService.updateAccount(id, input);

      // Update the account in the current list
      final updatedAccounts = state.accounts.map((account) {
        return account.id == id ? updatedAccount : account;
      }).toList();

      state = state.copyWith(
        accounts: updatedAccounts,
        status: AccountsStatus.loaded,
      );

      return updatedAccount;
    } catch (e) {
      state = state.copyWith(
        status: AccountsStatus.error,
        errorMessage: 'Failed to update account: $e',
      );
      return null;
    }
  }

  Future<bool> deleteAccount(String id) async {
    try {
      final success = await _accountsService.deleteAccount(id);

      if (success) {
        // Remove the account from the current list
        final updatedAccounts = state.accounts.where((account) => account.id != id).toList();
        state = state.copyWith(
          accounts: updatedAccounts,
          status: AccountsStatus.loaded,
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        status: AccountsStatus.error,
        errorMessage: 'Failed to delete account: $e',
      );
      return false;
    }
  }

  Future<bool> archiveAccount(String id) async {
    final result = await updateAccount(id, const UpdateAccountInput(isArchived: true));
    return result != null;
  }

  Future<bool> unarchiveAccount(String id) async {
    final result = await updateAccount(id, const UpdateAccountInput(isArchived: false));
    return result != null;
  }

  void clearError() {
    if (state.status == AccountsStatus.error) {
      state = state.copyWith(
        status: AccountsStatus.loaded,
        errorMessage: null,
      );
    }
  }

  // Add refresh method for compatibility
  Future<void> refresh() async {
    await loadAccounts();
  }
}

// Simple provider that returns AsyncValue<List<Account>> for compatibility with existing screens
@riverpod
Future<List<Account>> accountsList(Ref ref) async {
  // Get the GraphQL client directly
  final client = ref.read(graphQLClientProvider);
  final accountsService = AccountsService(client: client);
  
  try {
    // Fetch accounts directly
    final accounts = await accountsService.getAccounts();
    return accounts;
  } catch (e) {
    throw Exception('Failed to load accounts: $e');
  }
} 