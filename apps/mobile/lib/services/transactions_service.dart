import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:budapp/models/transaction.dart';

class TransactionsService {
  /// Defensive helper to ensure required fields are present and journalLines is non-null.
  /// Throws if any required field is missing/null. Defaults journalLines to [] if missing/null.
  Map<String, dynamic> _validateTransactionJson(Map<String, dynamic> json) {
    final requiredFields = [
      'id',
      'userId',
      'amount',
      'type',
      'date',
      'status',
      'isRecurring',
      'createdAt',
      'updatedAt',
    ];
    for (final key in requiredFields) {
      if (!json.containsKey(key) || json[key] == null) {
        throw Exception(
            "Transaction API error: Required field '$key' is missing or null.");
      }
    }
    if (!json.containsKey('journalLines') || json['journalLines'] == null) {
      json['journalLines'] = [];
    }
    return json;
  }
  final GraphQLClient _client;

  TransactionsService(this._client);

  // GraphQL queries and mutations
  static const String _getTransactionsQuery = r'''
    query GetTransactions($filter: TransactionFilterInput, $limit: Int, $offset: Int) {
      transactions(filter: $filter, limit: $limit, offset: $offset) {
        id
        userId
        description
        amount
        type
        date
        notes
        status
        isRecurring
        recurringPattern {
          type
          interval
          dayOfWeek
          dayOfMonth
          monthOfYear
          endDate
          occurrences
        }
        createdAt
        updatedAt
        journalLines {
          id
          journalEntryId
          accountId
          categoryId
          amount
          type
          notes
          createdAt
          updatedAt
          account {
            id
            name
            type
          }
          category {
            id
            name
            type
          }
        }
      }
    }
  ''';

  static const String _getTransactionQuery = r'''
    query GetTransaction($id: ID!) {
      transaction(id: $id) {
        id
        userId
        description
        amount
        type
        date
        notes
        status
        isRecurring
        recurringPattern {
          type
          interval
          dayOfWeek
          dayOfMonth
          monthOfYear
          endDate
          occurrences
        }
        createdAt
        updatedAt
        journalLines {
          id
          journalEntryId
          accountId
          categoryId
          amount
          type
          notes
          createdAt
          updatedAt
          account {
            id
            name
            type
          }
          category {
            id
            name
            type
          }
        }
      }
    }
  ''';

  static const String _createTransactionMutation = r'''
    mutation CreateTransaction($input: CreateTransactionInput!) {
      createTransaction(input: $input) {
        id
        userId
        description
        amount
        type
        date
        notes
        status
        isRecurring
        recurringPattern {
          type
          interval
          dayOfWeek
          dayOfMonth
          monthOfYear
          endDate
          occurrences
        }
        createdAt
        updatedAt
        journalLines {
          id
          journalEntryId
          accountId
          categoryId
          amount
          type
          notes
          createdAt
          updatedAt
          account {
            id
            name
            type
          }
          category {
            id
            name
            type
          }
        }
      }
    }
  ''';

  static const String _updateTransactionMutation = r'''
    mutation UpdateTransaction($id: ID!, $input: UpdateTransactionInput!) {
      updateTransaction(id: $id, input: $input) {
        id
        userId
        description
        amount
        type
        date
        notes
        status
        isRecurring
        recurringPattern {
          type
          interval
          dayOfWeek
          dayOfMonth
          monthOfYear
          endDate
          occurrences
        }
        createdAt
        updatedAt
        journalLines {
          id
          journalEntryId
          accountId
          categoryId
          amount
          type
          notes
          createdAt
          updatedAt
          account {
            id
            name
            type
          }
          category {
            id
            name
            type
          }
        }
      }
    }
  ''';

  static const String _deleteTransactionMutation = r'''
    mutation DeleteTransaction($id: ID!) {
      deleteTransaction(id: $id)
    }
  ''';

  /// Get all transactions for the current user
  Future<List<Transaction>> getTransactions({
    TransactionFilter? filter,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final result = await _client.query(
        QueryOptions(
          document: gql(_getTransactionsQuery),
          variables: {
            'filter': filter?.toJson(),
            'limit': limit,
            'offset': offset,
          },
          fetchPolicy: FetchPolicy.networkOnly, // Force network to avoid cache issues
          errorPolicy: ErrorPolicy.all,
        ),
      );

      if (result.hasException) {
        // Check for PartialDataException specifically
        final exception = result.exception;
        if (exception.toString().contains('PartialDataException')) {
          // Retry with network-only policy
          final retryResult = await _client.query(
            QueryOptions(
              document: gql(_getTransactionsQuery),
              variables: {
                'filter': filter?.toJson(),
                'limit': limit,
                'offset': offset,
              },
              fetchPolicy: FetchPolicy.networkOnly,
              errorPolicy: ErrorPolicy.all,
            ),
          );

          if (retryResult.hasException) {
            throw Exception('Failed to fetch transactions after retry: ${retryResult.exception}');
          }

          final List<dynamic> transactionsData = retryResult.data?['transactions'] ?? [];
          return transactionsData
              .map((data) => Transaction.fromJson(
                    _validateTransactionJson(data as Map<String, dynamic>)))
              .toList();
        }

        throw Exception('Failed to fetch transactions: ${result.exception}');
      }

      final List<dynamic> transactionsData = result.data?['transactions'] ?? [];
      return transactionsData
          .map((data) => Transaction.fromJson(
                _validateTransactionJson(data as Map<String, dynamic>)))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch transactions: $e');
    }
  }

  /// Get a specific transaction by ID
  Future<Transaction?> getTransaction(String id) async {
    try {
      final result = await _client.query(
        QueryOptions(
          document: gql(_getTransactionQuery),
          variables: {'id': id},
          fetchPolicy: FetchPolicy.networkOnly, // Force network to avoid cache issues
          errorPolicy: ErrorPolicy.all,
        ),
      );

      if (result.hasException) {
        // Check for PartialDataException specifically
        final exception = result.exception;
        if (exception.toString().contains('PartialDataException')) {
          // Retry with network-only policy
          final retryResult = await _client.query(
            QueryOptions(
              document: gql(_getTransactionQuery),
              variables: {'id': id},
              fetchPolicy: FetchPolicy.networkOnly,
              errorPolicy: ErrorPolicy.all,
            ),
          );

          if (retryResult.hasException) {
            throw Exception('Failed to fetch transaction after retry: ${retryResult.exception}');
          }

          final transactionData = retryResult.data?['transaction'];
          if (transactionData == null) return null;

          return Transaction.fromJson(
              _validateTransactionJson(transactionData as Map<String, dynamic>));
        }

        throw Exception('Failed to fetch transaction: ${result.exception}');
      }

      final transactionData = result.data?['transaction'];
      if (transactionData == null) return null;

      return Transaction.fromJson(
          _validateTransactionJson(transactionData as Map<String, dynamic>));
    } catch (e) {
      throw Exception('Failed to fetch transaction: $e');
    }
  }

  /// Create a new transaction
  Future<Transaction> createTransaction(CreateTransactionInput input) async {
    try {
      final result = await _client.mutate(
        MutationOptions(
          document: gql(_createTransactionMutation),
          variables: {'input': input.toJson()},
        ),
      );

      if (result.hasException) {
        throw Exception('Failed to create transaction: ${result.exception}');
      }

      final transactionData = result.data?['createTransaction'];
      if (transactionData == null) {
        throw Exception('No transaction data returned');
      }

      return Transaction.fromJson(
          _validateTransactionJson(transactionData as Map<String, dynamic>));
    } catch (e) {
      throw Exception('Failed to create transaction: $e');
    }
  }

  /// Update an existing transaction
  Future<Transaction> updateTransaction(
    String id,
    UpdateTransactionInput input,
  ) async {
    try {
      final result = await _client.mutate(
        MutationOptions(
          document: gql(_updateTransactionMutation),
          variables: {
            'id': id,
            'input': input.toJson(),
          },
        ),
      );

      if (result.hasException) {
        throw Exception('Failed to update transaction: ${result.exception}');
      }

      final transactionData = result.data?['updateTransaction'];
      if (transactionData == null) {
        throw Exception('No transaction data returned');
      }

      return Transaction.fromJson(
          _validateTransactionJson(transactionData as Map<String, dynamic>));
    } catch (e) {
      throw Exception('Failed to update transaction: $e');
    }
  }

  /// Delete a transaction
  Future<bool> deleteTransaction(String id) async {
    try {
      final result = await _client.mutate(
        MutationOptions(
          document: gql(_deleteTransactionMutation),
          variables: {'id': id},
        ),
      );

      if (result.hasException) {
        throw Exception('Failed to delete transaction: ${result.exception}');
      }

      return result.data?['deleteTransaction'] == true;
    } catch (e) {
      throw Exception('Failed to delete transaction: $e');
    }
  }
} 