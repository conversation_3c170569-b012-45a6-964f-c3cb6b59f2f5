import 'dart:convert';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:budapp/models/user.dart';
import 'package:budapp/services/secure_storage_service.dart';

class AuthService {
  final SecureStorageService _secureStorage;
  final GraphQLClient _client;
  final FlutterAppAuth _appAuth = const FlutterAppAuth();

  // OAuth configuration
  static const String _googleClientId = 'YOUR_GOOGLE_CLIENT_ID'; // Replace with actual client ID in production
  static const String _redirectUrl = 'com.example.budapp://oauth2redirect';
  static const List<String> _scopes = ['email', 'profile'];

  AuthService({
    required SecureStorageService secureStorage,
    required GraphQLClient client,
  })  : _secureStorage = secureStorage,
        _client = client;

  // Register a new user
  Future<AuthPayload> register({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) async {
    const String mutation = r'''
      mutation Register($input: RegisterInput!) {
        register(input: $input) {
          token
          user {
            id
            email
            firstName
            lastName
            role
            emailVerified
            createdAt
            updatedAt
          }
        }
      }
    ''';

    final MutationOptions options = MutationOptions(
      document: gql(mutation),
      variables: {
        'input': {
          'email': email,
          'password': password,
          'firstName': firstName,
          'lastName': lastName,
        },
      },
    );

    final QueryResult result = await _client.mutate(options);

    if (result.hasException) {
      final errorMessage = _formatGraphQLError(result.exception, 'Registration failed');
      throw Exception(errorMessage);
    }

    final AuthPayload authPayload = AuthPayload.fromJson(result.data!['register']);

    // Save token and user data
    await _secureStorage.saveToken(authPayload.token);
    await _secureStorage.saveUserData(jsonEncode(authPayload.user.toJson()));

    return authPayload;
  }

  // Login with email and password
  Future<AuthPayload> login({
    required String email,
    required String password,
  }) async {
    const String mutation = r'''
      mutation Login($input: LoginInput!) {
        login(input: $input) {
          token
          user {
            id
            email
            firstName
            lastName
            role
            emailVerified
            createdAt
            updatedAt
          }
        }
      }
    ''';

    final MutationOptions options = MutationOptions(
      document: gql(mutation),
      variables: {
        'input': {
          'email': email,
          'password': password,
        },
      },
    );

    final QueryResult result = await _client.mutate(options);

    if (result.hasException) {
      final errorMessage = _formatGraphQLError(result.exception, 'Login failed');
      throw Exception(errorMessage);
    }

    final AuthPayload authPayload = AuthPayload.fromJson(result.data!['login']);

    // Save token and user data
    await _secureStorage.saveToken(authPayload.token);
    await _secureStorage.saveUserData(jsonEncode(authPayload.user.toJson()));

    return authPayload;
  }

  // Login with Google
  Future<AuthPayload> loginWithGoogle() async {
    try {
      // Initiate Google OAuth flow
      final AuthorizationTokenResponse result = await _appAuth.authorizeAndExchangeCode(
        AuthorizationTokenRequest(
          _googleClientId,
          _redirectUrl,
          serviceConfiguration: const AuthorizationServiceConfiguration(
            authorizationEndpoint: 'https://accounts.google.com/o/oauth2/auth',
            tokenEndpoint: 'https://oauth2.googleapis.com/token',
          ),
          scopes: _scopes,
        ),
      );

      if (result.accessToken == null) {
        throw Exception('Google login failed');
      }

      // Exchange Google token for our app token
      // This would typically be a call to your backend API
      // For now, we'll simulate this with a GraphQL mutation

      // TODO: Implement actual Google token exchange with backend

      // Placeholder for demonstration
      const String mutation = r'''
        mutation GoogleLogin($token: String!) {
          googleLogin(token: $token) {
            token
            user {
              id
              email
              firstName
              lastName
              role
              emailVerified
              createdAt
              updatedAt
            }
          }
        }
      ''';

      final MutationOptions options = MutationOptions(
        document: gql(mutation),
        variables: {
          'token': result.accessToken,
        },
      );

      final QueryResult graphqlResult = await _client.mutate(options);

      if (graphqlResult.hasException) {
        final errorMessage = _formatGraphQLError(graphqlResult.exception, 'Google login failed');
        throw Exception(errorMessage);
      }

      final AuthPayload authPayload = AuthPayload.fromJson(graphqlResult.data!['googleLogin']);

      // Save token and user data
      await _secureStorage.saveToken(authPayload.token);
      await _secureStorage.saveUserData(jsonEncode(authPayload.user.toJson()));

      return authPayload;
    } catch (e) {
      throw Exception('Google login failed: $e');
    }
  }

  // Refresh token
  Future<AuthPayload> refreshToken() async {
    const String mutation = r'''
      mutation RefreshToken {
        refreshToken {
          token
          user {
            id
            email
            firstName
            lastName
            role
            emailVerified
            createdAt
            updatedAt
          }
        }
      }
    ''';

    final MutationOptions options = MutationOptions(
      document: gql(mutation),
    );

    final QueryResult result = await _client.mutate(options);

    if (result.hasException) {
      final errorMessage = _formatGraphQLError(result.exception, 'Token refresh failed');
      throw Exception(errorMessage);
    }

    final AuthPayload authPayload = AuthPayload.fromJson(result.data!['refreshToken']);

    // Save new token
    await _secureStorage.saveToken(authPayload.token);

    return authPayload;
  }

  // Logout
  Future<void> logout() async {
    const String mutation = r'''
      mutation Logout {
        logout
      }
    ''';

    final MutationOptions options = MutationOptions(
      document: gql(mutation),
    );

    await _client.mutate(options);

    // Clear stored data
    await _secureStorage.clearAll();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _secureStorage.getToken();
    return token != null && token.isNotEmpty;
  }

  // Get current user
  Future<User?> getCurrentUser() async {
    final userData = await _secureStorage.getUserData();
    if (userData == null) return null;

    return User.fromJson(jsonDecode(userData));
  }

  // Get current token
  Future<String?> getToken() async {
    return await _secureStorage.getToken();
  }

  // Helper method to format GraphQL errors
  String _formatGraphQLError(OperationException? exception, String fallbackMessage) {
    if (exception == null) {
      return fallbackMessage;
    }

    // Check for network errors first
    if (exception.linkException != null) {
      return 'Network error: Please check your connection and try again';
    }

    // Handle GraphQL errors
    if (exception.graphqlErrors.isNotEmpty) {
      final error = exception.graphqlErrors.first;

      // Check for error code in extensions
      final errorCode = error.extensions?['code'] as String?;
      if (errorCode != null) {
        switch (errorCode) {
          case 'INVALID_CREDENTIALS':
            return 'Invalid email or password. Please try again.';
          case 'EMAIL_ALREADY_EXISTS':
            return 'An account with this email already exists.';
          case 'WEAK_PASSWORD':
            return 'Password is too weak. Please use a stronger password.';
          case 'TOKEN_EXPIRED':
            return 'Your session has expired. Please log in again.';
          case 'UNAUTHORIZED':
            return 'You are not authorized to perform this action.';
          case 'USER_NOT_FOUND':
            return 'User not found. Please check your credentials.';
          case 'NETWORK_ERROR':
            return 'Network error. Please check your connection and try again.';
          case 'DATABASE_ERROR':
            return 'Server error. Please try again later.';
        }
      }

      // Return the original error message if available
      return error.message;
    }

    // Fallback to default message
    return fallbackMessage;
  }
}
