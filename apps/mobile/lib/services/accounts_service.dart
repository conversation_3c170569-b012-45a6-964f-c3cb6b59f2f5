import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:budapp/models/account.dart';

class AccountsService {
  final GraphQLClient client;

  AccountsService({required this.client});

  static const String _getAccountsQuery = '''
    query GetAccounts {
      accounts {
        id
        userId
        name
        type
        currency
        initialBalance
        currentBalance
        isArchived
        notes
        icon
        color
        includeInNetWorth
        displayOrder
        createdAt
        updatedAt
      }
    }
  ''';

  static const String _getAccountQuery = '''
    query GetAccount(\$id: ID!) {
      account(id: \$id) {
        id
        userId
        name
        type
        currency
        initialBalance
        currentBalance
        isArchived
        notes
        icon
        color
        includeInNetWorth
        displayOrder
        createdAt
        updatedAt
      }
    }
  ''';

  static const String _createAccountMutation = '''
    mutation CreateAccount(\$input: CreateAccountInput!) {
      createAccount(input: \$input) {
        id
        userId
        name
        type
        currency
        initialBalance
        currentBalance
        isArchived
        notes
        icon
        color
        includeInNetWorth
        displayOrder
        createdAt
        updatedAt
      }
    }
  ''';

  static const String _updateAccountMutation = '''
    mutation UpdateAccount(\$id: ID!, \$input: UpdateAccountInput!) {
      updateAccount(id: \$id, input: \$input) {
        id
        userId
        name
        type
        currency
        initialBalance
        currentBalance
        isArchived
        notes
        icon
        color
        includeInNetWorth
        displayOrder
        createdAt
        updatedAt
      }
    }
  ''';

  static const String _deleteAccountMutation = '''
    mutation DeleteAccount(\$id: ID!) {
      deleteAccount(id: \$id)
    }
  ''';

  Future<List<Account>> getAccounts() async {
    final QueryOptions options = QueryOptions(
      document: gql(_getAccountsQuery),
      fetchPolicy: FetchPolicy.networkOnly,
    );

    final QueryResult result = await client.query(options);

    if (result.hasException) {
      throw Exception('Failed to fetch accounts: ${result.exception}');
    }

    final List<dynamic> accountsData = result.data?['accounts'] ?? [];
    return accountsData.map((data) => _parseAccount(data)).toList();
  }

  Future<Account?> getAccount(String id) async {
    final QueryOptions options = QueryOptions(
      document: gql(_getAccountQuery),
      variables: {'id': id},
      fetchPolicy: FetchPolicy.networkOnly,
    );

    final QueryResult result = await client.query(options);

    if (result.hasException) {
      throw Exception('Failed to fetch account: ${result.exception}');
    }

    final dynamic accountData = result.data?['account'];
    if (accountData == null) return null;

    return _parseAccount(accountData);
  }

  Future<Account> createAccount(CreateAccountInput input) async {
    final MutationOptions options = MutationOptions(
      document: gql(_createAccountMutation),
      variables: {
        'input': {
          'name': input.name,
          'type': _accountTypeToGraphQL(input.type),
          'currency': input.currency,
          'initialBalance': input.initialBalance,
          'notes': input.notes,
          'icon': input.icon,
          'color': input.color,
          'includeInNetWorth': input.includeInNetWorth,
          'displayOrder': input.displayOrder,
        }
      },
    );

    final QueryResult result = await client.mutate(options);

    if (result.hasException) {
      throw Exception('Failed to create account: ${result.exception}');
    }

    final dynamic accountData = result.data?['createAccount'];
    if (accountData == null) {
      throw Exception('No account data returned from create mutation');
    }

    return _parseAccount(accountData);
  }

  Future<Account> updateAccount(String id, UpdateAccountInput input) async {
    final Map<String, dynamic> variables = {'id': id, 'input': <String, dynamic>{}};
    
    if (input.name != null) variables['input']['name'] = input.name;
    if (input.type != null) variables['input']['type'] = _accountTypeToGraphQL(input.type!);
    if (input.currency != null) variables['input']['currency'] = input.currency;
    if (input.notes != null) variables['input']['notes'] = input.notes;
    if (input.icon != null) variables['input']['icon'] = input.icon;
    if (input.color != null) variables['input']['color'] = input.color;
    if (input.includeInNetWorth != null) variables['input']['includeInNetWorth'] = input.includeInNetWorth;
    if (input.displayOrder != null) variables['input']['displayOrder'] = input.displayOrder;
    if (input.isArchived != null) variables['input']['isArchived'] = input.isArchived;

    final MutationOptions options = MutationOptions(
      document: gql(_updateAccountMutation),
      variables: variables,
    );

    final QueryResult result = await client.mutate(options);

    if (result.hasException) {
      throw Exception('Failed to update account: ${result.exception}');
    }

    final dynamic accountData = result.data?['updateAccount'];
    if (accountData == null) {
      throw Exception('No account data returned from update mutation');
    }

    return _parseAccount(accountData);
  }

  Future<bool> deleteAccount(String id) async {
    final MutationOptions options = MutationOptions(
      document: gql(_deleteAccountMutation),
      variables: {'id': id},
    );

    final QueryResult result = await client.mutate(options);

    if (result.hasException) {
      throw Exception('Failed to delete account: ${result.exception}');
    }

    return result.data?['deleteAccount'] ?? false;
  }

  Account _parseAccount(Map<String, dynamic> data) {
    return Account(
      id: data['id'],
      userId: data['userId'],
      name: data['name'],
      type: _parseAccountType(data['type']),
      currency: data['currency'],
      initialBalance: (data['initialBalance'] as num).toDouble(),
      currentBalance: (data['currentBalance'] as num).toDouble(),
      isArchived: data['isArchived'],
      notes: data['notes'],
      icon: data['icon'],
      color: data['color'],
      includeInNetWorth: data['includeInNetWorth'],
      displayOrder: data['displayOrder'],
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
    );
  }

  DateTime _parseDateTime(dynamic dateValue) {
    if (dateValue == null) {
      return DateTime.now();
    }
    
    if (dateValue is String) {
      // Try parsing as ISO string first
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        // If that fails, try parsing as timestamp
        final timestamp = int.tryParse(dateValue);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
        // Fallback to current time
        return DateTime.now();
      }
    }
    
    if (dateValue is int) {
      return DateTime.fromMillisecondsSinceEpoch(dateValue);
    }
    
    // Fallback to current time
    return DateTime.now();
  }

  AccountType _parseAccountType(String type) {
    switch (type.toUpperCase()) {
      case 'CHECKING':
        return AccountType.checking;
      case 'SAVINGS':
        return AccountType.savings;
      case 'CREDIT_CARD':
        return AccountType.creditCard;
      case 'CASH':
        return AccountType.cash;
      case 'INVESTMENT':
        return AccountType.investment;
      case 'LOAN':
        return AccountType.loan;
      case 'ASSET':
        return AccountType.asset;
      case 'LIABILITY':
        return AccountType.liability;
      default:
        throw Exception('Unknown account type: $type');
    }
  }

  String _accountTypeToGraphQL(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return 'CHECKING';
      case AccountType.savings:
        return 'SAVINGS';
      case AccountType.creditCard:
        return 'CREDIT_CARD';
      case AccountType.cash:
        return 'CASH';
      case AccountType.investment:
        return 'INVESTMENT';
      case AccountType.loan:
        return 'LOAN';
      case AccountType.asset:
        return 'ASSET';
      case AccountType.liability:
        return 'LIABILITY';
    }
  }
} 