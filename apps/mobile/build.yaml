# Build configuration for code generation
targets:
  $default:
    builders:
      riverpod_generator|riverpod_generator:
        enabled: true
        options:
          # Generate providers for all annotated classes
          generate_for:
            - lib/**
      json_serializable|json_serializable:
        enabled: true
        options:
          # JSON serialization configuration
          any_map: false
          checked: false
          create_factory: true
          create_to_json: true
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true
