query Me {
  me {
    id
    email
    firstName
    lastName
    role
    emailVerified
    createdAt
    updatedAt
  }
}

mutation Register($input: RegisterInput!) {
  register(input: $input) {
    token
    user {
      id
      email
      firstName
      lastName
      role
      emailVerified
      createdAt
      updatedAt
    }
  }
}

mutation Login($input: LoginInput!) {
  login(input: $input) {
    token
    user {
      id
      email
      firstName
      lastName
      role
      emailVerified
      createdAt
      updatedAt
    }
  }
}

mutation RefreshToken {
  refreshToken {
    token
    user {
      id
      email
      firstName
      lastName
      role
      emailVerified
      createdAt
      updatedAt
    }
  }
}

mutation Logout {
  logout
} 