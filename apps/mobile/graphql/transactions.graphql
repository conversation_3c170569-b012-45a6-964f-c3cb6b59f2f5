# Transaction fragments
fragment TransactionFragment on Transaction {
  id
  userId
  description
  amount
  type
  date
  notes
  status
  isRecurring
  recurringPattern {
    type
    interval
    dayOfWeek
    dayOfMonth
    monthOfYear
    endDate
    occurrences
  }
  createdAt
  updatedAt
  journalLines {
    id
    journalEntryId
    accountId
    categoryId
    amount
    type
    notes
    createdAt
    updatedAt
    account {
      id
      name
      type
    }
    category {
      id
      name
      type
    }
  }
}

# Get all transactions
query GetTransactions($filter: TransactionFilterInput, $limit: Int, $offset: Int) {
  transactions(filter: $filter, limit: $limit, offset: $offset) {
    ...TransactionFragment
  }
}

# Get a specific transaction
query GetTransaction($id: ID!) {
  transaction(id: $id) {
    ...TransactionFragment
  }
}

# Create a new transaction
mutation CreateTransaction($input: CreateTransactionInput!) {
  createTransaction(input: $input) {
    ...TransactionFragment
  }
}

# Update an existing transaction
mutation UpdateTransaction($id: ID!, $input: UpdateTransactionInput!) {
  updateTransaction(id: $id, input: $input) {
    ...TransactionFragment
  }
}

# Delete a transaction
mutation DeleteTransaction($id: ID!) {
  deleteTransaction(id: $id)
} 