query GetAccounts {
  accounts {
    id
    userId
    name
    type
    currency
    initialBalance
    currentBalance
    isArchived
    notes
    icon
    color
    includeInNetWorth
    displayOrder
    createdAt
    updatedAt
  }
}

query GetAccount($id: ID!) {
  account(id: $id) {
    id
    userId
    name
    type
    currency
    initialBalance
    currentBalance
    isArchived
    notes
    icon
    color
    includeInNetWorth
    displayOrder
    createdAt
    updatedAt
  }
}

mutation CreateAccount($input: CreateAccountInput!) {
  createAccount(input: $input) {
    id
    userId
    name
    type
    currency
    initialBalance
    currentBalance
    isArchived
    notes
    icon
    color
    includeInNetWorth
    displayOrder
    createdAt
    updatedAt
  }
}

mutation UpdateAccount($id: ID!, $input: UpdateAccountInput!) {
  updateAccount(id: $id, input: $input) {
    id
    userId
    name
    type
    currency
    initialBalance
    currentBalance
    isArchived
    notes
    icon
    color
    includeInNetWorth
    displayOrder
    createdAt
    updatedAt
  }
}

mutation DeleteAccount($id: ID!) {
  deleteAccount(id: $id)
} 