# BudApp Mobile

This is the mobile application for BudApp, a personal finance management application. It's built with Flutter and connects to the BudApp API.

## Tech Stack

- **Framework**: Flutter SDK (latest stable version)
- **Language**: Dart
- **State Management**: Riverpod with riverpod_annotation
- **Navigation**: go_router with authentication-aware routing
- **Data Fetching**: graphql_flutter
- **Authentication**: flutter_appauth for OAuth, flutter_secure_storage for token storage
- **UI Kit**: Material 3 + Cupertino components
- **Forms**: Flutter's built-in Form + TextFormField with validation
- **Testing**: flutter_test (unit and widget tests), integration_test (E2E)

## Getting Started

### Prerequisites

- Flutter SDK (latest stable version)
- Android Studio or VS Code with Flutter extensions
- Android emulator or iOS simulator (or physical device)

### Setup

1. Install dependencies:

```bash
flutter pub get
```

2. **Generate code (Required for Riverpod providers):**

```bash
dart run build_runner build --delete-conflicting-outputs
```

3. Run the app:

```bash
flutter run
```

## 🛠️ Development

### Code Generation

This project uses code generation for Riverpod providers (`@riverpod` annotations).

**Important**: Always run code generation after:
- Adding new `@riverpod` annotated providers
- Modifying existing provider signatures
- Pulling changes from git that include provider changes

#### Commands:
```bash
# Generate once
dart run build_runner build --delete-conflicting-outputs

# Watch for changes (development)
dart run build_runner watch --delete-conflicting-outputs

# Using the convenience script
./scripts/generate.sh --analyze
```

#### From Project Root:
```bash
# Generate mobile code
pnpm mobile:codegen

# Watch for changes
pnpm mobile:codegen:watch

# Analyze code
pnpm mobile:analyze

# Run tests
pnpm mobile:test
```

### Troubleshooting

#### "Target of URI hasn't been generated" errors
This means code generation hasn't been run. Execute:
```bash
dart run build_runner build --delete-conflicting-outputs
```

#### "Undefined name" errors for providers
Same issue - run code generation to create the `.g.dart` files.

#### CI/CD Issues
The CI pipeline automatically runs code generation before analysis. If you see generation errors in CI, ensure your local generated files are committed and up to date.

## Authentication System

The mobile app implements a complete authentication system that connects to the BudApp API. The authentication system supports:

- Email/password registration and login
- Social login with Google and Apple
- Secure token storage
- Automatic token refresh
- Authentication state management
- Protected routes

### Authentication Flow

1. **Registration**: Users can register with email/password or via OAuth providers (Google, Apple)
2. **Login**: Users can log in with email/password or via OAuth providers
3. **Token Storage**: JWT tokens are securely stored using flutter_secure_storage
4. **State Management**: Authentication state is managed using Riverpod
5. **Protected Routes**: Routes are protected based on authentication state using go_router
6. **Logout**: Tokens are removed from secure storage on logout

### Authentication Components

#### Models

- `User`: Represents a user with ID, email, name, etc.
- `AuthPayload`: Contains the JWT token and user information
- `AuthState`: Represents the current authentication state (initial, unauthenticated, authenticating, authenticated, error)

#### Services

- `AuthService`: Handles authentication operations (register, login, social login, token refresh, logout)
- `SecureStorageService`: Handles secure storage of authentication tokens

#### Providers

- `AuthProvider`: Manages authentication state using Riverpod
- `GraphQLProvider`: Configures the GraphQL client with authentication headers

#### Screens

- `LoginScreen`: Allows users to log in with email/password or social providers
- `RegisterScreen`: Allows users to register with email/password
- `ProfileScreen`: Displays user information and logout button
- `SplashScreen`: Checks authentication state on app startup

### Error Handling

The authentication system includes comprehensive error handling:

- User-friendly error messages for authentication failures
- Loading indicators during authentication operations
- Form validation with clear feedback
- Network error handling with retry mechanisms
- Secure error logging that doesn't expose sensitive information

### Security Considerations

- Tokens are stored securely using flutter_secure_storage
- Passwords are never stored on the device
- Authentication headers are automatically added to GraphQL requests
- Token refresh is handled automatically
- Authentication state is cleared on logout

## Project Structure

```
lib/
├── config/       # Configuration files
├── models/       # Data models
├── providers/    # Riverpod providers
├── screens/      # UI screens
├── services/     # Business logic services
├── utils/        # Utility functions
└── widgets/      # Reusable UI components
```
