import { resolve } from 'node:path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,

    // Setup file for integration tests
    setupFiles: ['./src/test-utils/setup.ts'],

    // Set environment variables for integration tests
    env: {
      TEST_SUITE_TYPE: 'integration',
      NODE_ENV: 'test',
      // Auth configuration for tests
      JWT_SECRET: 'test-jwt-secret-for-testing-purposes-only',
      JWT_EXPIRES_IN: '1h',
      AUTH_COOKIE_NAME: 'budapp_auth_test',
      AUTH_COOKIE_SECURE: 'false',
      AUTH_COOKIE_HTTP_ONLY: 'true',
      AUTH_COOKIE_DOMAIN: 'localhost',
      AUTH_COOKIE_MAX_AGE: '86400',
      AUTH_REDIRECT_URL: 'http://localhost:3001/auth/callback',
      AUTH_MOBILE_REDIRECT_URL: 'budapp://auth/callback',
      GOOGLE_CLIENT_ID: 'test-google-client-id',
      GOOGLE_CLIENT_SECRET: 'test-google-client-secret',
      APPLE_CLIENT_ID: 'test-apple-client-id',
      APPLE_CLIENT_SECRET: 'test-apple-client-secret',
      OTEL_SDK_DISABLED: 'true',
    },

    // Global setup and teardown for integration tests
    globalSetup: './src/test-utils/global-setup.ts',
    globalTeardown: './src/test-utils/global-teardown.ts',

    // Test files - include integration, e2e, and example tests
    include: [
      '**/__tests__/integration/**/*.{test,spec}.ts',
      '**/__tests__/e2e/**/*.{test,spec}.ts',
      '**/__tests__/examples/**/*.{test,spec}.ts',
    ],
    exclude: ['**/node_modules/**', '**/dist/**'],

    // Run tests sequentially to avoid database conflicts
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true,
      },
    },

    // Timeouts
    testTimeout: 30000, // 30 seconds for integration tests
    hookTimeout: 30000, // 30 seconds for integration tests

    // Coverage
    coverage: {
      provider: 'istanbul', // Using Istanbul for coverage
      reporter: ['text', 'text-summary', 'json-summary'], // Add summary reporters
      reportsDirectory: './coverage',
      exclude: [
        'coverage/**',
        'dist/**',
        '**/node_modules/**',
        '**/*.d.ts',
        '**/*.test.ts',
        '**/*.spec.ts',
        '**/test-utils/**',
        '**/mocks/**',
      ],
      thresholds: {
        statements: 44,
        branches: 40,
        functions: 40,
        lines: 44,
      },
      // Ensure proper handling of ESM + TypeScript
      all: true, // Include all files, not just imported ones
      include: ['src/**/*.ts'], // Only include TypeScript files in src
      excludeNodeModules: true, // Exclude node_modules
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
});
