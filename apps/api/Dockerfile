# Multi-stage build for production optimization
FROM node:22-alpine AS base

# Install pnpm globally
RUN npm install -g pnpm@10.11.0

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY turbo.json ./
COPY tsconfig.json tsconfig.paths.json ./

# Copy API package files
COPY apps/api/package.json ./apps/api/
COPY apps/api/tsconfig.json ./apps/api/

# Install dependencies
RUN pnpm install --frozen-lockfile --prod=false

# Development stage
FROM base AS development
COPY apps/api ./apps/api
EXPOSE 3000
CMD ["pnpm", "--filter=api", "dev"]

# Build stage
FROM base AS build

# Copy source code
COPY apps/api ./apps/api

# Build the application
RUN pnpm --filter=api build

# Production dependencies stage
FROM base AS prod-deps
RUN pnpm install --frozen-lockfile --prod

# Production stage
FROM node:22-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy production dependencies
COPY --from=prod-deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=prod-deps --chown=nodejs:nodejs /app/apps/api/node_modules ./apps/api/node_modules

# Copy built application
COPY --from=build --chown=nodejs:nodejs /app/apps/api/dist ./apps/api/dist
COPY --from=build --chown=nodejs:nodejs /app/apps/api/package.json ./apps/api/

# Copy migration files
COPY --from=build --chown=nodejs:nodejs /app/apps/api/migrations ./apps/api/migrations

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "apps/api/dist/index.js"]
