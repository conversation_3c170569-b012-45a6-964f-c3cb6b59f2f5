{"name": "api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "biome lint src", "format": "biome format --write src", "test": "pnpm test:unit && pnpm test:integration", "test:all": "pnpm test:unit && pnpm test:integration", "test:ui": "vitest --ui", "clean": "rm -rf dist node_modules .turbo coverage", "db:generate": "drizzle-kit generate", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed/index.ts", "db:seed:test": "NODE_ENV=test tsx src/database/seed/index.ts", "db:studio": "drizzle-kit studio", "test:unit": "NODE_ENV=test vitest run --config vitest.unit.config.ts", "test:integration": "NODE_ENV=test vitest run --config vitest.integration.config.ts", "test:unit:watch": "vitest --config vitest.unit.config.ts", "test:integration:watch": "vitest --config vitest.integration.config.ts", "test:unit:coverage": "vitest run --coverage --config vitest.unit.config.ts", "test:integration:coverage": "vitest run --coverage --config vitest.integration.config.ts", "test:e2e": "NODE_ENV=test vitest run --config vitest.integration.config.ts __tests__/e2e", "test:e2e:watch": "vitest --config vitest.integration.config.ts __tests__/e2e", "test:coverage": "pnpm test:unit:coverage && pnpm test:integration:coverage", "test:coverage:summary": "pnpm test:unit:coverage --reporter=text-summary && pnpm test:integration:coverage --reporter=text-summary", "generate:schema": "tsx scripts/generate-schema.ts"}, "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@graphql-tools/schema": "^10.0.23", "@opentelemetry/auto-instrumentations-node": "^0.59.0", "@opentelemetry/exporter-trace-otlp-http": "^0.201.1", "@opentelemetry/instrumentation": "^0.201.1", "@opentelemetry/instrumentation-fastify": "^0.46.0", "@opentelemetry/instrumentation-graphql": "^0.49.0", "@opentelemetry/sdk-node": "^0.201.1", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/passport-google-oauth20": "^2.0.16", "altair-fastify-plugin": "8.2.2", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "fastify": "^5.3.3", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "jsonwebtoken": "^9.0.2", "mercurius": "^16.1.0", "mercurius-apollo-tracing": "^3.0.0", "mercurius-cache": "^8.0.0", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-google-oauth20": "^2.0.0", "pg": "^8.16.0", "pino": "^9.7.0", "postgres": "^3.4.6", "zod": "^3.25.13"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@types/node": "^22.15.21", "@types/pg": "^8.15.2", "@types/supertest": "^6.0.2", "@vitest/coverage-istanbul": "^3.1.4", "drizzle-kit": "^0.31.1", "msw": "^2.8.4", "supertest": "^6.3.4", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.4"}}