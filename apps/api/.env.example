# Server Configuration
PORT=3000
HOST=localhost
LOG_LEVEL=info

# Observability
# APOLLO_KEY=your-apollo-studio-api-key  # Optional: Only needed if you want to use Apollo Studio
# OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318/v1/traces  # Optional: For OpenTelemetry tracing

# Database Configuration
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/budapp

# Authentication
# JWT_SECRET=your-jwt-secret-should-be-at-least-32-chars-long
# JWT_EXPIRES_IN=1d
# AUTH_COOKIE_NAME=budapp_auth
# AUTH_COOKIE_SECURE=true
# AUTH_COOKIE_HTTP_ONLY=true
# AUTH_COOKIE_DOMAIN=localhost
# AUTH_COOKIE_MAX_AGE=2592000
# AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
# AUTH_MOBILE_REDIRECT_URL=budapp://auth/callback

# OAuth Providers
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret
# APPLE_CLIENT_ID=your-apple-client-id
# APPLE_CLIENT_SECRET=your-apple-client-secret
# APPLE_KEY_ID=your-apple-key-id
# APPLE_TEAM_ID=your-apple-team-id
# APPLE_PRIVATE_KEY=your-apple-private-key

# Email Service
# RESEND_API_KEY=your-resend-api-key

# Redis (for BullMQ)
# REDIS_URL=redis://localhost:6379
