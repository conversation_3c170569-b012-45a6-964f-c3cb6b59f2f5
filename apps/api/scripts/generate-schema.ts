import { existsSync, mkdirSync, writeFileSync, copyFileSync } from 'node:fs';
import { dirname, resolve } from 'node:path';
import { makeExecutableSchema } from '@graphql-tools/schema';
// Script to generate GraphQL schema from the API's executable schema
import { printSchema } from 'graphql';
import { authTypeDefs } from '../src/graphql/schema/auth.js';
import { accountTypeDefs } from '../src/graphql/schema/accounts.js';
import { categoryTypeDefs } from '../src/graphql/schema/categories.js';
import { transactionTypeDefs } from '../src/graphql/schema/transactions.js';

// Import any other schema definitions here
// import { otherTypeDefs } from '../src/graphql/schema/other.js';

// Define base schema with Query and Mutation types
const baseTypeDefs = `
  type Query {
    hello: String!
  }

  type Mutation {
    _empty: String
  }
`;

// Create executable schema with all type definitions
const schema = makeExecutableSchema({
  typeDefs: [
    baseTypeDefs,
    authTypeDefs,
    accountTypeDefs,
    categoryTypeDefs,
    transactionTypeDefs,
    // Add other type definitions here
    // otherTypeDefs,
  ],
});

// Convert schema to SDL
const schemaSDL = printSchema(schema);

// Define output directories and paths
const apiOutputDir = resolve(process.cwd(), 'generated');
const apiOutputPath = resolve(apiOutputDir, 'schema.graphql');

// Mobile app schema path (relative to API directory)
const mobileOutputDir = resolve(process.cwd(), '..', 'mobile', 'graphql');
const mobileOutputPath = resolve(mobileOutputDir, 'schema.graphql');

// Create API output directory if it doesn't exist
if (!existsSync(apiOutputDir)) {
  mkdirSync(apiOutputDir, { recursive: true });
}

// Create mobile output directory if it doesn't exist
if (!existsSync(mobileOutputDir)) {
  mkdirSync(mobileOutputDir, { recursive: true });
}

// Write schema to API generated directory
writeFileSync(apiOutputPath, schemaSDL);
console.log(`GraphQL schema generated at ${apiOutputPath}`);

// Copy schema to mobile app GraphQL directory
try {
  copyFileSync(apiOutputPath, mobileOutputPath);
  console.log(`GraphQL schema copied to ${mobileOutputPath}`);
} catch (error) {
  console.warn(`Warning: Could not copy schema to mobile directory: ${error}`);
  console.log('You can manually copy the schema using: npm run mobile:copy-schema');
}
