# BudApp API

This is the backend API for BudApp, a personal finance management application. It's built with Node.js, Fastify, GraphQL, and PostgreSQL.

## Tech Stack

- **Runtime**: Node.js 22 LTS
- **Language**: TypeScript (ESM)
- **Framework**: Fastify 5
- **API**: GraphQL (Mercurius)
- **ORM**: Drizzle ORM
- **Validation**: Zod
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: JWT, OAuth 2.0 (Google, Apple)
- **Observability**: OpenTelemetry, Apollo Studio (optional)
- **Caching**: Mercurius Cache
- **Logging**: Pino
- **Testing**: Vitest

## Getting Started

### Prerequisites

- Node.js 22 LTS
- pnpm 10+
- PostgreSQL database (or Supabase account)

### Setup

1. Copy `.env.example` to `.env` and update the values as needed:

```bash
cp .env.example .env
```

2. Install dependencies (from the root of the monorepo):

```bash
pnpm install
```

3. Start the development server:

```bash
# From the root of the monorepo
pnpm api:dev

# Or from the api directory
pnpm dev
```

The API will be available at http://localhost:3000/graphql

You can access the Altair GraphQL Client at http://localhost:3000/altair to explore and test the API interactively.

## Optional Features

### Apollo Studio Integration

The API supports integration with Apollo Studio for GraphQL schema management and performance monitoring. To enable this feature:

1. Create an account at [Apollo Studio](https://studio.apollographql.com/)
2. Create a new graph in Apollo Studio
3. Get your API key from Apollo Studio
4. Add the API key to your `.env` file:

```
APOLLO_KEY=your-apollo-studio-api-key
```

When the API key is provided, the Apollo tracing plugin will be automatically enabled, and your GraphQL schema and operations will be reported to Apollo Studio.

### OpenTelemetry Tracing

The API includes OpenTelemetry integration for distributed tracing. By default, traces are sent to a local OpenTelemetry collector at `http://localhost:4318/v1/traces`. To configure a different endpoint:

```
OTEL_EXPORTER_OTLP_ENDPOINT=https://your-otel-collector-endpoint
```

OpenTelemetry tracing is only enabled in production mode to avoid overhead during development.

## Available Scripts

- `pnpm dev` - Start the development server with hot reload
- `pnpm build` - Build the project for production
- `pnpm start` - Start the production server
- `pnpm lint` - Run ESLint
- `pnpm format` - Run Prettier
- `pnpm test` - Run tests
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:coverage` - Run tests with coverage report
- `pnpm clean` - Clean build artifacts and node_modules

## Project Structure

```
src/
├── config/       # Configuration files
├── graphql/      # GraphQL schema and resolvers
├── lib/          # Shared libraries and utilities
├── routes/       # HTTP routes (for non-GraphQL endpoints)
├── services/     # Business logic
└── utils/        # Utility functions
```

## GraphQL API

The GraphQL API is available at `/graphql`. You can use the Altair GraphQL Client at `/altair` to explore and test the API interactively.

### Testing with curl

You can also test the API using curl:

```bash
# Query the hello field
curl -X POST http://localhost:3000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ hello }"}'

# Response: {"data":{"hello":"Hello from BudApp API!"}}
```

## Authentication

The API uses JWT for authentication with OAuth 2.0 providers (Google, Apple) supported for social login.

### Authentication Flow

1. **Registration**: Users can register with email/password or via OAuth providers (Google, Apple)
2. **Login**: Users can log in with email/password or via OAuth providers
3. **Token Management**: JWT tokens are issued upon successful authentication
4. **Authorization**: Protected routes/queries require a valid JWT token
5. **Logout**: Tokens can be invalidated on the client side

### JWT Authentication

JWT (JSON Web Token) is used for stateless authentication. The token contains the user's ID, email, and role, and is signed with a secret key.

#### Token Structure

- **Header**: Contains the token type and algorithm
- **Payload**: Contains user information (ID, email, role) and token metadata (expiration)
- **Signature**: Ensures the token hasn't been tampered with

#### Token Lifecycle

- Tokens are issued upon successful login or registration
- Tokens expire after a configurable period (default: 1 day)
- Tokens can be refreshed using the refresh token endpoint
- Tokens are invalidated on logout (client-side)

### OAuth 2.0 Authentication

OAuth 2.0 is supported for social login with Google and Apple.

#### OAuth Flow

1. User initiates OAuth flow by clicking on a social login button
2. User is redirected to the OAuth provider's login page
3. After successful authentication, the provider redirects back to our API
4. API creates or finds the user in the database
5. API issues a JWT token for the user
6. User is redirected back to the application with the token

### GraphQL Authentication

Authentication in GraphQL is handled through the context object, which contains the user ID extracted from the JWT token.

#### Authentication Mutations

```graphql
# Register a new user
mutation Register($input: RegisterInput!) {
  register(input: $input) {
    token
    user {
      id
      email
      firstName
      lastName
      role
      emailVerified
    }
  }
}

# Login with email and password
mutation Login($input: LoginInput!) {
  login(input: $input) {
    token
    user {
      id
      email
      firstName
      lastName
      role
      emailVerified
    }
  }
}

# Refresh token
mutation RefreshToken {
  refreshToken {
    token
    user {
      id
      email
    }
  }
}

# Logout
mutation Logout {
  logout
}
```

#### Authentication Queries

```graphql
# Get the current user
query Me {
  me {
    id
    email
    firstName
    lastName
    role
    emailVerified
  }
}
```

### OAuth Endpoints

- `GET /auth/google`: Initiate Google OAuth flow
- `GET /auth/google/callback`: Handle Google OAuth callback
- `GET /auth/apple`: Initiate Apple Sign In flow
- `POST /auth/apple/callback`: Handle Apple Sign In callback

### Error Handling

Authentication errors are returned with appropriate HTTP status codes and error messages. Common error codes include:

- `INVALID_CREDENTIALS`: Invalid email or password
- `EMAIL_ALREADY_EXISTS`: Email already exists during registration
- `UNAUTHORIZED`: Missing or invalid token
- `TOKEN_EXPIRED`: Token has expired
- `WEAK_PASSWORD`: Password does not meet security requirements

### Security Considerations

- Passwords are hashed using bcrypt
- JWT tokens are signed with a secret key
- OAuth providers are configured with appropriate scopes
- Sensitive data is not stored in JWT tokens
- HTTPS is required for all API communication
- Token expiration is enforced
- Row-Level Security (RLS) is implemented in the database

## Database

The API uses Drizzle ORM to interact with a PostgreSQL database. The database schema is defined using Drizzle's schema builder and migrations are managed through Drizzle Kit.
