type Query {
  hello: String!
  me: User
  accounts: [Account!]!
  account(id: ID!): Account
  categories(filter: CategoryFilterInput): [Category!]!
  categoryTree(filter: CategoryFilterInput): [CategoryTree!]!
  category(id: ID!): Category
  transactions(filter: TransactionFilterInput, limit: Int = 50, offset: Int = 0): [Transaction!]!
  transaction(id: ID!): Transaction
  simpleTransaction(id: ID!): SimpleTransaction
}

type Mutation {
  _empty: String
  register(input: RegisterInput!): AuthPayload!
  login(input: LoginInput!): AuthPayload
  refreshToken: AuthPayload!
  logout: Boolean!
  createAccount(input: CreateAccountInput!): Account!
  updateAccount(id: ID!, input: UpdateAccountInput!): Account!
  deleteAccount(id: ID!): Boolean!
  createCategory(input: CreateCategoryInput!): Category!
  updateCategory(id: ID!, input: UpdateCategoryInput!): Category!
  deleteCategory(id: ID!): Boolean!
  createTransaction(input: CreateTransactionInput!): Transaction!
  updateTransaction(id: ID!, input: UpdateTransactionInput!): Transaction!
  deleteTransaction(id: ID!): Boolean!
}

type User {
  id: ID!
  email: String!
  firstName: String
  lastName: String
  role: String!
  emailVerified: Boolean!
  createdAt: String!
  updatedAt: String!
}

type AuthPayload {
  token: String!
  user: User!
}

input RegisterInput {
  email: String!
  password: String!
  firstName: String
  lastName: String
}

input LoginInput {
  email: String!
  password: String!
}

enum AccountType {
  CHECKING
  SAVINGS
  CREDIT_CARD
  CASH
  INVESTMENT
  LOAN
  ASSET
  LIABILITY
}

type Account {
  id: ID!
  userId: ID!
  name: String!
  type: AccountType!
  currency: String!
  initialBalance: Float!
  currentBalance: Float!
  isArchived: Boolean!
  notes: String
  icon: String
  color: String
  includeInNetWorth: Boolean!
  displayOrder: Int!
  createdAt: String!
  updatedAt: String!
}

input CreateAccountInput {
  name: String!
  type: AccountType!
  currency: String = "USD"
  initialBalance: Float = 0
  notes: String
  icon: String
  color: String
  includeInNetWorth: Boolean = true
  displayOrder: Int = 0
}

input UpdateAccountInput {
  name: String
  type: AccountType
  currency: String
  notes: String
  icon: String
  color: String
  includeInNetWorth: Boolean
  displayOrder: Int
  isArchived: Boolean
}

enum CategoryType {
  income
  expense
}

type Category {
  id: ID!
  userId: ID!
  parentId: ID
  name: String!
  type: CategoryType!
  icon: String
  color: String
  isDefault: Boolean!
  isSystem: Boolean!
  isArchived: Boolean!
  displayOrder: Int!
  createdAt: String!
  updatedAt: String!
  children: [Category!]
  parent: Category
}

type CategoryTree {
  id: ID!
  name: String!
  type: CategoryType!
  icon: String
  color: String
  displayOrder: Int!
  children: [CategoryTree!]!
}

input CreateCategoryInput {
  name: String!
  type: CategoryType!
  parentId: ID
  icon: String
  color: String
  displayOrder: Int = 0
}

input UpdateCategoryInput {
  name: String
  type: CategoryType
  parentId: ID
  icon: String
  color: String
  displayOrder: Int
  isArchived: Boolean
}

input CategoryFilterInput {
  type: CategoryType
  parentId: ID
  includeArchived: Boolean = false
  includeSystem: Boolean = true
}

enum TransactionType {
  INCOME
  EXPENSE
  TRANSFER
}

enum TransactionEntryType {
  DEBIT
  CREDIT
}

enum TransactionStatus {
  PENDING
  COMPLETED
  CANCELLED
  RECONCILED
}

enum RecurringPatternType {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

type RecurringPattern {
  type: RecurringPatternType!
  interval: Int!
  dayOfWeek: Int
  dayOfMonth: Int
  monthOfYear: Int
  endDate: String
  occurrences: Int
}

type JournalLine {
  id: ID!
  journalEntryId: ID!
  accountId: ID!
  categoryId: ID
  amount: Float!
  type: TransactionEntryType!
  notes: String
  createdAt: String!
  updatedAt: String!
  account: Account
  category: Category
}

type Transaction {
  id: ID!
  userId: ID!
  description: String!
  date: String!
  notes: String
  isRecurring: Boolean!
  recurringPattern: RecurringPattern
  status: TransactionStatus!
  createdAt: String!
  updatedAt: String!
  journalLines: [JournalLine!]!
  amount: Float!
  type: TransactionType!
}

type SimpleTransaction {
  id: ID!
  userId: ID!
  description: String!
  amount: Float!
  type: TransactionType!
  date: String!
  notes: String
  status: TransactionStatus!
  isRecurring: Boolean!
  account: Account!
  category: Category
  createdAt: String!
  updatedAt: String!
}

input JournalLineInput {
  accountId: ID!
  categoryId: ID
  amount: Float!
  type: TransactionEntryType!
  notes: String
}

input RecurringPatternInput {
  type: RecurringPatternType!
  interval: Int!
  dayOfWeek: Int
  dayOfMonth: Int
  monthOfYear: Int
  endDate: String
  occurrences: Int
}

input CreateTransactionInput {
  description: String!
  amount: Float!
  date: String
  notes: String
  accountId: ID
  categoryId: ID
  type: TransactionType
  journalLines: [JournalLineInput!]
  isRecurring: Boolean = false
  recurringPattern: RecurringPatternInput
}

input UpdateTransactionInput {
  description: String
  date: String
  notes: String
  status: TransactionStatus
  isRecurring: Boolean
  recurringPattern: RecurringPatternInput
}

input TransactionFilterInput {
  accountId: ID
  categoryId: ID
  type: TransactionType
  status: TransactionStatus
  startDate: String
  endDate: String
  minAmount: Float
  maxAmount: Float
  search: String
  isRecurring: Boolean
}