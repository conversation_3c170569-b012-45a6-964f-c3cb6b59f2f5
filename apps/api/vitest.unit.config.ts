import { resolve } from 'node:path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,

    // Setup file for unit tests
    setupFiles: ['./src/test-utils/setup.ts'],

    // Set environment variables for unit tests
    env: {
      TEST_SUITE_TYPE: 'unit',
      DATABASE_URL: 'postgresql://fake:fake@localhost:5432/fake_test_db',
      JWT_SECRET: 'fake-jwt-secret-for-unit-tests-only-32-chars-long',
      JWT_EXPIRES_IN: '1h',
      AUTH_COOKIE_NAME: 'budapp_auth_test',
      AUTH_COOKIE_SECURE: 'false',
      AUTH_COOKIE_HTTP_ONLY: 'true',
      AUTH_COOKIE_DOMAIN: 'localhost',
      AUTH_COOKIE_MAX_AGE: '86400',
      AUTH_REDIRECT_URL: 'http://localhost:3001/auth/callback',
      AUTH_MO<PERSON>LE_REDIRECT_URL: 'budapp://auth/callback',
      GOOGLE_CLIENT_ID: 'fake-google-client-id',
      GOOGLE_CLIENT_SECRET: 'fake-google-client-secret',
      APPLE_CLIENT_ID: 'fake-apple-client-id',
      APPLE_CLIENT_SECRET: 'fake-apple-client-secret',
      OTEL_SDK_DISABLED: 'true',
    },

    // Disable global setup and teardown for unit tests
    globalSetup: undefined,
    globalTeardown: undefined,

    // Test files - only include unit tests
    include: ['**/__tests__/unit/**/*.{test,spec}.ts'],
    exclude: ['**/node_modules/**', '**/dist/**'],

    // Timeouts
    testTimeout: 10000, // 10 seconds
    hookTimeout: 10000, // 10 seconds

    // Coverage
    coverage: {
      provider: 'istanbul', // Using Istanbul for coverage
      reporter: ['text', 'text-summary', 'json-summary'], // Add summary reporters
      reportsDirectory: './coverage',
      exclude: [
        'coverage/**',
        'dist/**',
        '**/node_modules/**',
        '**/*.d.ts',
        '**/*.test.ts',
        '**/*.spec.ts',
        '**/test-utils/**',
        '**/mocks/**',
      ],
      thresholds: {
        statements: 50,
        branches: 45,
        functions: 50,
        lines: 50,
      },
      // Ensure proper handling of ESM + TypeScript
      all: true, // Include all files, not just imported ones
      include: ['src/**/*.ts'], // Only include TypeScript files in src
      excludeNodeModules: true, // Exclude node_modules
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
});
