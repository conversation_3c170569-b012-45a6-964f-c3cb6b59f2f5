{"id": "f78745f4-783c-4010-bf9f-7158fe87deba", "prevId": "9a3f1d1c-06c4-4f26-b962-ad2644e5f9a8", "version": "6", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "account_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "initial_balance": {"name": "initial_balance", "type": "numeric(19, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "current_balance": {"name": "current_balance", "type": "numeric(19, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "include_in_net_worth": {"name": "include_in_net_worth", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"accounts_user_id_idx": {"name": "accounts_user_id_idx", "columns": ["user_id"], "isUnique": false}, "accounts_type_idx": {"name": "accounts_type_idx", "columns": ["type"], "isUnique": false}, "accounts_is_archived_idx": {"name": "accounts_is_archived_idx", "columns": ["is_archived"], "isUnique": false}}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.budgets": {"name": "budgets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(19, 4)", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "budget_period", "typeSchema": "public", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_recurring": {"name": "is_recurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"budgets_user_id_idx": {"name": "budgets_user_id_idx", "columns": ["user_id"], "isUnique": false}, "budgets_category_id_idx": {"name": "budgets_category_id_idx", "columns": ["category_id"], "isUnique": false}, "budgets_period_idx": {"name": "budgets_period_idx", "columns": ["period"], "isUnique": false}, "budgets_start_date_idx": {"name": "budgets_start_date_idx", "columns": ["start_date"], "isUnique": false}, "budgets_is_archived_idx": {"name": "budgets_is_archived_idx", "columns": ["is_archived"], "isUnique": false}}, "foreignKeys": {"budgets_user_id_users_id_fk": {"name": "budgets_user_id_users_id_fk", "tableFrom": "budgets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "budgets_category_id_categories_id_fk": {"name": "budgets_category_id_categories_id_fk", "tableFrom": "budgets", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "category_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"categories_user_id_idx": {"name": "categories_user_id_idx", "columns": ["user_id"], "isUnique": false}, "categories_parent_id_idx": {"name": "categories_parent_id_idx", "columns": ["parent_id"], "isUnique": false}, "categories_type_idx": {"name": "categories_type_idx", "columns": ["type"], "isUnique": false}, "categories_is_default_idx": {"name": "categories_is_default_idx", "columns": ["is_default"], "isUnique": false}, "categories_is_system_idx": {"name": "categories_is_system_idx", "columns": ["is_system"], "isUnique": false}, "categories_is_archived_idx": {"name": "categories_is_archived_idx", "columns": ["is_archived"], "isUnique": false}}, "foreignKeys": {"categories_user_id_users_id_fk": {"name": "categories_user_id_users_id_fk", "tableFrom": "categories", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.goals": {"name": "goals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "goal_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "target_amount": {"name": "target_amount", "type": "numeric(19, 4)", "primaryKey": false, "notNull": true}, "current_amount": {"name": "current_amount", "type": "numeric(19, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "deadline": {"name": "deadline", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"goals_user_id_idx": {"name": "goals_user_id_idx", "columns": ["user_id"], "isUnique": false}, "goals_type_idx": {"name": "goals_type_idx", "columns": ["type"], "isUnique": false}, "goals_account_id_idx": {"name": "goals_account_id_idx", "columns": ["account_id"], "isUnique": false}, "goals_deadline_idx": {"name": "goals_deadline_idx", "columns": ["deadline"], "isUnique": false}, "goals_is_archived_idx": {"name": "goals_is_archived_idx", "columns": ["is_archived"], "isUnique": false}, "goals_is_completed_idx": {"name": "goals_is_completed_idx", "columns": ["is_completed"], "isUnique": false}}, "foreignKeys": {"goals_user_id_users_id_fk": {"name": "goals_user_id_users_id_fk", "tableFrom": "goals", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "goals_account_id_accounts_id_fk": {"name": "goals_account_id_accounts_id_fk", "tableFrom": "goals", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.journal_entries": {"name": "journal_entries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "is_recurring": {"name": "is_recurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "recurring_pattern": {"name": "recurring_pattern", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'completed'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"journal_entries_user_id_idx": {"name": "journal_entries_user_id_idx", "columns": ["user_id"], "isUnique": false}, "journal_entries_date_idx": {"name": "journal_entries_date_idx", "columns": ["date"], "isUnique": false}, "journal_entries_is_recurring_idx": {"name": "journal_entries_is_recurring_idx", "columns": ["is_recurring"], "isUnique": false}}, "foreignKeys": {"journal_entries_user_id_users_id_fk": {"name": "journal_entries_user_id_users_id_fk", "tableFrom": "journal_entries", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.journal_lines": {"name": "journal_lines", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "journal_entry_id": {"name": "journal_entry_id", "type": "uuid", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(19, 4)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "transaction_entry_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"journal_lines_journal_entry_id_idx": {"name": "journal_lines_journal_entry_id_idx", "columns": ["journal_entry_id"], "isUnique": false}, "journal_lines_account_id_idx": {"name": "journal_lines_account_id_idx", "columns": ["account_id"], "isUnique": false}, "journal_lines_category_id_idx": {"name": "journal_lines_category_id_idx", "columns": ["category_id"], "isUnique": false}, "journal_lines_type_idx": {"name": "journal_lines_type_idx", "columns": ["type"], "isUnique": false}}, "foreignKeys": {"journal_lines_journal_entry_id_journal_entries_id_fk": {"name": "journal_lines_journal_entry_id_journal_entries_id_fk", "tableFrom": "journal_lines", "tableTo": "journal_entries", "columnsFrom": ["journal_entry_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "journal_lines_account_id_accounts_id_fk": {"name": "journal_lines_account_id_accounts_id_fk", "tableFrom": "journal_lines", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "journal_lines_category_id_categories_id_fk": {"name": "journal_lines_category_id_categories_id_fk", "tableFrom": "journal_lines", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "profile_picture_url": {"name": "profile_picture_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "default_currency": {"name": "default_currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "theme": {"name": "theme", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'light'"}, "date_format": {"name": "date_format", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'MM/DD/YYYY'"}, "time_format": {"name": "time_format", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'12h'"}, "notification_preferences": {"name": "notification_preferences", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"budgetAlerts\":true,\"lowBalanceAlerts\":true,\"goalAchievedAlerts\":true,\"weeklyReports\":true,\"marketingEmails\":false}'::jsonb"}, "privacy_settings": {"name": "privacy_settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"hideBalances\":false,\"requireAuthForSensitiveOperations\":true}'::jsonb"}, "app_settings": {"name": "app_settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"defaultView\":\"dashboard\",\"defaultAccountView\":\"all\",\"defaultTransactionPeriod\":\"month\"}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_settings_user_id_idx": {"name": "user_settings_user_id_idx", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "phone_verified": {"name": "phone_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}}, "enums": {"public.account_type": {"name": "account_type", "schema": "public", "values": ["checking", "savings", "credit_card", "cash", "investment", "loan", "asset", "liability"]}, "public.budget_period": {"name": "budget_period", "schema": "public", "values": ["daily", "weekly", "monthly", "quarterly", "yearly"]}, "public.category_type": {"name": "category_type", "schema": "public", "values": ["income", "expense"]}, "public.goal_type": {"name": "goal_type", "schema": "public", "values": ["savings", "debt_payoff", "purchase", "emergency_fund", "other"]}, "public.recurring_pattern_type": {"name": "recurring_pattern_type", "schema": "public", "values": ["daily", "weekly", "monthly", "yearly"]}, "public.transaction_entry_type": {"name": "transaction_entry_type", "schema": "public", "values": ["debit", "credit"]}, "public.transaction_type": {"name": "transaction_type", "schema": "public", "values": ["income", "expense", "transfer"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "admin"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}