// Load environment variables first
import './config/env.js';

import { createServer } from './server.js';
import { sdk } from './server.js';
import { logger } from './utils/logger.js';

const PORT = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
const HOST = process.env.HOST || '0.0.0.0';

async function start() {
  const server = await createServer();

  try {
    await server.listen({ port: PORT, host: HOST });
    logger.info(`Server listening on ${HOST}:${PORT}`);

    // Handle graceful shutdown
    const shutdown = async () => {
      logger.info('Shutting down server...');
      await server.close();

      // Shutdown OpenTelemetry
      if (process.env.NODE_ENV === 'production') {
        logger.info('Shutting down OpenTelemetry...');
        await sdk.shutdown();
      }

      process.exit(0);
    };

    // Listen for termination signals
    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
  } catch (err) {
    logger.error(err instanceof Error ? err : String(err));
    process.exit(1);
  }
}

start();
