// Transaction types and validation schemas
import { z } from 'zod';

// Transaction type enum for validation
export const transactionTypeSchema = z.enum([
  'income',
  'expense', 
  'transfer',
]);

export type TransactionTypeValue = z.infer<typeof transactionTypeSchema>;

// Transaction entry type enum for validation (double-entry accounting)
export const transactionEntryTypeSchema = z.enum([
  'debit',
  'credit',
]);

export type TransactionEntryTypeValue = z.infer<typeof transactionEntryTypeSchema>;

// Transaction status enum
export const transactionStatusSchema = z.enum([
  'pending',
  'completed',
  'cancelled',
  'reconciled',
]);

export type TransactionStatusValue = z.infer<typeof transactionStatusSchema>;

// Journal line input for creating transaction entries
export const journalLineInputSchema = z.object({
  accountId: z.string().uuid('Account ID must be a valid UUID'),
  categoryId: z.string().uuid('Category ID must be a valid UUID').optional(),
  amount: z.number().positive('Amount must be positive'),
  type: transactionEntryTypeSchema,
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
});

export type JournalLineInput = z.infer<typeof journalLineInputSchema>;

// Create transaction input validation schema
export const createTransactionInputSchema = z.object({
  description: z.string().min(1, 'Description is required').max(255, 'Description must be less than 255 characters'),
  amount: z.number().positive('Amount must be positive'),
  date: z.date().optional(), // Defaults to now if not provided
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  
  // For simple income/expense transactions
  accountId: z.string().uuid('Account ID must be a valid UUID').optional(),
  categoryId: z.string().uuid('Category ID must be a valid UUID').optional(),
  type: transactionTypeSchema.optional(),
  
  // For complex transactions or transfers (manual journal entries)
  journalLines: z.array(journalLineInputSchema).min(2, 'At least two journal lines required for double-entry').optional(),
  
  // Recurring transaction fields
  isRecurring: z.boolean().default(false),
  recurringPattern: z.object({
    type: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
    interval: z.number().int().min(1, 'Interval must be at least 1'),
    dayOfWeek: z.number().int().min(0).max(6).optional(), // 0-6 (Sunday-Saturday)
    dayOfMonth: z.number().int().min(1).max(31).optional(), // 1-31
    monthOfYear: z.number().int().min(1).max(12).optional(), // 1-12
    endDate: z.date().optional(),
    occurrences: z.number().int().positive().optional(),
  }).optional(),
}).refine(
  (data) => {
    // Either provide simple transaction fields OR journal lines, not both
    const hasSimpleFields = data.accountId && data.type;
    const hasJournalLines = data.journalLines && data.journalLines.length > 0;
    return hasSimpleFields !== hasJournalLines; // XOR - exactly one should be true
  },
  {
    message: 'Provide either simple transaction fields (accountId, type) OR journal lines, not both',
  }
);

export type CreateTransactionInput = z.infer<typeof createTransactionInputSchema>;

// Update transaction input validation schema
export const updateTransactionInputSchema = z.object({
  description: z.string().min(1, 'Description is required').max(255, 'Description must be less than 255 characters').optional(),
  date: z.date().optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  status: transactionStatusSchema.optional(),
  
  // Recurring transaction updates
  isRecurring: z.boolean().optional(),
  recurringPattern: z.object({
    type: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
    interval: z.number().int().min(1, 'Interval must be at least 1'),
    dayOfWeek: z.number().int().min(0).max(6).optional(),
    dayOfMonth: z.number().int().min(1).max(31).optional(),
    monthOfYear: z.number().int().min(1).max(12).optional(),
    endDate: z.date().optional(),
    occurrences: z.number().int().positive().optional(),
  }).optional(),
});

export type UpdateTransactionInput = z.infer<typeof updateTransactionInputSchema>;

// Transaction filter input for queries
export const transactionFilterInputSchema = z.object({
  accountId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  type: transactionTypeSchema.optional(),
  status: transactionStatusSchema.optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  minAmount: z.number().optional(),
  maxAmount: z.number().optional(),
  search: z.string().optional(), // Search in description and notes
  isRecurring: z.boolean().optional(),
});

export type TransactionFilterInput = z.infer<typeof transactionFilterInputSchema>;

// Journal line response type (matches database schema)
export interface JournalLineResponse {
  id: string;
  journalEntryId: string;
  accountId: string;
  categoryId?: string | null;
  amount: string; // Decimal stored as string
  type: TransactionEntryTypeValue;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  
  // Populated relations
  account?: {
    id: string;
    name: string;
    type: string;
  };
  category?: {
    id: string;
    name: string;
    type: string;
  } | null;
}

// Transaction response type (matches database schema)
export interface TransactionResponse {
  id: string;
  userId: string;
  description: string;
  date: Date;
  notes?: string | null;
  isRecurring: boolean;
  recurringPattern?: {
    type: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    dayOfWeek?: number;
    dayOfMonth?: number;
    monthOfYear?: number;
    endDate?: Date;
    occurrences?: number;
  } | null; // JSON field
  status: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Populated journal lines
  journalLines: JournalLineResponse[];
  
  // Computed fields
  amount: string; // Total amount (sum of debits or credits)
  type: TransactionTypeValue; // Computed from journal lines
}

// Simple transaction response for income/expense (derived from TransactionResponse)
export interface SimpleTransactionResponse {
  id: string;
  userId: string;
  description: string;
  amount: string;
  type: TransactionTypeValue;
  date: Date;
  notes?: string | null;
  status: string;
  isRecurring: boolean;
  
  // Primary account and category
  account: {
    id: string;
    name: string;
    type: string;
  };
  category?: {
    id: string;
    name: string;
    type: string;
  } | null;
  
  createdAt: Date;
  updatedAt: Date;
} 