// Account service
import { and, eq } from 'drizzle-orm';
import { db } from '../../database/client.js';
import { accounts } from '../../database/schema.js';
import type { Account } from '../../database/types.js';
import {
  DatabaseError,
  RecordNotFoundError,
  AuthenticationError,
  ValidationError,
} from '../../utils/error-handler.js';
import { logger } from '../../utils/logger.js';
import type {
  AccountResponse,
  CreateAccountInput,
  UpdateAccountInput,
} from './accounts.types.js';

/**
 * Get all accounts for a user
 * @param userId User ID
 * @returns Array of user's accounts
 */
export async function getUserAccounts(userId: string): Promise<AccountResponse[]> {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    const userAccounts = await db.query.accounts.findMany({
      where: and(eq(accounts.userId, userId), eq(accounts.isArchived, false)),
      orderBy: [accounts.displayOrder, accounts.createdAt],
    });

    return userAccounts.map(formatAccountResponse);
  } catch (error) {
    logger.error('Error fetching user accounts:', error);
    throw new DatabaseError('Error fetching accounts', { originalError: error });
  }
}

/**
 * Get a specific account by ID
 * @param accountId Account ID
 * @param userId User ID (for authorization)
 * @returns Account or null if not found
 */
export async function getAccountById(
  accountId: string,
  userId: string
): Promise<AccountResponse | null> {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    const account = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.id, accountId),
        eq(accounts.userId, userId),
        eq(accounts.isArchived, false)
      ),
    });

    return account ? formatAccountResponse(account) : null;
  } catch (error) {
    logger.error('Error fetching account:', error);
    throw new DatabaseError('Error fetching account', { originalError: error });
  }
}

/**
 * Create a new account
 * @param userId User ID
 * @param input Account creation data
 * @returns Created account
 */
export async function createAccount(
  userId: string,
  input: CreateAccountInput
): Promise<AccountResponse> {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // Set current balance to initial balance for new accounts
    const [newAccount] = await db
      .insert(accounts)
      .values({
        userId,
        name: input.name,
        type: input.type,
        currency: input.currency || 'USD',
        initialBalance: input.initialBalance?.toString() || '0',
        currentBalance: input.initialBalance?.toString() || '0',
        notes: input.notes || null,
        icon: input.icon || null,
        color: input.color || null,
        includeInNetWorth: input.includeInNetWorth ?? true,
        displayOrder: input.displayOrder || 0,
        isArchived: false,
      })
      .returning();

    if (!newAccount) {
      throw new DatabaseError('Failed to create account');
    }

    logger.info(`Account created: ${newAccount.id} for user: ${userId}`);
    return formatAccountResponse(newAccount);
  } catch (error) {
    logger.error('Error creating account:', error);
    throw new DatabaseError('Error creating account', { originalError: error });
  }
}

/**
 * Update an existing account
 * @param accountId Account ID
 * @param userId User ID (for authorization)
 * @param input Account update data
 * @returns Updated account
 */
export async function updateAccount(
  accountId: string,
  userId: string,
  input: UpdateAccountInput
): Promise<AccountResponse> {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // First verify the account exists and belongs to the user
    const existingAccount = await getAccountById(accountId, userId);
    if (!existingAccount) {
      throw new RecordNotFoundError('Account not found');
    }

    // Build update object with only provided fields
    const updateData: Partial<Account> = {};
    if (input.name !== undefined) updateData.name = input.name;
    if (input.type !== undefined) updateData.type = input.type;
    if (input.currency !== undefined) updateData.currency = input.currency;
    if (input.notes !== undefined) updateData.notes = input.notes;
    if (input.icon !== undefined) updateData.icon = input.icon;
    if (input.color !== undefined) updateData.color = input.color;
    if (input.includeInNetWorth !== undefined) updateData.includeInNetWorth = input.includeInNetWorth;
    if (input.displayOrder !== undefined) updateData.displayOrder = input.displayOrder;
    if (input.isArchived !== undefined) updateData.isArchived = input.isArchived;

    // Add updated timestamp
    updateData.updatedAt = new Date();

    const [updatedAccount] = await db
      .update(accounts)
      .set(updateData)
      .where(and(eq(accounts.id, accountId), eq(accounts.userId, userId)))
      .returning();

    if (!updatedAccount) {
      throw new DatabaseError('Failed to update account');
    }

    logger.info(`Account updated: ${accountId} for user: ${userId}`);
    return formatAccountResponse(updatedAccount);
  } catch (error) {
    if (error instanceof RecordNotFoundError) {
      throw error;
    }
    logger.error('Error updating account:', error);
    throw new DatabaseError('Error updating account', { originalError: error });
  }
}

/**
 * Delete an account (soft delete by archiving)
 * @param accountId Account ID
 * @param userId User ID (for authorization)
 * @returns Success boolean
 */
export async function deleteAccount(
  accountId: string,
  userId: string
): Promise<boolean> {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // First verify the account exists and belongs to the user
    const existingAccount = await getAccountById(accountId, userId);
    if (!existingAccount) {
      throw new RecordNotFoundError('Account not found');
    }

    // Soft delete by archiving
    const [archivedAccount] = await db
      .update(accounts)
      .set({
        isArchived: true,
        updatedAt: new Date(),
      })
      .where(and(eq(accounts.id, accountId), eq(accounts.userId, userId)))
      .returning();

    if (!archivedAccount) {
      throw new DatabaseError('Failed to archive account');
    }

    logger.info(`Account archived: ${accountId} for user: ${userId}`);
    return true;
  } catch (error) {
    if (error instanceof RecordNotFoundError) {
      throw error;
    }
    logger.error('Error archiving account:', error);
    throw new DatabaseError('Error archiving account', { originalError: error });
  }
}

/**
 * Format account data for API response
 * @param account Raw account from database
 * @returns Formatted account response
 */
function formatAccountResponse(account: Account): AccountResponse {
  return {
    id: account.id,
    userId: account.userId,
    name: account.name,
    type: account.type,
    currency: account.currency,
    initialBalance: account.initialBalance,
    currentBalance: account.currentBalance,
    isArchived: account.isArchived,
    notes: account.notes,
    icon: account.icon,
    color: account.color,
    includeInNetWorth: account.includeInNetWorth,
    displayOrder: account.displayOrder,
    createdAt: account.createdAt,
    updatedAt: account.updatedAt,
  };
} 