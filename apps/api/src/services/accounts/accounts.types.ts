// Account types and validation schemas
import { z } from 'zod';

// Account type enum for validation
export const accountTypeSchema = z.enum([
  'checking',
  'savings',
  'credit_card',
  'cash',
  'investment',
  'loan',
  'asset',
  'liability',
]);

export type AccountTypeValue = z.infer<typeof accountTypeSchema>;

// Create account input validation schema
export const createAccountInputSchema = z.object({
  name: z.string().min(1, 'Account name is required').max(255, 'Account name must be less than 255 characters'),
  type: accountTypeSchema,
  currency: z.string().length(3, 'Currency must be a 3-letter code').default('USD'),
  initialBalance: z.number().default(0),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').nullable().optional(),
  icon: z.string().max(50, 'Icon must be less than 50 characters').nullable().optional(),
  color: z.string().max(20, 'Color must be less than 20 characters').nullable().optional(),
  includeInNetWorth: z.boolean().default(true),
  displayOrder: z.number().int().min(0).default(0),
});

export type CreateAccountInput = z.infer<typeof createAccountInputSchema>;

// Update account input validation schema
export const updateAccountInputSchema = z.object({
  name: z.string().min(1, 'Account name is required').max(255, 'Account name must be less than 255 characters').optional(),
  type: accountTypeSchema.optional(),
  currency: z.string().length(3, 'Currency must be a 3-letter code').optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').nullable().optional(),
  icon: z.string().max(50, 'Icon must be less than 50 characters').nullable().optional(),
  color: z.string().max(20, 'Color must be less than 20 characters').nullable().optional(),
  includeInNetWorth: z.boolean().optional(),
  displayOrder: z.number().int().min(0).optional(),
  isArchived: z.boolean().optional(),
});

export type UpdateAccountInput = z.infer<typeof updateAccountInputSchema>;

// Account response type (matches database schema)
export interface AccountResponse {
  id: string;
  userId: string;
  name: string;
  type: AccountTypeValue;
  currency: string;
  initialBalance: string; // Decimal stored as string
  currentBalance: string; // Decimal stored as string
  isArchived: boolean;
  notes?: string | null;
  icon?: string | null;
  color?: string | null;
  includeInNetWorth: boolean;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
} 