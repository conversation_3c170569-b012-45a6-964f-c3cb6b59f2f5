// Category types and validation schemas
import { z } from 'zod';

// Category type enum for validation (matches database schema)
export const categoryTypeSchema = z.enum(['income', 'expense']);

export type CategoryTypeValue = z.infer<typeof categoryTypeSchema>;

// Create category input validation schema
export const createCategoryInputSchema = z.object({
  name: z.string().min(1, 'Category name is required').max(255, 'Category name must be less than 255 characters'),
  type: categoryTypeSchema,
  parentId: z.string().uuid('Parent ID must be a valid UUID').optional(),
  icon: z.string().max(50, 'Icon must be less than 50 characters').optional(),
  color: z.string().max(20, 'Color must be less than 20 characters').optional(),
  displayOrder: z.number().int().min(0).default(0),
});

export type CreateCategoryInput = z.infer<typeof createCategoryInputSchema>;

// Update category input validation schema
export const updateCategoryInputSchema = z.object({
  name: z.string().min(1, 'Category name is required').max(255, 'Category name must be less than 255 characters').optional(),
  type: categoryTypeSchema.optional(),
  parentId: z.string().uuid('Parent ID must be a valid UUID').nullable().optional(),
  icon: z.string().max(50, 'Icon must be less than 50 characters').optional(),
  color: z.string().max(20, 'Color must be less than 20 characters').optional(),
  displayOrder: z.number().int().min(0).optional(),
  isArchived: z.boolean().optional(),
});

export type UpdateCategoryInput = z.infer<typeof updateCategoryInputSchema>;

// Category filter input validation schema
export const categoryFilterInputSchema = z.object({
  type: categoryTypeSchema.optional(),
  parentId: z.string().uuid('Parent ID must be a valid UUID').nullable().optional(),
  includeArchived: z.boolean().default(false),
  includeSystem: z.boolean().default(true),
});

export type CategoryFilterInput = z.infer<typeof categoryFilterInputSchema>;

// Category response type (matches database schema)
export interface CategoryResponse {
  id: string;
  userId: string;
  parentId?: string | null;
  name: string;
  type: CategoryTypeValue;
  icon?: string | null;
  color?: string | null;
  isDefault: boolean;
  isSystem: boolean;
  isArchived: boolean;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
  // Computed fields for hierarchical display
  children?: CategoryResponse[];
  parent?: CategoryResponse | null;
}

// Category tree response for hierarchical display
export interface CategoryTreeResponse {
  id: string;
  name: string;
  type: CategoryTypeValue;
  icon?: string | null;
  color?: string | null;
  displayOrder: number;
  children: CategoryTreeResponse[];
} 