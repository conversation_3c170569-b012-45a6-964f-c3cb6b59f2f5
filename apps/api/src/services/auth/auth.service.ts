import { eq } from 'drizzle-orm';
import type { JwtPayload } from '../../config/auth.js';
import type { OAuthProfile } from '../../config/auth.js';
import { db } from '../../database/client.js';
import { userSettings, users } from '../../database/schema.js';
import type { User } from '../../database/types.js';
// Authentication service
import { generateToken } from '../../lib/auth/jwt.js';
import { hashPassword, verifyPassword } from '../../lib/auth/password.js';
import {
  DatabaseError,
  EmailAlreadyExistsError,
  InvalidCredentialsError,
  OAuthError,
  UserNotFoundError,
  WeakPasswordError,
} from '../../utils/error-handler.js';
import { logger } from '../../utils/logger.js';

// Type guard for error objects with code property
function hasErrorCode(error: unknown): error is { code: string } {
  return typeof error === 'object' && error !== null && 'code' in error;
}

// Type guard for error objects with message property
function hasErrorMessage(error: unknown): error is { message: string } {
  return typeof error === 'object' && error !== null && 'message' in error;
}

/**
 * Retry a database operation with exponential backoff
 * @param operation The operation to retry
 * @param maxRetries Maximum number of retries
 * @param baseDelay Base delay in milliseconds
 * @returns The result of the operation
 */
async function retryDatabaseOperation<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  let lastError: Error = new Error('Unknown error');

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: unknown) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Don't retry for certain types of errors
      if (
        hasErrorCode(error) &&
        (error.code === 'EMAIL_ALREADY_EXISTS' ||
          error.code === 'INVALID_CREDENTIALS' ||
          error.code === 'UNAUTHORIZED')
      ) {
        throw error;
      }

      // Check if it's a connection timeout or similar network error
      const isRetryableError =
        (hasErrorCode(error) &&
          (error.code === 'CONNECT_TIMEOUT' ||
            error.code === 'ECONNRESET' ||
            error.code === 'ENOTFOUND')) ||
        (hasErrorMessage(error) &&
          (error.message.includes('timeout') ||
            error.message.includes('connection')));

      if (!isRetryableError || attempt === maxRetries) {
        throw error;
      }

      // Wait before retrying with exponential backoff
      const delay = baseDelay * 2 ** attempt;
      const errorMessage = hasErrorMessage(error)
        ? error.message
        : String(error);
      logger.warn(
        `Database operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms:`,
        errorMessage
      );
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * Register a new user
 * @param email User email
 * @param password User password
 * @param firstName User first name
 * @param lastName User last name
 * @returns The created user and JWT token
 */
export async function registerUser(
  email: string,
  password: string,
  firstName?: string,
  lastName?: string
) {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email.toLowerCase()),
    });

    if (existingUser) {
      throw new EmailAlreadyExistsError('User with this email already exists');
    }

    // Validate password strength (additional check beyond Zod validation)
    if (password.length < 8) {
      throw new WeakPasswordError('Password must be at least 8 characters');
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user in database
    const [newUser] = await db
      .insert(users)
      .values({
        email: email.toLowerCase(),
        passwordHash,
        firstName,
        lastName,
        role: 'user',
        emailVerified: false,
      })
      .returning();

    if (!newUser) {
      throw new DatabaseError('Failed to create user');
    }

    // Create user settings
    await db.insert(userSettings).values({
      userId: newUser.id,
      defaultCurrency: 'USD',
      theme: 'light',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
    });

    // Generate JWT token
    const token = generateToken({
      userId: newUser.id,
      email: newUser.email,
      role: newUser.role,
    });

    return {
      user: newUser,
      token,
    };
  } catch (error) {
    // Re-throw AppErrors, wrap other errors
    if (
      error instanceof EmailAlreadyExistsError ||
      error instanceof WeakPasswordError ||
      error instanceof DatabaseError
    ) {
      throw error;
    }

    // Wrap unknown errors
    throw new DatabaseError('Error registering user', { originalError: error });
  }
}

/**
 * Login a user with email and password
 * @param email User email
 * @param password User password
 * @returns The user and JWT token
 */
export async function loginUser(email: string, password: string) {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // Find user by email
    const user = await db.query.users.findFirst({
      where: eq(users.email, email.toLowerCase()),
    });

    if (!user || !user.passwordHash) {
      throw new InvalidCredentialsError('Invalid email or password');
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);

    if (!isPasswordValid) {
      throw new InvalidCredentialsError('Invalid email or password');
    }

    // Update last login time (optional)
    // await db.update(users)
    //   .set({ lastLoginAt: new Date() })
    //   .where(eq(users.id, user.id));

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    return {
      user,
      token,
    };
  } catch (error) {
    // Re-throw AppErrors, wrap other errors
    if (
      error instanceof InvalidCredentialsError ||
      error instanceof DatabaseError
    ) {
      throw error;
    }

    // Wrap unknown errors
    throw new DatabaseError('Error during login', { originalError: error });
  }
}

/**
 * Handle OAuth authentication
 * @param profile OAuth profile
 * @returns The user and JWT token
 */
export async function handleOAuthLogin(profile: OAuthProfile) {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // Check if user already exists
    let user = await db.query.users.findFirst({
      where: eq(users.email, profile.email.toLowerCase()),
    });

    if (!user) {
      // Create new user
      const [newUser] = await db
        .insert(users)
        .values({
          email: profile.email.toLowerCase(),
          firstName: profile.firstName,
          lastName: profile.lastName,
          role: 'user',
          emailVerified: true, // Email is verified through OAuth
        })
        .returning();

      if (!newUser) {
        throw new DatabaseError('Failed to create user');
      }

      // Create user settings
      await db.insert(userSettings).values({
        userId: newUser.id,
        defaultCurrency: 'USD',
        theme: 'light',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
      });

      user = newUser;
    }

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    return {
      user,
      token,
    };
  } catch (error) {
    // Re-throw AppErrors, wrap other errors
    if (error instanceof DatabaseError) {
      throw error;
    }

    // Wrap unknown errors as OAuthError
    throw new OAuthError(`OAuth authentication failed: ${profile.provider}`, {
      originalError: error,
    });
  }
}

/**
 * Get user by ID
 * @param userId User ID
 * @returns The user or null if not found
 */
export async function getUserById(userId: string): Promise<User | null> {
  if (!db) {
    throw new DatabaseError('Database connection not available');
  }

  try {
    // Find user by ID
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      return null;
    }

    return user;
  } catch (error) {
    // Wrap unknown errors
    throw new DatabaseError('Error fetching user', { originalError: error });
  }
}

/**
 * Validate a JWT payload
 * @param payload JWT payload
 * @returns True if the payload is valid
 */
export async function validateJwtPayload(
  payload: JwtPayload
): Promise<boolean> {
  if (!payload.userId || !payload.email) {
    return false;
  }

  try {
    if (!db) {
      throw new DatabaseError('Database connection not available');
    }

    // Check if user exists in database
    const user = await db.query.users.findFirst({
      where: eq(users.id, payload.userId),
    });

    return !!user && user.email === payload.email;
  } catch (error) {
    // Log the error but don't throw it to avoid breaking authentication flow
    console.error('Error validating JWT payload:', error);
    return false;
  }
}
