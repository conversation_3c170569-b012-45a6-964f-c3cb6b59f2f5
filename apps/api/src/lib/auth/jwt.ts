// JWT utilities
import jsonwebtoken from 'jsonwebtoken';
import { type JwtPayload, authConfig } from '../../config/auth.js';

// Create a type-safe wrapper for jsonwebtoken
const jwt = {
  sign: (
    payload: string | object | Buffer,
    secret: jsonwebtoken.Secret,
    options?: jsonwebtoken.SignOptions
  ) => jsonwebtoken.sign(payload, secret, options),
  verify: (
    token: string,
    secret: jsonwebtoken.Secret,
    options?: jsonwebtoken.VerifyOptions
  ) => jsonwebtoken.verify(token, secret, options),
};

// Re-export JwtPayload type
export type { JwtPayload };

/**
 * Generate a JWT token for a user
 * @param payload The payload to include in the token
 * @returns The generated JWT token
 */
export function generateToken(payload: JwtPayload): string {
  // Using a simple string for the secret
  // In a real implementation, we would use a secure secret
  return jwt.sign(payload, authConfig.JWT_SECRET, {
    expiresIn:
      authConfig.JWT_EXPIRES_IN as jsonwebtoken.SignOptions['expiresIn'],
  });
}

/**
 * Verify a JWT token
 * @param token The token to verify
 * @returns The decoded token payload or null if invalid
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    return jwt.verify(token, authConfig.JWT_SECRET) as JwtPayload;
  } catch (error) {
    return null;
  }
}

/**
 * Extract a token from an authorization header
 * @param authHeader The authorization header
 * @returns The extracted token or null if not found
 */
export function extractTokenFromHeader(authHeader?: string): string | null {
  if (!authHeader) {
    return null;
  }

  const [type, token] = authHeader.split(' ');

  if (type !== 'Bearer' || !token) {
    return null;
  }

  return token;
}

/**
 * Generate a refresh token
 * @param userId The user ID
 * @returns The generated refresh token
 */
export function generateRefreshToken(userId: string): string {
  return jwt.sign({ userId }, authConfig.JWT_SECRET, {
    expiresIn: '30d' as jsonwebtoken.SignOptions['expiresIn'], // Longer expiration for refresh token
  });
}
