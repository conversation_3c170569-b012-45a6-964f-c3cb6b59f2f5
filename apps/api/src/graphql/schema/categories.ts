// Categories GraphQL schema
import { gql } from 'graphql-tag';

export const categoryTypeDefs = gql`
  # Category type enum
  enum CategoryType {
    income
    expense
  }

  # Category type
  type Category {
    id: ID!
    userId: ID!
    parentId: ID
    name: String!
    type: CategoryType!
    icon: String
    color: String
    isDefault: Boolean!
    isSystem: Boolean!
    isArchived: Boolean!
    displayOrder: Int!
    createdAt: String!
    updatedAt: String!
    # Computed fields for hierarchical display
    children: [Category!]
    parent: Category
  }

  # Category tree type for hierarchical display
  type CategoryTree {
    id: ID!
    name: String!
    type: CategoryType!
    icon: String
    color: String
    displayOrder: Int!
    children: [CategoryTree!]!
  }

  # Create category input
  input CreateCategoryInput {
    name: String!
    type: CategoryType!
    parentId: ID
    icon: String
    color: String
    displayOrder: Int = 0
  }

  # Update category input
  input UpdateCategoryInput {
    name: String
    type: CategoryType
    parentId: ID
    icon: String
    color: String
    displayOrder: Int
    isArchived: Boolean
  }

  # Category filter input
  input CategoryFilterInput {
    type: CategoryType
    parentId: ID
    includeArchived: Boolean = false
    includeSystem: Boolean = true
  }

  # Extend the root Query type
  extend type Query {
    # Get all categories for the authenticated user
    categories(filter: CategoryFilterInput): [Category!]!

    # Get categories organized in a hierarchical tree structure
    categoryTree(filter: CategoryFilterInput): [CategoryTree!]!

    # Get a specific category by ID
    category(id: ID!): Category
  }

  # Extend the root Mutation type
  extend type Mutation {
    # Create a new category
    createCategory(input: CreateCategoryInput!): Category!

    # Update an existing category
    updateCategory(id: ID!, input: UpdateCategoryInput!): Category!

    # Delete a category (soft delete by archiving)
    deleteCategory(id: ID!): Boolean!
  }
`; 