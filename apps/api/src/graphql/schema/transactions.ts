// Transactions GraphQL schema
import { gql } from 'graphql-tag';

export const transactionTypeDefs = gql`
  # Transaction type enum
  enum TransactionType {
    INCOME
    EXPENSE
    TRANSFER
  }

  # Transaction entry type enum (for double-entry accounting)
  enum TransactionEntryType {
    DEBIT
    CREDIT
  }

  # Transaction status enum
  enum TransactionStatus {
    PENDING
    COMPLETED
    CANCELLED
    RECONCILED
  }

  # Recurring pattern type enum
  enum RecurringPatternType {
    DAILY
    WEEKLY
    MONTHLY
    YEARLY
  }

  # Recurring pattern type
  type RecurringPattern {
    type: RecurringPatternType!
    interval: Int!
    dayOfWeek: Int
    dayOfMonth: Int
    monthOfYear: Int
    endDate: String
    occurrences: Int
  }

  # Journal line type (for double-entry accounting)
  type JournalLine {
    id: ID!
    journalEntryId: ID!
    accountId: ID!
    categoryId: ID
    amount: Float!
    type: TransactionEntryType!
    notes: String
    createdAt: String!
    updatedAt: String!
    
    # Populated relations
    account: Account
    category: Category
  }

  # Transaction type
  type Transaction {
    id: ID!
    userId: ID!
    description: String!
    date: String!
    notes: String
    isRecurring: Boolean!
    recurringPattern: RecurringPattern
    status: TransactionStatus!
    createdAt: String!
    updatedAt: String!
    
    # Journal lines (double-entry accounting)
    journalLines: [JournalLine!]!
    
    # Computed fields
    amount: Float!
    type: TransactionType!
  }

  # Simple transaction view for income/expense
  type SimpleTransaction {
    id: ID!
    userId: ID!
    description: String!
    amount: Float!
    type: TransactionType!
    date: String!
    notes: String
    status: TransactionStatus!
    isRecurring: Boolean!
    
    # Primary account and category
    account: Account!
    category: Category
    
    createdAt: String!
    updatedAt: String!
  }

  # Journal line input for creating transaction entries
  input JournalLineInput {
    accountId: ID!
    categoryId: ID
    amount: Float!
    type: TransactionEntryType!
    notes: String
  }

  # Recurring pattern input
  input RecurringPatternInput {
    type: RecurringPatternType!
    interval: Int!
    dayOfWeek: Int
    dayOfMonth: Int
    monthOfYear: Int
    endDate: String
    occurrences: Int
  }

  # Create transaction input
  input CreateTransactionInput {
    description: String!
    amount: Float!
    date: String
    notes: String
    
    # For simple income/expense transactions
    accountId: ID
    categoryId: ID
    type: TransactionType
    
    # For complex transactions or transfers (manual journal entries)
    journalLines: [JournalLineInput!]
    
    # Recurring transaction fields
    isRecurring: Boolean = false
    recurringPattern: RecurringPatternInput
  }

  # Update transaction input
  input UpdateTransactionInput {
    description: String
    date: String
    notes: String
    status: TransactionStatus
    
    # Recurring transaction updates
    isRecurring: Boolean
    recurringPattern: RecurringPatternInput
  }

  # Transaction filter input
  input TransactionFilterInput {
    accountId: ID
    categoryId: ID
    type: TransactionType
    status: TransactionStatus
    startDate: String
    endDate: String
    minAmount: Float
    maxAmount: Float
    search: String
    isRecurring: Boolean
  }

  # Extend the root Query type
  extend type Query {
    # Get all transactions for the authenticated user
    transactions(filter: TransactionFilterInput, limit: Int = 50, offset: Int = 0): [Transaction!]!

    # Get a specific transaction by ID
    transaction(id: ID!): Transaction

    # Get simple transaction view (for income/expense only)
    simpleTransaction(id: ID!): SimpleTransaction
  }

  # Extend the root Mutation type
  extend type Mutation {
    # Create a new transaction
    createTransaction(input: CreateTransactionInput!): Transaction!

    # Update an existing transaction
    updateTransaction(id: ID!, input: UpdateTransactionInput!): Transaction!

    # Delete a transaction (soft delete by setting status to cancelled)
    deleteTransaction(id: ID!): Boolean!
  }
`; 