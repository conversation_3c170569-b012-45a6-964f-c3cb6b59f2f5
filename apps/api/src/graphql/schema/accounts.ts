// Accounts GraphQL schema
import { gql } from 'graphql-tag';

export const accountTypeDefs = gql`
  # Account type enum
  enum AccountType {
    CHECKING
    SAVINGS
    CREDIT_CARD
    CASH
    INVESTMENT
    LOAN
    ASSET
    LIABILITY
  }

  # Account type
  type Account {
    id: ID!
    userId: ID!
    name: String!
    type: AccountType!
    currency: String!
    initialBalance: Float!
    currentBalance: Float!
    isArchived: Boolean!
    notes: String
    icon: String
    color: String
    includeInNetWorth: Boolean!
    displayOrder: Int!
    createdAt: String!
    updatedAt: String!
  }

  # Create account input
  input CreateAccountInput {
    name: String!
    type: AccountType!
    currency: String = "USD"
    initialBalance: Float = 0
    notes: String
    icon: String
    color: String
    includeInNetWorth: Boolean = true
    displayOrder: Int = 0
  }

  # Update account input
  input UpdateAccountInput {
    name: String
    type: AccountType
    currency: String
    notes: String
    icon: String
    color: String
    includeInNetWorth: Boolean
    displayOrder: Int
    isArchived: Boolean
  }

  # Extend the root Query type
  extend type Query {
    # Get all accounts for the authenticated user
    accounts: [Account!]!

    # Get a specific account by ID
    account(id: ID!): Account
  }

  # Extend the root Mutation type
  extend type Mutation {
    # Create a new account
    createAccount(input: CreateAccountInput!): Account!

    # Update an existing account
    updateAccount(id: ID!, input: UpdateAccountInput!): Account!

    # Delete an account (soft delete by archiving)
    deleteAccount(id: ID!): Boolean!
  }
`; 