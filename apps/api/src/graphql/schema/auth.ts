// Authentication GraphQL schema
import { gql } from 'graphql-tag';

export const authTypeDefs = gql`
  # User type
  type User {
    id: ID!
    email: String!
    firstName: String
    lastName: String
    role: String!
    emailVerified: Boolean!
    createdAt: String!
    updatedAt: String!
  }

  # Authentication payload
  type AuthPayload {
    token: String!
    user: User!
  }

  # Registration input
  input RegisterInput {
    email: String!
    password: String!
    firstName: String
    lastName: String
  }

  # Login input
  input LoginInput {
    email: String!
    password: String!
  }

  # Extend the root Query type
  extend type Query {
    # Get the current authenticated user
    me: User
  }

  # Extend the root Mutation type
  extend type Mutation {
    # Register a new user
    register(input: RegisterInput!): AuthPayload!

    # Login with email and password
    login(input: LoginInput!): AuthPayload

    # Refresh the authentication token
    refreshToken: AuthPayload!

    # Logout the current user
    logout: Boolean!
  }
`;
