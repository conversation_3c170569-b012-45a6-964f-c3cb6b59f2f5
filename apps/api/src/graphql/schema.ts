import { makeExecutableSchema } from '@graphql-tools/schema';
import { gql } from 'graphql-tag';

// Define a simple schema for now
const typeDefs = gql`
  type Query {
    hello: String!
  }
`;

// Define simple resolvers
const resolvers = {
  Query: {
    hello: () => 'Hello from BudApp API!',
  },
};

// Create executable schema
export const schema = makeExecutableSchema({
  typeDefs,
  resolvers,
});
