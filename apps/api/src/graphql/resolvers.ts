import { ZodError } from 'zod';
import { generateToken } from '../lib/auth/jwt.js';
import {
  getUserById,
  loginUser,
  registerUser,
} from '../services/auth/auth.service.js';
import {
  type LoginInput,
  type RegisterInput,
  loginInputSchema,
  registerInputSchema,
} from '../services/auth/auth.types.js';
import {
  createAccount,
  deleteAccount,
  getAccountById,
  getUserAccounts,
  updateAccount,
} from '../services/accounts/accounts.service.js';
import {
  createAccountInputSchema,
  updateAccountInputSchema,
  type CreateAccountInput,
  type UpdateAccountInput,
  type AccountResponse,
} from '../services/accounts/accounts.types.js';
import {
  createCategory,
  deleteCategory,
  getCategoryById,
  getUserCategories,
  getCategoryTree,
  updateCategory,
} from '../services/categories/categories.service.js';
import {
  createCategoryInputSchema,
  updateCategoryInputSchema,
  categoryFilterInputSchema,
  type CreateCategoryInput,
  type UpdateCategoryInput,
  type CategoryFilterInput,
} from '../services/categories/categories.types.js';
import {
  createTransaction,
  deleteTransaction,
  getTransactionById,
  getUserTransactions,
  updateTransaction,
} from '../services/transactions/transactions.service.js';
import {
  createTransactionInputSchema,
  updateTransactionInputSchema,
  transactionFilterInputSchema,
  type CreateTransactionInput,
  type UpdateTransactionInput,
  type TransactionFilterInput,
  type TransactionResponse,
} from '../services/transactions/transactions.types.js';
import {
  AppError,
  AuthenticationError,
  DatabaseError,
  ValidationError,
  formatZodError,
} from '../utils/error-handler.js';
import { logger } from '../utils/logger.js';

// GraphQL resolver context type
interface GraphQLContext {
  userId?: string;
  user?: unknown;
}

// Transform GraphQL enum values to database format
const transformAccountType = (graphqlType: string): string => {
  const typeMap: Record<string, string> = {
    'CHECKING': 'checking',
    'SAVINGS': 'savings',
    'CREDIT_CARD': 'credit_card',
    'CASH': 'cash',
    'INVESTMENT': 'investment',
    'LOAN': 'loan',
    'ASSET': 'asset',
    'LIABILITY': 'liability',
  };
  return typeMap[graphqlType] || graphqlType.toLowerCase();
};

// Transform account input from GraphQL to service format
const transformAccountInput = (input: CreateAccountInput | UpdateAccountInput) => {
  if (input.type) {
    return {
      ...input,
      type: transformAccountType(input.type),
    };
  }
  return input;
};

// Transform database enum values to GraphQL format
const transformAccountTypeToGraphQL = (dbType: string): string => {
  const typeMap: Record<string, string> = {
    'checking': 'CHECKING',
    'savings': 'SAVINGS',
    'credit_card': 'CREDIT_CARD',
    'cash': 'CASH',
    'investment': 'INVESTMENT',
    'loan': 'LOAN',
    'asset': 'ASSET',
    'liability': 'LIABILITY',
  };
  return typeMap[dbType] || dbType.toUpperCase();
};

// Transform account output from service to GraphQL format
const transformAccountOutput = (account: AccountResponse) => {
  return {
    ...account,
    type: transformAccountTypeToGraphQL(account.type),
    initialBalance: Number.parseFloat(account.initialBalance),
    currentBalance: Number.parseFloat(account.currentBalance),
    createdAt: account.createdAt.toISOString(),
    updatedAt: account.updatedAt.toISOString(),
  };
};

// Transform category output from service to GraphQL format
const transformCategoryOutput = (category: any) => {
  const transformed = {
    ...category,
    createdAt: category.createdAt ? category.createdAt.toISOString() : new Date().toISOString(),
    updatedAt: category.updatedAt ? category.updatedAt.toISOString() : new Date().toISOString(),
  };
  
  // Transform children if they exist
  if (category.children) {
    transformed.children = category.children.map(transformCategoryOutput);
  }
  
  // Transform parent if it exists
  if (category.parent) {
    transformed.parent = transformCategoryOutput(category.parent);
  }
  
  return transformed;
};

// Transform GraphQL transaction enum values to database format
const transformTransactionType = (graphqlType: string): string => {
  const typeMap: Record<string, string> = {
    'INCOME': 'income',
    'EXPENSE': 'expense',
    'TRANSFER': 'transfer',
  };
  return typeMap[graphqlType] || graphqlType.toLowerCase();
};

const transformTransactionEntryType = (graphqlType: string): string => {
  const typeMap: Record<string, string> = {
    'DEBIT': 'debit',
    'CREDIT': 'credit',
  };
  return typeMap[graphqlType] || graphqlType.toLowerCase();
};

const transformTransactionStatus = (graphqlStatus: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': 'pending',
    'COMPLETED': 'completed',
    'CANCELLED': 'cancelled',
    'RECONCILED': 'reconciled',
  };
  return statusMap[graphqlStatus] || graphqlStatus.toLowerCase();
};

// Transform database enum values to GraphQL format
const transformTransactionTypeToGraphQL = (dbType: string): string => {
  const typeMap: Record<string, string> = {
    'income': 'INCOME',
    'expense': 'EXPENSE',
    'transfer': 'TRANSFER',
  };
  return typeMap[dbType] || dbType.toUpperCase();
};

const transformTransactionEntryTypeToGraphQL = (dbType: string): string => {
  const typeMap: Record<string, string> = {
    'debit': 'DEBIT',
    'credit': 'CREDIT',
  };
  return typeMap[dbType] || dbType.toUpperCase();
};

const transformTransactionStatusToGraphQL = (dbStatus: string): string => {
  const statusMap: Record<string, string> = {
    'pending': 'PENDING',
    'completed': 'COMPLETED',
    'cancelled': 'CANCELLED',
    'reconciled': 'RECONCILED',
  };
  return statusMap[dbStatus] || dbStatus.toUpperCase();
};

// Transform transaction input from GraphQL to service format
const transformTransactionInput = (input: GraphQLCreateTransactionInput | GraphQLUpdateTransactionInput): any => {
  const transformed: any = { ...input };
  
  // Convert null values to undefined for Zod validation
  Object.keys(transformed).forEach(key => {
    if (transformed[key] === null) {
      transformed[key] = undefined;
    }
  });
  
  if ('type' in input && input.type) {
    transformed.type = transformTransactionType(input.type);
  }
  
  if ('status' in input && input.status) {
    transformed.status = transformTransactionStatus(input.status);
  }
  
  if ('journalLines' in input && input.journalLines) {
    transformed.journalLines = input.journalLines.map((line: GraphQLJournalLineInput) => ({
      ...line,
      type: transformTransactionEntryType(line.type),
    }));
  }
  
  // Convert date string to Date object if provided
  if (input.date && typeof input.date === 'string') {
    transformed.date = new Date(input.date);
  }
  
  return transformed;
};

// Transform transaction output from service to GraphQL format
const transformTransactionOutput = (transaction: TransactionResponse) => {
  const result: any = {
    id: transaction.id,
    userId: transaction.userId,
    description: transaction.description,
    type: transformTransactionTypeToGraphQL(transaction.type),
    status: transformTransactionStatusToGraphQL(transaction.status),
    date: transaction.date.toISOString(),
    createdAt: transaction.createdAt.toISOString(),
    updatedAt: transaction.updatedAt.toISOString(),
    amount: Number.parseFloat(transaction.amount),
    isRecurring: transaction.isRecurring,
    notes: transaction.notes || null,
    recurringPattern: transaction.recurringPattern || null,
    journalLines: transaction.journalLines.map(line => {
      const transformedLine: any = {
        id: line.id,
        journalEntryId: line.journalEntryId,
        accountId: line.accountId,
        categoryId: line.categoryId || null,
        amount: Number.parseFloat(line.amount),
        type: transformTransactionEntryTypeToGraphQL(line.type),
        notes: line.notes || null,
        createdAt: line.createdAt.toISOString(),
        updatedAt: line.updatedAt.toISOString(),
        account: line.account ? {
          ...line.account,
          type: transformAccountTypeToGraphQL(line.account.type),
        } : null,
        category: line.category ? {
          ...line.category,
        } : null,
      };
      
      return transformedLine;
    }),
  };
  
  return result;
};

// GraphQL resolver argument types
interface ResolverArgs {
  [key: string]: unknown;
}

// GraphQL Transaction Input Types
interface GraphQLJournalLineInput {
  accountId: string;
  categoryId?: string;
  amount: number;
  type: string;
  notes?: string;
}

interface GraphQLRecurringPatternInput {
  type: string;
  interval: number;
  dayOfWeek?: number;
  dayOfMonth?: number;
  monthOfYear?: number;
  endDate?: string;
  occurrences?: number;
}

interface GraphQLCreateTransactionInput {
  description: string;
  amount: number;
  date?: string;
  notes?: string;
  accountId?: string;
  categoryId?: string;
  type?: string;
  journalLines?: GraphQLJournalLineInput[];
  isRecurring?: boolean;
  recurringPattern?: GraphQLRecurringPatternInput;
}

interface GraphQLUpdateTransactionInput {
  description?: string;
  date?: string;
  notes?: string;
  status?: string;
  isRecurring?: boolean;
  recurringPattern?: GraphQLRecurringPatternInput;
}

interface GraphQLTransactionFilterInput {
  accountId?: string;
  categoryId?: string;
  type?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
  isRecurring?: boolean;
}

// Export resolvers
export const resolvers = {
  Query: {
    hello: () => 'Hello from BudApp API!',
    me: async (_: unknown, __: ResolverArgs, context: GraphQLContext) => {
      const { userId } = context;

      if (!userId) {
        return null;
      }

      return getUserById(userId);
    },
    // Account queries
    accounts: async (_: unknown, __: ResolverArgs, context: GraphQLContext) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        const accounts = await getUserAccounts(userId);
        return accounts.map(transformAccountOutput);
      } catch (error) {
        logger.error('Error fetching accounts:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching accounts');
      }
    },
    account: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        const account = await getAccountById(id, userId);
        return account ? transformAccountOutput(account) : null;
      } catch (error) {
        logger.error('Error fetching account:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching account');
      }
    },
    // Category queries
    categories: async (
      _: unknown,
      { filter }: { filter?: CategoryFilterInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate filter if provided
        const validatedFilter = filter ? categoryFilterInputSchema.parse(filter) : undefined;
        
        const categories = await getUserCategories(userId, validatedFilter);
        return categories.map(transformCategoryOutput);
      } catch (error) {
        logger.error('Error fetching categories:', error);
        
        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching categories');
      }
    },
    categoryTree: async (
      _: unknown,
      { filter }: { filter?: CategoryFilterInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate filter if provided
        const validatedFilter = filter ? categoryFilterInputSchema.parse(filter) : undefined;
        
        const categoryTree = await getCategoryTree(userId, validatedFilter);
        return categoryTree.map(transformCategoryOutput);
      } catch (error) {
        logger.error('Error fetching category tree:', error);
        
        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching category tree');
      }
    },
    category: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        const category = await getCategoryById(id, userId);
        return category ? transformCategoryOutput(category) : null;
      } catch (error) {
        logger.error('Error fetching category:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching category');
      }
    },
    // Transaction queries
    transactions: async (
      _: unknown,
      { filter, limit = 50, offset = 0 }: { filter?: GraphQLTransactionFilterInput; limit?: number; offset?: number },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Preprocess filter to convert null values to undefined for Zod validation
        let validatedFilter: any = undefined;
        if (filter) {
          const cleanedFilter = Object.fromEntries(
            Object.entries(filter).filter(([_, value]) => value !== null)
          );
          
          // Convert date strings to Date objects for Zod validation
          if (cleanedFilter.startDate && typeof cleanedFilter.startDate === 'string') {
            cleanedFilter.startDate = new Date(cleanedFilter.startDate);
          }
          if (cleanedFilter.endDate && typeof cleanedFilter.endDate === 'string') {
            cleanedFilter.endDate = new Date(cleanedFilter.endDate);
          }
          
          validatedFilter = Object.keys(cleanedFilter).length > 0 
            ? transactionFilterInputSchema.parse(cleanedFilter) 
            : undefined;
        }
        
        const transactions = await getUserTransactions(userId, validatedFilter, limit, offset);
        return transactions.map(transformTransactionOutput);
      } catch (error) {
        logger.error('Error fetching transactions:', error);
        
        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching transactions');
      }
    },
    transaction: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        const transaction = await getTransactionById(id, userId);
        return transaction ? transformTransactionOutput(transaction) : null;
      } catch (error) {
        logger.error('Error fetching transaction:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching transaction');
      }
    },
  },
  Mutation: {
    register: async (_: unknown, { input }: { input: RegisterInput }) => {
      try {
        // Validate input
        const validatedInput = registerInputSchema.parse(input);

        // Register user
        const { user, token } = await registerUser(
          validatedInput.email,
          validatedInput.password,
          validatedInput.firstName,
          validatedInput.lastName
        );

        return { user, token };
      } catch (error) {
        // Log the error for debugging
        logger.error('Registration error:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        logger.error('Unknown registration error:', error);
        throw new DatabaseError('Error registering user');
      }
    },
    login: async (_: unknown, { input }: { input: LoginInput }) => {
      try {
        // Validate input
        const validatedInput = loginInputSchema.parse(input);

        // Login user
        const { user, token } = await loginUser(
          validatedInput.email,
          validatedInput.password
        );

        return { user, token };
      } catch (error) {
        // Log the error for debugging
        logger.error('Login error:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        logger.error('Unknown login error:', error);
        throw new AuthenticationError('Error logging in user');
      }
    },
    refreshToken: async (
      _: unknown,
      __: ResolverArgs,
      context: GraphQLContext
    ) => {
      try {
        const { userId } = context;

        if (!userId) {
          throw new AuthenticationError('Not authenticated');
        }

        const user = await getUserById(userId);

        if (!user) {
          throw new AuthenticationError('User not found');
        }

        // Generate a new access token
        const token = generateToken({
          userId: user.id,
          email: user.email,
          role: user.role,
        });

        return {
          user,
          token,
        };
      } catch (error) {
        // Log the error for debugging
        logger.error('Refresh token error:', error);

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        logger.error('Unknown refresh token error:', error);
        throw new AuthenticationError('Error refreshing token');
      }
    },
    logout: async () => {
      // In a real implementation, we would invalidate the token
      // or remove it from a whitelist

      return true;
    },
    // Account mutations
    createAccount: async (
      _: unknown,
      { input }: { input: CreateAccountInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Transform GraphQL input to service format
        const transformedInput = transformAccountInput(input);
        
        // Validate input
        const validatedInput = createAccountInputSchema.parse(transformedInput);

        // Create account
        const account = await createAccount(userId, validatedInput);
        return transformAccountOutput(account);
      } catch (error) {
        logger.error('Error creating account:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error creating account');
      }
    },
    updateAccount: async (
      _: unknown,
      { id, input }: { id: string; input: UpdateAccountInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Transform GraphQL input to service format
        const transformedInput = transformAccountInput(input);
        
        // Validate input
        const validatedInput = updateAccountInputSchema.parse(transformedInput);

        // Update account
        const account = await updateAccount(id, userId, validatedInput);
        return transformAccountOutput(account);
      } catch (error) {
        logger.error('Error updating account:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error updating account');
      }
    },
    deleteAccount: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await deleteAccount(id, userId);
      } catch (error) {
        logger.error('Error deleting account:', error);

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error deleting account');
      }
    },
    // Category mutations
    createCategory: async (
      _: unknown,
      { input }: { input: CreateCategoryInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate input
        const validatedInput = createCategoryInputSchema.parse(input);

        // Create category
        const category = await createCategory(userId, validatedInput);
        return transformCategoryOutput(category);
      } catch (error) {
        logger.error('Error creating category:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error creating category');
      }
    },
    updateCategory: async (
      _: unknown,
      { id, input }: { id: string; input: UpdateCategoryInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate input
        const validatedInput = updateCategoryInputSchema.parse(input);

        // Update category
        const category = await updateCategory(id, userId, validatedInput);
        return transformCategoryOutput(category);
      } catch (error) {
        logger.error('Error updating category:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error updating category');
      }
    },
    deleteCategory: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await deleteCategory(id, userId);
      } catch (error) {
        logger.error('Error deleting category:', error);

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error deleting category');
      }
    },
    // Transaction mutations
    createTransaction: async (
      _: unknown,
      { input }: { input: GraphQLCreateTransactionInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        logger.info('Creating transaction with input:', input);
        
        // Transform GraphQL input to service format
        const transformedInput = transformTransactionInput(input);
        logger.info('Transformed input:', transformedInput);
        
        // Validate input
        const validatedInput = createTransactionInputSchema.parse(transformedInput);
        logger.info('Validated input:', validatedInput);

        // Create transaction
        const transaction = await createTransaction(userId, validatedInput);
        const result = transformTransactionOutput(transaction);
        logger.info('Returning transaction result:', JSON.stringify(result, null, 2));
        return result;
      } catch (error) {
        logger.error('Error creating transaction:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error creating transaction');
      }
    },
    updateTransaction: async (
      _: unknown,
      { id, input }: { id: string; input: GraphQLUpdateTransactionInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Transform GraphQL input to service format
        const transformedInput = transformTransactionInput(input);
        
        // Validate input
        const validatedInput = updateTransactionInputSchema.parse(transformedInput);

        // Update transaction
        const transaction = await updateTransaction(id, userId, validatedInput);
        return transformTransactionOutput(transaction);
      } catch (error) {
        logger.error('Error updating transaction:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error updating transaction');
      }
    },
    deleteTransaction: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await deleteTransaction(id, userId);
      } catch (error) {
        logger.error('Error deleting transaction:', error);

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error deleting transaction');
      }
    },
  },
};
