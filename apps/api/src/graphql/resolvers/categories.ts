// Category resolvers
import { ZodError } from 'zod';
import {
  createCate<PERSON><PERSON>,
  deleteCategory,
  getCategoryById,
  getUserCategories,
  getCategoryTree,
  updateCategory,
} from '../../services/categories/categories.service.js';
import {
  createCategoryInputSchema,
  updateCategoryInputSchema,
  categoryFilterInputSchema,
  type CreateCategoryInput,
  type UpdateCategoryInput,
  type CategoryFilterInput,
} from '../../services/categories/categories.types.js';
import {
  AppError,
  AuthenticationError,
  DatabaseError,
  formatZodError,
} from '../../utils/error-handler.js';
import { logger } from '../../utils/logger.js';

// GraphQL resolver context type
interface GraphQLContext {
  userId?: string;
  user?: unknown;
}

// GraphQL resolver argument types
interface ResolverArgs {
  [key: string]: unknown;
}

export const categoryResolvers = {
  Query: {
    // Get all categories for the authenticated user
    categories: async (
      _: unknown,
      { filter }: { filter?: CategoryFilterInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate filter if provided
        const validatedFilter = filter ? categoryFilterInputSchema.parse(filter) : undefined;
        
        return await getUserCategories(userId, validatedFilter);
      } catch (error) {
        logger.error('Error fetching categories:', error);
        
        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching categories');
      }
    },

    // Get categories organized in a hierarchical tree structure
    categoryTree: async (
      _: unknown,
      { filter }: { filter?: CategoryFilterInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate filter if provided
        const validatedFilter = filter ? categoryFilterInputSchema.parse(filter) : undefined;
        
        return await getCategoryTree(userId, validatedFilter);
      } catch (error) {
        logger.error('Error fetching category tree:', error);
        
        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching category tree');
      }
    },

    // Get a specific category by ID
    category: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await getCategoryById(id, userId);
      } catch (error) {
        logger.error('Error fetching category:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching category');
      }
    },
  },

  Mutation: {
    // Create a new category
    createCategory: async (
      _: unknown,
      { input }: { input: CreateCategoryInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate input
        const validatedInput = createCategoryInputSchema.parse(input);

        // Create category
        return await createCategory(userId, validatedInput);
      } catch (error) {
        logger.error('Error creating category:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error creating category');
      }
    },

    // Update an existing category
    updateCategory: async (
      _: unknown,
      { id, input }: { id: string; input: UpdateCategoryInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate input
        const validatedInput = updateCategoryInputSchema.parse(input);

        // Update category
        return await updateCategory(id, userId, validatedInput);
      } catch (error) {
        logger.error('Error updating category:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error updating category');
      }
    },

    // Delete a category (soft delete by archiving)
    deleteCategory: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await deleteCategory(id, userId);
      } catch (error) {
        logger.error('Error deleting category:', error);

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error deleting category');
      }
    },
  },
}; 