import { extractTokenFromHeader, verifyToken } from '../../lib/auth/jwt.js';
// Authentication resolvers
import {
  getUserById,
  loginUser,
  registerUser,
  validateJwtPayload,
} from '../../services/auth/auth.service.js';
import {
  type LoginInput,
  type RegisterInput,
  loginInputSchema,
  registerInputSchema,
} from '../../services/auth/auth.types.js';

// GraphQL resolver context type
interface GraphQLContext {
  userId?: string;
  user?: unknown;
}

// GraphQL resolver argument types
interface ResolverArgs {
  [key: string]: unknown;
}

// Request type for auth context
interface AuthRequest {
  req?: {
    headers?: {
      authorization?: string;
    };
  };
}

export const authResolvers = {
  Query: {
    // Get the current authenticated user
    me: async (_: unknown, __: ResolverArgs, context: GraphQLContext) => {
      const { userId } = context;

      if (!userId) {
        return null;
      }

      return getUserById(userId);
    },
  },

  Mutation: {
    // Register a new user
    register: async (_: unknown, { input }: { input: RegisterInput }) => {
      // Validate input
      const validatedInput = registerInputSchema.parse(input);

      // Register user
      const { user, token } = await registerUser(
        validatedInput.email,
        validatedInput.password,
        validatedInput.firstName,
        validatedInput.lastName
      );

      return { user, token };
    },

    // Login with email and password
    login: async (_: unknown, { input }: { input: LoginInput }) => {
      // Validate input
      const validatedInput = loginInputSchema.parse(input);

      // Login user
      const { user, token } = await loginUser(
        validatedInput.email,
        validatedInput.password
      );

      return { user, token };
    },

    // Refresh the authentication token
    refreshToken: async (
      _: unknown,
      __: ResolverArgs,
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new Error('Not authenticated');
      }

      const user = await getUserById(userId);

      if (!user) {
        throw new Error('User not found');
      }

      // In a real implementation, we would validate the refresh token
      // and generate a new access token

      return {
        user,
        token: 'mock-refreshed-token',
      };
    },

    // Logout the current user
    logout: async () => {
      // In a real implementation, we would invalidate the token
      // or remove it from a whitelist

      return true;
    },
  },
};

// Context middleware for authentication
export const authContext = async (request: AuthRequest) => {
  try {
    // Check if req exists and has headers
    if (!request || !request.req || !request.req.headers) {
      console.log('Request object is missing or malformed:', request);
      return { userId: null };
    }

    // Extract token from Authorization header
    const token = extractTokenFromHeader(request.req.headers.authorization);

    if (!token) {
      return { userId: null };
    }

    // Verify token
    const payload = verifyToken(token);

    if (!payload) {
      return { userId: null };
    }

    // Validate payload
    const isValid = await validateJwtPayload(payload);

    if (!isValid) {
      return { userId: null };
    }

    return { userId: payload.userId };
  } catch (error) {
    console.error('Error in auth context:', error);
    return { userId: null };
  }
};
