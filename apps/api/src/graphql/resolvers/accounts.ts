// Account resolvers
import { ZodError } from 'zod';
import {
  createAccount,
  deleteAccount,
  getAccountById,
  getUserAccounts,
  updateAccount,
} from '../../services/accounts/accounts.service.js';
import {
  createAccountInputSchema,
  updateAccountInputSchema,
  type CreateAccountInput,
  type UpdateAccountInput,
} from '../../services/accounts/accounts.types.js';
import {
  AppError,
  AuthenticationError,
  DatabaseError,
  formatZodError,
} from '../../utils/error-handler.js';
import { logger } from '../../utils/logger.js';

// GraphQL resolver context type
interface GraphQLContext {
  userId?: string;
  user?: unknown;
}

// GraphQL resolver argument types
interface ResolverArgs {
  [key: string]: unknown;
}

export const accountResolvers = {
  Query: {
    // Get all accounts for the authenticated user
    accounts: async (_: unknown, __: ResolverArgs, context: GraphQLContext) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await getUserAccounts(userId);
      } catch (error) {
        logger.error('Error fetching accounts:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching accounts');
      }
    },

    // Get a specific account by ID
    account: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await getAccountById(id, userId);
      } catch (error) {
        logger.error('Error fetching account:', error);
        
        if (error instanceof AppError) {
          throw error;
        }
        
        throw new DatabaseError('Error fetching account');
      }
    },
  },

  Mutation: {
    // Create a new account
    createAccount: async (
      _: unknown,
      { input }: { input: CreateAccountInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate input
        const validatedInput = createAccountInputSchema.parse(input);

        // Create account
        return await createAccount(userId, validatedInput);
      } catch (error) {
        logger.error('Error creating account:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error creating account');
      }
    },

    // Update an existing account
    updateAccount: async (
      _: unknown,
      { id, input }: { id: string; input: UpdateAccountInput },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        // Validate input
        const validatedInput = updateAccountInputSchema.parse(input);

        // Update account
        return await updateAccount(id, userId, validatedInput);
      } catch (error) {
        logger.error('Error updating account:', error);

        // Handle Zod validation errors
        if (error instanceof ZodError) {
          throw formatZodError(error);
        }

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error updating account');
      }
    },

    // Delete an account (soft delete by archiving)
    deleteAccount: async (
      _: unknown,
      { id }: { id: string },
      context: GraphQLContext
    ) => {
      const { userId } = context;

      if (!userId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        return await deleteAccount(id, userId);
      } catch (error) {
        logger.error('Error deleting account:', error);

        // Handle known AppErrors
        if (error instanceof AppError) {
          throw error;
        }

        // Handle unknown errors
        throw new DatabaseError('Error deleting account');
      }
    },
  },
}; 