import { GraphQLError } from 'graphql';
import { ZodError } from 'zod';
// Error handling utilities for the API
import { logger } from './logger.js';

// Type definitions for error handling
interface ErrorDetails {
  [key: string]: unknown;
}

interface GraphQLErrorWithExtensions {
  message: string;
  locations?: unknown[];
  path?: unknown[];
  extensions?: {
    code?: string;
    statusCode?: number;
    details?: ErrorDetails;
  };
  originalError?: Error;
}

interface ExecutionResult {
  data?: unknown;
  errors?: GraphQLErrorWithExtensions[];
}

interface MercuriusExecutionResult {
  statusCode?: number;
  response?: {
    data?: unknown;
    errors?: Array<{
      message: string;
      locations?: unknown[];
      path?: unknown[];
      extensions: {
        code: string;
        statusCode: number;
        details?: ErrorDetails;
      };
    }>;
  };
  data?: unknown;
  errors?: GraphQLErrorWithExtensions[];
}

// Error codes for different types of errors
export enum ErrorCode {
  // Authentication errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  UNAUTHORIZED = 'UNAUTHORIZED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  WEAK_PASSWORD = 'WEAK_PASSWORD',
  OAUTH_ERROR = 'OAUTH_ERROR',

  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',

  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',

  // Server errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',

  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',

  // Unknown errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Base error class
export class AppError extends Error {
  code: ErrorCode;
  statusCode: number;
  details?: ErrorDetails;

  constructor(
    message: string,
    code: ErrorCode,
    statusCode = 500,
    details?: ErrorDetails
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  // Convert to GraphQL error
  toGraphQLError(): GraphQLError {
    return new GraphQLError(this.message, {
      extensions: {
        code: this.code,
        statusCode: this.statusCode,
        details: this.details,
      },
    });
  }
}

// Authentication errors
export class AuthenticationError extends AppError {
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNAUTHORIZED,
    details?: ErrorDetails
  ) {
    super(message, code, 401, details);
  }
}

export class InvalidCredentialsError extends AuthenticationError {
  constructor(message = 'Invalid email or password', details?: ErrorDetails) {
    super(message, ErrorCode.INVALID_CREDENTIALS, details);
  }
}

export class TokenExpiredError extends AuthenticationError {
  constructor(
    message = 'Authentication token has expired',
    details?: ErrorDetails
  ) {
    super(message, ErrorCode.TOKEN_EXPIRED, details);
  }
}

export class InvalidTokenError extends AuthenticationError {
  constructor(
    message = 'Invalid authentication token',
    details?: ErrorDetails
  ) {
    super(message, ErrorCode.INVALID_TOKEN, details);
  }
}

export class UserNotFoundError extends AuthenticationError {
  constructor(message = 'User not found', details?: ErrorDetails) {
    super(message, ErrorCode.USER_NOT_FOUND, details);
  }
}

export class EmailAlreadyExistsError extends AppError {
  constructor(message = 'Email already exists', details?: ErrorDetails) {
    super(message, ErrorCode.EMAIL_ALREADY_EXISTS, 409, details);
  }
}

export class WeakPasswordError extends AppError {
  constructor(
    message = 'Password does not meet security requirements',
    details?: ErrorDetails
  ) {
    super(message, ErrorCode.WEAK_PASSWORD, 400, details);
  }
}

export class OAuthError extends AuthenticationError {
  constructor(message = 'OAuth authentication failed', details?: ErrorDetails) {
    super(message, ErrorCode.OAUTH_ERROR, details);
  }
}

// Database errors
export class DatabaseError extends AppError {
  constructor(message = 'Database operation failed', details?: ErrorDetails) {
    super(message, ErrorCode.DATABASE_ERROR, 500, details);
  }
}

export class RecordNotFoundError extends AppError {
  constructor(message = 'Record not found', details?: ErrorDetails) {
    super(message, ErrorCode.RECORD_NOT_FOUND, 404, details);
  }
}

// Validation errors
export class ValidationError extends AppError {
  constructor(message = 'Validation failed', details?: ErrorDetails) {
    super(message, ErrorCode.VALIDATION_ERROR, 400, details);
  }
}

// Format Zod validation errors
export function formatZodError(error: ZodError): ValidationError {
  const formattedErrors = error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message,
  }));

  return new ValidationError('Validation failed', { errors: formattedErrors });
}

// Error formatter for Mercurius
export function errorFormatter(
  execution: ExecutionResult,
  _context: unknown
): MercuriusExecutionResult {
  // For Mercurius, we need to return the errors in the correct format
  // The execution object contains the errors array and data
  if (!execution.errors || execution.errors.length === 0) {
    return execution;
  }

  const formattedErrors = execution.errors.map(
    (error: GraphQLErrorWithExtensions) => {
      // Extract the original error
      const originalError = error.originalError;

      // Log the error for debugging
      logger.error('GraphQL Error:', originalError || error);

      // If we have the original error and it's an AppError
      if (originalError instanceof AppError) {
        return {
          message: originalError.message,
          locations: error.locations,
          path: error.path,
          extensions: {
            code: originalError.code,
            statusCode: originalError.statusCode,
            details: originalError.details,
          },
        };
      }

      // Handle Zod validation errors
      if (originalError instanceof ZodError) {
        const validationError = formatZodError(originalError);
        return {
          message: validationError.message,
          locations: error.locations,
          path: error.path,
          extensions: {
            code: validationError.code,
            statusCode: validationError.statusCode,
            details: validationError.details,
          },
        };
      }

      // For other errors, preserve the original error structure
      // but add our custom extensions if needed
      const code =
        (error.extensions?.code as string) || ErrorCode.UNKNOWN_ERROR;
      const statusCode = getStatusCodeForErrorCode(code);

      // Mask sensitive information in error messages in production
      const isProduction = process.env.NODE_ENV === 'production';
      const safeMessage =
        isProduction && !isKnownErrorCode(code)
          ? 'An unexpected error occurred'
          : error.message;

      return {
        message: safeMessage,
        locations: error.locations,
        path: error.path,
        extensions: {
          ...error.extensions,
          code,
          statusCode,
        },
      };
    }
  );

  // Get the primary error for status code determination
  const primaryError = execution.errors[0];
  let statusCode = 500;

  if (primaryError.originalError instanceof AppError) {
    statusCode = primaryError.originalError.statusCode;
  } else if (primaryError.originalError instanceof ZodError) {
    statusCode = 400;
  } else if (primaryError.extensions?.code) {
    statusCode = getStatusCodeForErrorCode(primaryError.extensions.code);
  }

  // Return the expected structure for tests
  return {
    statusCode,
    response: {
      data: execution.data,
      errors: formattedErrors,
    },
  };
}

// Handle errors for Mercurius
export function handleErrors(
  errors: GraphQLErrorWithExtensions[],
  _request: unknown,
  _reply: unknown
): MercuriusExecutionResult {
  if (!errors || !errors.length) {
    return {
      statusCode: 500,
      response: {
        errors: [
          {
            message: 'Unknown error',
            extensions: {
              code: ErrorCode.UNKNOWN_ERROR,
              statusCode: 500,
            },
          },
        ],
      },
    };
  }

  const error = errors[0];

  // Log the error
  logger.error('GraphQL Error:', error);

  // If we have an AppError
  if (error.originalError && error.originalError instanceof AppError) {
    return {
      statusCode: error.originalError.statusCode,
      response: {
        errors: [
          {
            message: error.message,
            extensions: {
              code: error.originalError.code,
              statusCode: error.originalError.statusCode,
              details: error.originalError.details,
            },
          },
        ],
      },
    };
  }

  // For other errors, create a generic error response
  const code = (error.extensions?.code as string) || ErrorCode.UNKNOWN_ERROR;
  const statusCode = getStatusCodeForErrorCode(code);

  // Mask sensitive information in error messages in production
  const isProduction = process.env.NODE_ENV === 'production';
  const safeMessage =
    isProduction && !isKnownErrorCode(code)
      ? 'An unexpected error occurred'
      : error.message;

  return {
    statusCode,
    response: {
      errors: [
        {
          message: safeMessage,
          extensions: {
            code,
            statusCode,
          },
        },
      ],
    },
  };
}

// Check if error code is a known error code
function isKnownErrorCode(code: string): boolean {
  return Object.values(ErrorCode).includes(code as ErrorCode);
}

// Get HTTP status code for error code
function getStatusCodeForErrorCode(code: string): number {
  switch (code) {
    case ErrorCode.INVALID_CREDENTIALS:
    case ErrorCode.UNAUTHORIZED:
    case ErrorCode.TOKEN_EXPIRED:
    case ErrorCode.INVALID_TOKEN:
      return 401;
    case ErrorCode.USER_NOT_FOUND:
    case ErrorCode.RECORD_NOT_FOUND:
      return 404;
    case ErrorCode.EMAIL_ALREADY_EXISTS:
      return 409;
    case ErrorCode.VALIDATION_ERROR:
    case ErrorCode.WEAK_PASSWORD:
      return 400;
    default:
      return 500;
  }
}
