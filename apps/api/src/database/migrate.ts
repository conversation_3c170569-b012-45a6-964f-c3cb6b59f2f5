import { existsSync } from 'node:fs';
import { dirname, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { migrate as drizzleMigrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import * as schema from './schema.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Check if DATABASE_URL is already set in environment (e.g., from command line or CI)
const hasEnvDatabaseUrl = !!process.env.DATABASE_URL;

// Only load .env file if DATABASE_URL is not already set
if (!hasEnvDatabaseUrl) {
  // Try .env.test first (for testing), then .env (for development)
  const nodeEnv = process.env.NODE_ENV || 'development';
  const envFile = nodeEnv === 'test' ? '.env.test' : '.env';
  const envPath = resolve(__dirname, `../../${envFile}`);

  if (existsSync(envPath)) {
    config({ path: envPath });
  }
}

// Get the database URL from the environment
const databaseUrl = process.env.DATABASE_URL;

// Export the migrate function for use in other modules
export async function migrate(
  db: PostgresJsDatabase<typeof schema>,
  options: { migrationsFolder: string }
) {
  return drizzleMigrate(db, options);
}

// Run migrations
async function main() {
  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  console.log('Running migrations...');

  const client = postgres(databaseUrl, { max: 1 });
  const db = drizzle(client, { schema });

  // Use a relative path from the current file to the migrations folder
  const migrationsFolder = resolve(__dirname, '../../migrations');
  await drizzleMigrate(db, { migrationsFolder });

  console.log('Migrations completed successfully');
  process.exit(0);
}

// Only run main if this file is executed directly (not imported)
// In ES modules, we check if the current file is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Migration failed:');
    console.error(error);
    process.exit(1);
  });
}
