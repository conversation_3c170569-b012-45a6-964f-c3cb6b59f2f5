import { drizzle } from 'drizzle-orm/postgres-js';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema.js';

// Get the database URL from the environment
const databaseUrl = process.env.DATABASE_URL;

// Create a postgres client
export const createClient = (customUrl?: string) => {
  // Use the provided URL or fall back to the environment variable
  const connectionUrl = customUrl || databaseUrl;

  if (!connectionUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // Create a postgres client with improved connection settings
  const sql = postgres(connectionUrl, {
    // Connection pool settings
    max: process.env.NODE_ENV === 'test' ? 1 : 10, // Limit connections in test mode
    idle_timeout: 20, // Close idle connections after 20 seconds
    connect_timeout: 10, // Connection timeout in seconds

    // Retry settings
    max_lifetime: 60 * 30, // 30 minutes max connection lifetime

    // Error handling
    onnotice: () => {}, // Suppress PostgreSQL notices in tests

    // Transform settings for better compatibility
    transform: {
      undefined: null,
    },
  });

  // Create a drizzle instance
  const db = drizzle(sql, { schema });

  // Create a custom type that includes the end method
  type DbWithEnd = PostgresJsDatabase<typeof schema> & {
    end: () => Promise<void>;
  };

  // Add the end method to the db object
  const dbWithEnd = Object.assign(db, {
    async end() {
      return sql.end();
    },
  }) as DbWithEnd;

  return dbWithEnd;
};

// Export a singleton instance for use in the application
export const db = databaseUrl ? createClient(databaseUrl) : null;
