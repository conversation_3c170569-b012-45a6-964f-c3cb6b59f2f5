// This file is the entry point for the db package
// It exports the database schema, client, and other utilities

// Export database client
export * from './client.js';

// Export schema enums
export {
  userRoleEnum,
  accountTypeEnum,
  transactionTypeEnum,
  transactionEntryTypeEnum,
  categoryTypeEnum,
  budgetPeriodEnum,
  goalTypeEnum,
  recurringPatternTypeEnum,
} from './schema.js';

// Export schema types
export type {
  CurrencyCode,
  Money,
  RecurringPattern,
} from './schema.js';

// Export inferred types from the schema
export type {
  User,
  Account,
  Category,
  JournalEntry,
  JournalLine,
  Budget,
  Goal,
  UserSettings,
} from './types.js';

// Export schema tables
export {
  users,
  accounts,
  categories,
  journalEntries,
  journalLines,
  budgets,
  goals,
  userSettings,
} from './schema.js';
