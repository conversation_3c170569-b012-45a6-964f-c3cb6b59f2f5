import { journalEntries, journalLines } from '../../../schema.js';

export const testJournalEntries = [
  {
    id: '********-0000-0000-0000-********0301',
    userId: '********-0000-0000-0000-********0001', // Test user
    description: 'Test Salary',
    date: new Date('2023-01-15T10:00:00.000Z'),
    notes: 'Monthly salary',
    isRecurring: false,
    status: 'completed' as const,
    createdAt: new Date('2023-01-15T10:00:00.000Z'),
    updatedAt: new Date('2023-01-15T10:00:00.000Z'),
  },
  {
    id: '********-0000-0000-0000-********0302',
    userId: '********-0000-0000-0000-********0001', // Test user
    description: 'Test Rent Payment',
    date: new Date('2023-01-20T14:30:00.000Z'),
    notes: 'Monthly rent',
    isRecurring: false,
    status: 'completed' as const,
    createdAt: new Date('2023-01-20T14:30:00.000Z'),
    updatedAt: new Date('2023-01-20T14:30:00.000Z'),
  },
];

export const testJournalLines = [
  // Salary transaction - debit to checking account
  {
    id: '********-0000-0000-0000-********0401',
    journalEntryId: '********-0000-0000-0000-********0301',
    accountId: '********-0000-0000-0000-********0101', // Test Checking Account
    categoryId: '********-0000-0000-0000-********0201', // Salary
    type: 'debit' as const,
    amount: '3000.00',
    currency: 'USD',
    notes: 'Salary deposit',
    createdAt: new Date('2023-01-15T10:00:00.000Z'),
    updatedAt: new Date('2023-01-15T10:00:00.000Z'),
  },
  // Salary transaction - credit to income category
  {
    id: '********-0000-0000-0000-********0402',
    journalEntryId: '********-0000-0000-0000-********0301',
    accountId: '********-0000-0000-0000-********0101', // Using Test Checking Account as a placeholder
    categoryId: '********-0000-0000-0000-********0201', // Salary
    type: 'credit' as const,
    amount: '3000.00',
    currency: 'USD',
    notes: 'Salary income',
    createdAt: new Date('2023-01-15T10:00:00.000Z'),
    updatedAt: new Date('2023-01-15T10:00:00.000Z'),
  },
  // Rent payment - debit to housing expense category
  {
    id: '********-0000-0000-0000-********0403',
    journalEntryId: '********-0000-0000-0000-********0302',
    accountId: '********-0000-0000-0000-********0101', // Using Test Checking Account as a placeholder
    categoryId: '********-0000-0000-0000-************', // Housing
    type: 'debit' as const,
    amount: '1500.00',
    currency: 'USD',
    notes: 'Rent payment',
    createdAt: new Date('2023-01-20T14:30:00.000Z'),
    updatedAt: new Date('2023-01-20T14:30:00.000Z'),
  },
  // Rent payment - credit to checking account
  {
    id: '********-0000-0000-0000-********0404',
    journalEntryId: '********-0000-0000-0000-********0302',
    accountId: '********-0000-0000-0000-********0101', // Test Checking Account
    categoryId: '********-0000-0000-0000-************', // Housing
    type: 'credit' as const,
    amount: '1500.00',
    currency: 'USD',
    notes: 'Rent payment',
    createdAt: new Date('2023-01-20T14:30:00.000Z'),
    updatedAt: new Date('2023-01-20T14:30:00.000Z'),
  },
];
