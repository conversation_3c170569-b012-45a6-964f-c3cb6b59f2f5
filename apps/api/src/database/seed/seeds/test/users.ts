import { userRoleEnum, users } from '../../../schema.js';

export const testUsers = [
  {
    id: '00000000-0000-0000-0000-000000000001',
    email: '<EMAIL>',
    passwordHash:
      '$2a$10$JJV0TYH/S/4KeVJNPAOZ5eP.6XQkWZ0yZxKUGGTdOY1LkrJsWMK.e', // password: Test123!
    firstName: 'Test',
    lastName: 'User',
    role: 'user' as const,
    emailVerified: true,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
    isDeleted: false,
  },
  {
    id: '00000000-0000-0000-0000-000000000002',
    email: '<EMAIL>',
    passwordHash:
      '$2a$10$JJV0TYH/S/4KeVJNPAOZ5eP.6XQkWZ0yZxKUGGTdOY1LkrJsWMK.e', // password: Test123!
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as const,
    emailVerified: true,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
    isDeleted: false,
  },
];
