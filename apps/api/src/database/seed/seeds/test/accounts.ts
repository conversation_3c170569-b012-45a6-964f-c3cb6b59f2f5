import { accounts } from '../../../schema.js';

export const testAccounts = [
  {
    id: '********-0000-0000-0000-********0101',
    userId: '********-0000-0000-0000-********0001', // Test user
    name: 'Test Checking Account',
    type: 'checking' as const,
    currency: 'USD',
    initialBalance: '1000.00',
    currentBalance: '1000.00',
    isArchived: false,
    notes: 'Test checking account',
    icon: 'bank',
    color: '#4285F4',
    includeInNetWorth: true,
    displayOrder: 1,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
  {
    id: '********-0000-0000-0000-********0102',
    userId: '********-0000-0000-0000-********0001', // Test user
    name: 'Test Savings Account',
    type: 'savings' as const,
    currency: 'USD',
    initialBalance: '5000.00',
    currentBalance: '5000.00',
    isArchived: false,
    notes: 'Test savings account',
    icon: 'piggy-bank',
    color: '#34A853',
    includeInNetWorth: true,
    displayOrder: 2,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
  {
    id: '********-0000-0000-0000-********0103',
    userId: '********-0000-0000-0000-************', // Admin user
    name: 'Admin Checking Account',
    type: 'checking' as const,
    currency: 'USD',
    initialBalance: '2000.00',
    currentBalance: '2000.00',
    isArchived: false,
    notes: 'Admin checking account',
    icon: 'bank',
    color: '#4285F4',
    includeInNetWorth: true,
    displayOrder: 1,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
];
