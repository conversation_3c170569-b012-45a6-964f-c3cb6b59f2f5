import { userSettings } from '../../../schema.js';

export const testUserSettings = [
  {
    userId: '********-0000-0000-0000-********0001',
    defaultCurrency: 'USD',
    theme: 'light',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    notificationPreferences: {
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    },
    privacySettings: {
      hideBalances: false,
      requireAuthForSensitiveOperations: true,
    },
    appSettings: {
      defaultView: 'dashboard',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'month',
    },
  },
  {
    userId: '********-0000-0000-0000-********0002',
    defaultCurrency: 'USD',
    theme: 'dark',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h',
    notificationPreferences: {
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    },
    privacySettings: {
      hideBalances: true,
      requireAuthForSensitiveOperations: true,
    },
    appSettings: {
      defaultView: 'accounts',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'week',
    },
  },
];
