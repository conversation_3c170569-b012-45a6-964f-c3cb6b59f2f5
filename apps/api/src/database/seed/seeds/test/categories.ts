import { categories } from '../../../schema.js';

export const testCategories = [
  // Test user income categories
  {
    id: '00000000-0000-0000-0000-000000000201',
    userId: '00000000-0000-0000-0000-000000000001', // Test user
    parentId: null,
    name: '<PERSON><PERSON>',
    type: 'income' as const,
    icon: 'briefcase',
    color: '#4285F4',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
  // Test user expense categories
  {
    id: '00000000-0000-0000-0000-000000000202',
    userId: '00000000-0000-0000-0000-000000000001', // Test user
    parentId: null,
    name: 'Housing',
    type: 'expense' as const,
    icon: 'home',
    color: '#EA4335',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
  {
    id: '00000000-0000-0000-0000-000000000203',
    userId: '00000000-0000-0000-0000-000000000001', // Test user
    parentId: null,
    name: 'Food',
    type: 'expense' as const,
    icon: 'utensils',
    color: '#FBBC05',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 2,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
  // Admin user categories
  {
    id: '00000000-0000-0000-0000-000000000204',
    userId: '00000000-0000-0000-0000-000000000002', // Admin user
    parentId: null,
    name: 'Income',
    type: 'income' as const,
    icon: 'money-bill',
    color: '#4285F4',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
  {
    id: '00000000-0000-0000-0000-000000000205',
    userId: '00000000-0000-0000-0000-000000000002', // Admin user
    parentId: null,
    name: 'Expenses',
    type: 'expense' as const,
    icon: 'shopping-bag',
    color: '#EA4335',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  },
];
