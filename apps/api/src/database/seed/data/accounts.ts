import { accounts } from '../../schema.js';

export const accountsData = [
  // Demo user accounts
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    name: 'Checking Account',
    type: 'checking' as const,
    currency: 'USD',
    initialBalance: '1000.00',
    currentBalance: '1000.00',
    isArchived: false,
    notes: 'Primary checking account',
    icon: 'bank',
    color: '#4285F4',
    includeInNetWorth: true,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    name: 'Savings Account',
    type: 'savings' as const,
    currency: 'USD',
    initialBalance: '5000.00',
    currentBalance: '5000.00',
    isArchived: false,
    notes: 'Emergency fund',
    icon: 'piggy-bank',
    color: '#34A853',
    includeInNetWorth: true,
    displayOrder: 2,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    name: 'Credit Card',
    type: 'credit_card' as const,
    currency: 'USD',
    initialBalance: '0.00',
    currentBalance: '0.00',
    isArchived: false,
    notes: 'Primary credit card',
    icon: 'credit-card',
    color: '#EA4335',
    includeInNetWorth: true,
    displayOrder: 3,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    name: 'Cash',
    type: 'cash' as const,
    currency: 'USD',
    initialBalance: '200.00',
    currentBalance: '200.00',
    isArchived: false,
    notes: 'Physical cash',
    icon: 'cash',
    color: '#FBBC05',
    includeInNetWorth: true,
    displayOrder: 4,
  },

  // Admin user accounts
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Admin user
    name: 'Main Account',
    type: 'checking' as const,
    currency: 'EUR',
    initialBalance: '2000.00',
    currentBalance: '2000.00',
    isArchived: false,
    notes: 'Primary checking account',
    icon: 'bank',
    color: '#4285F4',
    includeInNetWorth: true,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Admin user
    name: 'Savings',
    type: 'savings' as const,
    currency: 'EUR',
    initialBalance: '10000.00',
    currentBalance: '10000.00',
    isArchived: false,
    notes: 'Long-term savings',
    icon: 'piggy-bank',
    color: '#34A853',
    includeInNetWorth: true,
    displayOrder: 2,
  },
];

export const getAccountsInsertData = () => {
  return accountsData.map(account => ({
    ...account,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};
