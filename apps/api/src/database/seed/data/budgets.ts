import { budgets } from '../../schema.js';

export const budgetsData = [
  // Demo user budgets
  {
    id: '550e8400-e29b-41d4-a716-446655440100',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    name: 'Housing Budget',
    amount: '1500.00',
    period: 'monthly' as const,
    categoryId: '550e8400-e29b-41d4-a716-446655440040', // Housing
    startDate: new Date('2023-10-01T00:00:00Z'),
    endDate: null,
    isRecurring: true,
    isArchived: false,
    notes: 'Monthly housing budget including rent and utilities',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440101',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    name: 'Food Budget',
    amount: '600.00',
    period: 'monthly' as const,
    categoryId: '550e8400-e29b-41d4-a716-446655440043', // Food
    startDate: new Date('2023-10-01T00:00:00Z'),
    endDate: null,
    isRecurring: true,
    isArchived: false,
    notes: 'Monthly food budget including groceries and restaurants',
  },

  // Admin user budgets
  {
    id: '550e8400-e29b-41d4-a716-446655440102',
    userId: '550e8400-e29b-41d4-a716-446655440001', // Admin user
    name: 'Monthly Expenses',
    amount: '2000.00',
    period: 'monthly' as const,
    categoryId: '550e8400-e29b-41d4-a716-446655440051', // Expenses
    startDate: new Date('2023-10-01T00:00:00Z'),
    endDate: null,
    isRecurring: true,
    isArchived: false,
    notes: 'Monthly expenses budget',
  },
];

export const getBudgetsInsertData = () => {
  return budgetsData.map(budget => ({
    ...budget,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};
