import { goals } from '../../schema.js';

export const goalsData = [
  // Demo user goals
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    name: 'Emergency Fund',
    type: 'emergency_fund' as const,
    targetAmount: '10000.00',
    currentAmount: '5000.00',
    currency: 'USD',
    deadline: new Date('2024-12-31T23:59:59Z'),
    accountId: '550e8400-e29b-41d4-a716-************', // Savings Account
    isArchived: false,
    isCompleted: false,
    notes: 'Building an emergency fund for unexpected expenses',
    icon: 'shield',
    color: '#34A853',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    name: 'Vacation',
    type: 'savings' as const,
    targetAmount: '3000.00',
    currentAmount: '1000.00',
    currency: 'USD',
    deadline: new Date('2024-06-30T23:59:59Z'),
    accountId: '550e8400-e29b-41d4-a716-************', // Savings Account
    isArchived: false,
    isCompleted: false,
    notes: 'Saving for summer vacation',
    icon: 'umbrella-beach',
    color: '#FBBC05',
  },

  // Admin user goals
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Admin user
    name: 'Savings Goal',
    type: 'savings' as const,
    targetAmount: '20000.00',
    currentAmount: '10000.00',
    currency: 'EUR',
    deadline: new Date('2024-12-31T23:59:59Z'),
    accountId: '550e8400-e29b-41d4-a716-************', // Savings
    isArchived: false,
    isCompleted: false,
    notes: 'Long-term savings goal',
    icon: 'piggy-bank',
    color: '#34A853',
  },
];

export const getGoalsInsertData = () => {
  return goalsData.map(goal => ({
    ...goal,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};
