import { userSettings } from '../../schema.js';

export const userSettingsData = [
  {
    userId: '550e8400-e29b-41d4-a716-************', // Demo user
    defaultCurrency: 'USD',
    theme: 'light',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    notificationPreferences: {
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    },
    privacySettings: {
      hideBalances: false,
      requireAuthForSensitiveOperations: true,
    },
    appSettings: {
      defaultView: 'dashboard',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'month',
    },
  },
  {
    userId: '550e8400-e29b-41d4-a716-************', // Admin user
    defaultCurrency: 'EUR',
    theme: 'dark',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    notificationPreferences: {
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    },
    privacySettings: {
      hideBalances: true,
      requireAuthForSensitiveOperations: true,
    },
    appSettings: {
      defaultView: 'accounts',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'month',
    },
  },
];

export const getUserSettingsInsertData = () => {
  return userSettingsData.map(setting => ({
    ...setting,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};
