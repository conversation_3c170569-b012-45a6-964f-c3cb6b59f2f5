import { journalEntries, journalLines } from '../../schema.js';

// Journal entries (transactions)
export const journalEntriesData = [
  // Demo user transactions
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    description: 'Salary deposit',
    date: new Date('2023-10-01T10:00:00Z'),
    notes: 'Monthly salary',
    isRecurring: false,
    status: 'completed' as const,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    description: 'Rent payment',
    date: new Date('2023-10-05T14:30:00Z'),
    notes: 'Monthly rent',
    isRecurring: false,
    status: 'completed' as const,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    description: 'Grocery shopping',
    date: new Date('2023-10-10T18:45:00Z'),
    notes: 'Weekly groceries',
    isRecurring: false,
    status: 'completed' as const,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    description: 'Transfer to savings',
    date: new Date('2023-10-15T09:15:00Z'),
    notes: 'Monthly savings',
    isRecurring: false,
    status: 'completed' as const,
  },

  // Admin user transactions
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Admin user
    description: 'Income',
    date: new Date('2023-10-01T11:00:00Z'),
    notes: 'Monthly income',
    isRecurring: false,
    status: 'completed' as const,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    userId: '550e8400-e29b-41d4-a716-************', // Admin user
    description: 'Expense',
    date: new Date('2023-10-07T16:20:00Z'),
    notes: 'General expense',
    isRecurring: false,
    status: 'completed' as const,
  },
];

// Journal lines (transaction entries)
export const journalLinesData = [
  // Demo user - Salary deposit
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Salary deposit
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account
    categoryId: '550e8400-e29b-41d4-a716-************', // Salary
    amount: '3000.00',
    type: 'debit' as const, // Money coming in
    notes: 'Monthly salary deposit',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Salary deposit
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account (income account)
    categoryId: '550e8400-e29b-41d4-a716-************', // Salary
    amount: '3000.00',
    type: 'credit' as const, // Balancing entry
    notes: 'Monthly salary deposit',
  },

  // Demo user - Rent payment
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Rent payment
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account
    categoryId: '550e8400-e29b-41d4-a716-************', // Rent
    amount: '1200.00',
    type: 'credit' as const, // Money going out
    notes: 'Monthly rent payment',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Rent payment
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account (expense account)
    categoryId: '550e8400-e29b-41d4-a716-************', // Rent
    amount: '1200.00',
    type: 'debit' as const, // Balancing entry
    notes: 'Monthly rent payment',
  },

  // Demo user - Grocery shopping
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Grocery shopping
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account
    categoryId: '550e8400-e29b-41d4-a716-************', // Groceries
    amount: '150.00',
    type: 'credit' as const, // Money going out
    notes: 'Weekly grocery shopping',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Grocery shopping
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account (expense account)
    categoryId: '550e8400-e29b-41d4-a716-************', // Groceries
    amount: '150.00',
    type: 'debit' as const, // Balancing entry
    notes: 'Weekly grocery shopping',
  },

  // Demo user - Transfer to savings
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Transfer to savings
    accountId: '550e8400-e29b-41d4-a716-************', // Checking Account
    categoryId: null, // No category for transfers
    amount: '500.00',
    type: 'credit' as const, // Money going out from checking
    notes: 'Monthly transfer to savings',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Transfer to savings
    accountId: '550e8400-e29b-41d4-a716-************', // Savings Account
    categoryId: null, // No category for transfers
    amount: '500.00',
    type: 'debit' as const, // Money coming in to savings
    notes: 'Monthly transfer to savings',
  },

  // Admin user - Income
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Income
    accountId: '550e8400-e29b-41d4-a716-************', // Main Account
    categoryId: '550e8400-e29b-41d4-a716-************', // Income
    amount: '4000.00',
    type: 'debit' as const, // Money coming in
    notes: 'Monthly income',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Income
    accountId: '550e8400-e29b-41d4-a716-************', // Main Account (income account)
    categoryId: '550e8400-e29b-41d4-a716-************', // Income
    amount: '4000.00',
    type: 'credit' as const, // Balancing entry
    notes: 'Monthly income',
  },

  // Admin user - Expense
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Expense
    accountId: '550e8400-e29b-41d4-a716-************', // Main Account
    categoryId: '550e8400-e29b-41d4-a716-************', // Expenses
    amount: '1000.00',
    type: 'credit' as const, // Money going out
    notes: 'General expense',
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    journalEntryId: '550e8400-e29b-41d4-a716-************', // Expense
    accountId: '550e8400-e29b-41d4-a716-************', // Main Account (expense account)
    categoryId: '550e8400-e29b-41d4-a716-************', // Expenses
    amount: '1000.00',
    type: 'debit' as const, // Balancing entry
    notes: 'General expense',
  },
];

export const getJournalEntriesInsertData = () => {
  return journalEntriesData.map(entry => ({
    ...entry,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

export const getJournalLinesInsertData = () => {
  return journalLinesData.map(line => ({
    ...line,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};
