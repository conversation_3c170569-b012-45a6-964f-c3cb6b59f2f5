import { categories } from '../../schema.js';

export const categoriesData = [
  // Demo user income categories
  {
    id: '550e8400-e29b-41d4-a716-446655440030',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: null,
    name: 'Salary',
    type: 'income' as const,
    icon: 'briefcase',
    color: '#4285F4',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440031',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: null,
    name: 'Investments',
    type: 'income' as const,
    icon: 'chart-line',
    color: '#34A853',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 2,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440032',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: null,
    name: 'Gifts',
    type: 'income' as const,
    icon: 'gift',
    color: '#FBBC05',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 3,
  },

  // Demo user expense categories
  {
    id: '550e8400-e29b-41d4-a716-446655440040',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: null,
    name: 'Housing',
    type: 'expense' as const,
    icon: 'home',
    color: '#EA4335',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440041',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: '550e8400-e29b-41d4-a716-446655440040', // Housing
    name: 'Rent',
    type: 'expense' as const,
    icon: 'key',
    color: '#EA4335',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440042',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: '550e8400-e29b-41d4-a716-446655440040', // Housing
    name: 'Utilities',
    type: 'expense' as const,
    icon: 'bolt',
    color: '#EA4335',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 2,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440043',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: null,
    name: 'Food',
    type: 'expense' as const,
    icon: 'utensils',
    color: '#FBBC05',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 2,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440044',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: '550e8400-e29b-41d4-a716-446655440043', // Food
    name: 'Groceries',
    type: 'expense' as const,
    icon: 'shopping-cart',
    color: '#FBBC05',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440045',
    userId: '550e8400-e29b-41d4-a716-446655440000', // Demo user
    parentId: '550e8400-e29b-41d4-a716-446655440043', // Food
    name: 'Restaurants',
    type: 'expense' as const,
    icon: 'utensils',
    color: '#FBBC05',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 2,
  },

  // Admin user categories (basic set)
  {
    id: '550e8400-e29b-41d4-a716-446655440050',
    userId: '550e8400-e29b-41d4-a716-446655440001', // Admin user
    parentId: null,
    name: 'Income',
    type: 'income' as const,
    icon: 'money-bill',
    color: '#4285F4',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440051',
    userId: '550e8400-e29b-41d4-a716-446655440001', // Admin user
    parentId: null,
    name: 'Expenses',
    type: 'expense' as const,
    icon: 'shopping-bag',
    color: '#EA4335',
    isDefault: true,
    isSystem: true,
    isArchived: false,
    displayOrder: 1,
  },
];

export const getCategoriesInsertData = () => {
  return categoriesData.map(category => ({
    ...category,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};
