import { users } from '../../schema.js';

export const usersData = [
  {
    id: '550e8400-e29b-41d4-a716-************', // Fixed UUID for reference in other seed files
    email: '<EMAIL>',
    passwordHash:
      '$2a$10$dqyYw5XovLjpmkYNiRDEWuwKaRAvLaG45fnXE5b3KTccKZcRPka2m', // "password"
    firstName: 'Demo',
    lastName: 'User',
    role: 'user' as const,
    emailVerified: true,
    phoneNumber: '+1234567890',
    phoneVerified: false,
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    email: '<EMAIL>',
    passwordHash:
      '$2a$10$dqyYw5XovLjpmkYNiRDEWuwKaRAvLaG45fnXE5b3KTccKZcRPka2m', // "password"
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as const,
    emailVerified: true,
    phoneNumber: '+1987654321',
    phoneVerified: true,
  },
];

export const getUsersInsertData = () => {
  return usersData.map(user => ({
    ...user,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  }));
};
