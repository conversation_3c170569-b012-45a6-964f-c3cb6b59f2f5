import {
  boolean,
  date,
  index,
  integer,
  jsonb,
  numeric,
  pgEnum,
  pgTable,
  primaryKey,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';

// ==========================================
// Define enums
// ==========================================

// User role enum
export const userRoleEnum = pgEnum('user_role', ['user', 'admin']);

// Account type enum
export const accountTypeEnum = pgEnum('account_type', [
  'checking',
  'savings',
  'credit_card',
  'cash',
  'investment',
  'loan',
  'asset',
  'liability',
]);

// Transaction type enum
export const transactionTypeEnum = pgEnum('transaction_type', [
  'income',
  'expense',
  'transfer',
]);

// Transaction entry type enum (for double-entry accounting)
export const transactionEntryTypeEnum = pgEnum('transaction_entry_type', [
  'debit',
  'credit',
]);

// Category type enum
export const categoryTypeEnum = pgEnum('category_type', ['income', 'expense']);

// Budget period enum
export const budgetPeriodEnum = pgEnum('budget_period', [
  'daily',
  'weekly',
  'monthly',
  'quarterly',
  'yearly',
]);

// Goal type enum
export const goalTypeEnum = pgEnum('goal_type', [
  'savings',
  'debt_payoff',
  'purchase',
  'emergency_fund',
  'other',
]);

// Recurring pattern type enum
export const recurringPatternTypeEnum = pgEnum('recurring_pattern_type', [
  'daily',
  'weekly',
  'monthly',
  'yearly',
]);

// ==========================================
// Define common types and utility functions
// ==========================================

// ISO currency code type (3-letter code)
export type CurrencyCode = string;

// Money type (amount with currency)
export type Money = {
  amount: number;
  currency: CurrencyCode;
};

// Recurring pattern type
export type RecurringPattern = {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  dayOfWeek?: number; // 0-6 (Sunday-Saturday)
  dayOfMonth?: number; // 1-31
  monthOfYear?: number; // 1-12
  endDate?: Date | null;
  occurrences?: number | null;
};

// ==========================================
// Define tables
// ==========================================
// Note on database design:
// We follow the principle of normalization by separating user identity (users table)
// from user preferences and settings (user_settings table) with a one-to-one relationship.
// This separation allows for:
// 1. Cleaner schema organization
// 2. Better performance for authentication queries (smaller users table)
// 3. Easier extension of user settings without modifying the core users table
// 4. Logical separation of concerns (identity vs. preferences)

// Users table - contains only core user identity and authentication data
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }),
  firstName: varchar('first_name', { length: 255 }),
  lastName: varchar('last_name', { length: 255 }),
  role: userRoleEnum('role').notNull().default('user'),
  emailVerified: boolean('email_verified').notNull().default(false),

  // Core user information
  phoneNumber: varchar('phone_number', { length: 20 }),
  phoneVerified: boolean('phone_verified').notNull().default(false),

  // Timestamps
  lastLoginAt: timestamp('last_login_at', { withTimezone: true }),
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true })
    .notNull()
    .defaultNow(),

  // Soft delete
  isDeleted: boolean('is_deleted').notNull().default(false),
});

// Accounts table
export const accounts = pgTable(
  'accounts',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    name: varchar('name', { length: 255 }).notNull(),
    type: accountTypeEnum('type').notNull(),
    currency: varchar('currency', { length: 3 }).notNull().default('USD'),
    initialBalance: numeric('initial_balance', { precision: 19, scale: 4 })
      .notNull()
      .default('0'),
    currentBalance: numeric('current_balance', { precision: 19, scale: 4 })
      .notNull()
      .default('0'),
    isArchived: boolean('is_archived').notNull().default(false),
    notes: text('notes'),
    icon: varchar('icon', { length: 50 }),
    color: varchar('color', { length: 20 }),
    includeInNetWorth: boolean('include_in_net_worth').notNull().default(true),
    displayOrder: integer('display_order').notNull().default(0),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      userIdIdx: index('accounts_user_id_idx').on(table.userId),
      typeIdx: index('accounts_type_idx').on(table.type),
      archivedIdx: index('accounts_is_archived_idx').on(table.isArchived),
    };
  }
);

// Categories table with self-reference for hierarchical structure
export const categories = pgTable(
  'categories',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    parentId: uuid('parent_id'), // Self-reference will be added after table definition
    name: varchar('name', { length: 255 }).notNull(),
    type: categoryTypeEnum('type').notNull(),
    icon: varchar('icon', { length: 50 }),
    color: varchar('color', { length: 20 }),
    isDefault: boolean('is_default').notNull().default(false),
    isSystem: boolean('is_system').notNull().default(false),
    isArchived: boolean('is_archived').notNull().default(false),
    displayOrder: integer('display_order').notNull().default(0),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      userIdIdx: index('categories_user_id_idx').on(table.userId),
      parentIdIdx: index('categories_parent_id_idx').on(table.parentId),
      typeIdx: index('categories_type_idx').on(table.type),
      defaultIdx: index('categories_is_default_idx').on(table.isDefault),
      systemIdx: index('categories_is_system_idx').on(table.isSystem),
      archivedIdx: index('categories_is_archived_idx').on(table.isArchived),
    };
  }
);

// Note: The self-reference for categories.parentId to categories.id will be handled
// by Drizzle when generating migrations. We've defined the column but not the constraint
// to avoid circular references in TypeScript.
//
// Relationships between tables:
// 1. Users to User Settings: One-to-one relationship with userSettings.userId references users.id (cascade delete)
//    This is a key relationship that follows normalization principles by separating identity from preferences
// 2. Users to Accounts: accounts.userId references users.id (cascade delete)
// 3. Users to Categories: categories.userId references users.id (cascade delete)
// 4. Users to Budgets: budgets.userId references users.id (cascade delete)
// 5. Users to Goals: goals.userId references users.id (cascade delete)
// 6. Categories to Subcategories: categories.parentId references categories.id (set null on delete)
// 7. Accounts to Transactions: journalLines.accountId references accounts.id (restrict delete)
// 8. Categories to Transactions: journalLines.categoryId references categories.id (set null on delete)
// 9. Transactions to Transaction Entries: journalLines.journalEntryId references journalEntries.id (cascade delete)
// 10. Goals to Accounts: goals.accountId references accounts.id (set null on delete)
// 11. Budgets to Categories: budgets.categoryId references categories.id (set null on delete)

// Transactions table (journal entries)
export const journalEntries = pgTable(
  'journal_entries',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    description: varchar('description', { length: 255 }).notNull(),
    date: timestamp('date', { withTimezone: true }).notNull().defaultNow(),
    notes: text('notes'),
    isRecurring: boolean('is_recurring').notNull().default(false),
    recurringPattern: jsonb('recurring_pattern'),
    status: varchar('status', { length: 20 }).notNull().default('completed'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      userIdIdx: index('journal_entries_user_id_idx').on(table.userId),
      dateIdx: index('journal_entries_date_idx').on(table.date),
      recurringIdx: index('journal_entries_is_recurring_idx').on(
        table.isRecurring
      ),
    };
  }
);

// Transaction entries table (journal lines)
export const journalLines = pgTable(
  'journal_lines',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    journalEntryId: uuid('journal_entry_id')
      .notNull()
      .references(() => journalEntries.id, { onDelete: 'cascade' }),
    accountId: uuid('account_id')
      .notNull()
      .references(() => accounts.id, { onDelete: 'restrict' }),
    categoryId: uuid('category_id').references(() => categories.id, {
      onDelete: 'set null',
    }),
    amount: numeric('amount', { precision: 19, scale: 4 }).notNull(),
    type: transactionEntryTypeEnum('type').notNull(),
    notes: text('notes'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      journalEntryIdIdx: index('journal_lines_journal_entry_id_idx').on(
        table.journalEntryId
      ),
      accountIdIdx: index('journal_lines_account_id_idx').on(table.accountId),
      categoryIdIdx: index('journal_lines_category_id_idx').on(
        table.categoryId
      ),
      typeIdx: index('journal_lines_type_idx').on(table.type),
    };
  }
);

// Budgets table
export const budgets = pgTable(
  'budgets',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    name: varchar('name', { length: 255 }).notNull(),
    amount: numeric('amount', { precision: 19, scale: 4 }).notNull(),
    period: budgetPeriodEnum('period').notNull(),
    categoryId: uuid('category_id').references(() => categories.id, {
      onDelete: 'set null',
    }),
    startDate: timestamp('start_date', { withTimezone: true }).notNull(),
    endDate: timestamp('end_date', { withTimezone: true }),
    isRecurring: boolean('is_recurring').notNull().default(false),
    isArchived: boolean('is_archived').notNull().default(false),
    notes: text('notes'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      userIdIdx: index('budgets_user_id_idx').on(table.userId),
      categoryIdIdx: index('budgets_category_id_idx').on(table.categoryId),
      periodIdx: index('budgets_period_idx').on(table.period),
      startDateIdx: index('budgets_start_date_idx').on(table.startDate),
      archivedIdx: index('budgets_is_archived_idx').on(table.isArchived),
    };
  }
);

// Goals table
export const goals = pgTable(
  'goals',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    name: varchar('name', { length: 255 }).notNull(),
    type: goalTypeEnum('type').notNull(),
    targetAmount: numeric('target_amount', {
      precision: 19,
      scale: 4,
    }).notNull(),
    currentAmount: numeric('current_amount', { precision: 19, scale: 4 })
      .notNull()
      .default('0'),
    currency: varchar('currency', { length: 3 }).notNull().default('USD'),
    deadline: timestamp('deadline', { withTimezone: true }),
    accountId: uuid('account_id').references(() => accounts.id, {
      onDelete: 'set null',
    }),
    isArchived: boolean('is_archived').notNull().default(false),
    isCompleted: boolean('is_completed').notNull().default(false),
    notes: text('notes'),
    icon: varchar('icon', { length: 50 }),
    color: varchar('color', { length: 20 }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      userIdIdx: index('goals_user_id_idx').on(table.userId),
      typeIdx: index('goals_type_idx').on(table.type),
      accountIdIdx: index('goals_account_id_idx').on(table.accountId),
      deadlineIdx: index('goals_deadline_idx').on(table.deadline),
      archivedIdx: index('goals_is_archived_idx').on(table.isArchived),
      completedIdx: index('goals_is_completed_idx').on(table.isCompleted),
    };
  }
);

// User settings table - contains all user preferences and settings
export const userSettings = pgTable(
  'user_settings',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' })
      .unique(),

    // Profile information
    profilePictureUrl: varchar('profile_picture_url', { length: 1024 }),

    // Display preferences
    defaultCurrency: varchar('default_currency', { length: 3 })
      .notNull()
      .default('USD'),
    theme: varchar('theme', { length: 20 }).notNull().default('light'),
    dateFormat: varchar('date_format', { length: 20 })
      .notNull()
      .default('MM/DD/YYYY'),
    timeFormat: varchar('time_format', { length: 20 }).notNull().default('12h'),

    // Notification preferences
    notificationPreferences: jsonb('notification_preferences').default({
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    }),

    // Privacy settings
    privacySettings: jsonb('privacy_settings').default({
      hideBalances: false,
      requireAuthForSensitiveOperations: true,
    }),

    // App settings
    appSettings: jsonb('app_settings').default({
      defaultView: 'dashboard',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'month',
    }),

    // Timestamps
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  table => {
    return {
      userIdIdx: index('user_settings_user_id_idx').on(table.userId),
    };
  }
);
