// This file exports TypeScript types for the database schema
import type { InferSelectModel } from 'drizzle-orm';
import type {
  accounts,
  budgets,
  categories,
  goals,
  journalEntries,
  journalLines,
  userSettings,
  users,
} from './schema.js';

// Export inferred types from the schema
export type User = InferSelectModel<typeof users>;
export type Account = InferSelectModel<typeof accounts>;
export type Category = InferSelectModel<typeof categories>;
export type JournalEntry = InferSelectModel<typeof journalEntries>;
export type JournalLine = InferSelectModel<typeof journalLines>;
export type Budget = InferSelectModel<typeof budgets>;
export type Goal = InferSelectModel<typeof goals>;
export type UserSettings = InferSelectModel<typeof userSettings>;
