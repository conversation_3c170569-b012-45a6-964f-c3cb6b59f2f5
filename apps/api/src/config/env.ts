import { existsSync } from 'node:fs';
import { dirname, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
// Load environment variables from .env file
import dotenv from 'dotenv';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Determine which .env file to load based on NODE_ENV
const nodeEnv = process.env.NODE_ENV || 'development';
const envFile = nodeEnv === 'test' ? '.env.test' : '.env';

// Try to load environment variables from different locations
const envPaths = [
  resolve(__dirname, `../../${envFile}`),
  resolve(process.cwd(), envFile),
];

// Load the first .env file that exists
for (const envPath of envPaths) {
  if (existsSync(envPath)) {
    // Only log environment loading in verbose mode or development
    const isVerbose =
      process.env.VITEST_VERBOSE === 'true' ||
      process.env.DEBUG === 'true' ||
      nodeEnv === 'development';
    if (isVerbose) {
      console.log(
        `Loading environment variables from ${envPath} (${nodeEnv} environment)`
      );
    }

    // For unit tests, preserve environment variables if they're already set (e.g., by vitest config)
    // This allows unit tests to use fake values without loading real ones from .env files
    // We detect unit tests by checking the TEST_SUITE_TYPE environment variable OR
    // if we're running a unit test file (detected by file path in call stack)
    const isUnitTestContext =
      nodeEnv === 'test' &&
      (process.env.TEST_SUITE_TYPE === 'unit' ||
        // Check if we're running unit tests by looking at the call stack
        new Error().stack?.includes('/__tests__/unit/'));

    const preservedEnvVars: Record<string, string> = {};
    if (isUnitTestContext) {
      // Preserve all environment variables that might be set by vitest unit config
      const envVarsToPreserve = [
        'DATABASE_URL',
        'JWT_SECRET',
        'JWT_EXPIRES_IN',
        'AUTH_COOKIE_NAME',
        'AUTH_COOKIE_SECURE',
        'AUTH_COOKIE_HTTP_ONLY',
        'AUTH_COOKIE_DOMAIN',
        'AUTH_COOKIE_MAX_AGE',
        'AUTH_REDIRECT_URL',
        'AUTH_MOBILE_REDIRECT_URL',
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'APPLE_CLIENT_ID',
        'APPLE_CLIENT_SECRET',
        'OTEL_SDK_DISABLED',
      ];

      for (const envVar of envVarsToPreserve) {
        if (process.env[envVar]) {
          preservedEnvVars[envVar] = process.env[envVar]!;
        }
      }
    }

    dotenv.config({ path: envPath });

    // Restore the preserved environment variables if this is a unit test context
    if (isUnitTestContext) {
      for (const [key, value] of Object.entries(preservedEnvVars)) {
        process.env[key] = value;
      }
    }

    break;
  }
}

// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => {
  const value = process.env[envVar];
  return (
    value === undefined ||
    value === null ||
    value === '' ||
    value === 'undefined'
  );
});

if (missingEnvVars.length > 0) {
  console.warn(
    `Missing required environment variables: ${missingEnvVars.join(', ')}`
  );
}

export default process.env;
