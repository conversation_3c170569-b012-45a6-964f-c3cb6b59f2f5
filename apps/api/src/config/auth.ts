// Authentication configuration
import { z } from 'zod';

// Environment variables schema for authentication
const authEnvSchema = z.object({
  JWT_SECRET: z
    .string()
    .min(32)
    .default('your-jwt-secret-should-be-at-least-32-chars-long'),
  JWT_EXPIRES_IN: z.string().default('1d'),
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  APPLE_CLIENT_ID: z.string().optional(),
  APPLE_CLIENT_SECRET: z.string().optional(),
  AUTH_COOKIE_NAME: z.string().default('budapp_auth'),
  AUTH_COOKIE_SECURE: z.coerce.boolean().optional(),
  AUTH_COOKIE_HTTP_ONLY: z.coerce.boolean().default(true),
  AUTH_COOKIE_DOMAIN: z.string().optional(),
  AUTH_COOKIE_MAX_AGE: z.coerce.number().optional(),
  AUTH_REDIRECT_URL: z.string().default('http://localhost:3000/auth/callback'),
  AUTH_MOBILE_REDIRECT_URL: z.string().default('budapp://auth/callback'),
});

// Helper function to filter out undefined values and string "undefined"
const filterUndefined = (obj: Record<string, string | undefined>) => {
  const filtered: Record<string, string> = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined && value !== 'undefined') {
      filtered[key] = value;
    }
  }
  return filtered;
};

// Parse environment variables
const rawConfig = authEnvSchema.parse(
  filterUndefined({
    JWT_SECRET: process.env.JWT_SECRET,
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN,
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    APPLE_CLIENT_ID: process.env.APPLE_CLIENT_ID,
    APPLE_CLIENT_SECRET: process.env.APPLE_CLIENT_SECRET,
    AUTH_COOKIE_NAME: process.env.AUTH_COOKIE_NAME,
    AUTH_COOKIE_SECURE: process.env.AUTH_COOKIE_SECURE,
    AUTH_COOKIE_HTTP_ONLY: process.env.AUTH_COOKIE_HTTP_ONLY,
    AUTH_COOKIE_DOMAIN: process.env.AUTH_COOKIE_DOMAIN,
    AUTH_COOKIE_MAX_AGE: process.env.AUTH_COOKIE_MAX_AGE,
    AUTH_REDIRECT_URL: process.env.AUTH_REDIRECT_URL,
    AUTH_MOBILE_REDIRECT_URL: process.env.AUTH_MOBILE_REDIRECT_URL,
  })
);

// Apply runtime defaults for values that depend on NODE_ENV
export const authConfig = {
  ...rawConfig,
  AUTH_COOKIE_SECURE:
    rawConfig.AUTH_COOKIE_SECURE ?? process.env.NODE_ENV === 'production',
  AUTH_COOKIE_MAX_AGE: rawConfig.AUTH_COOKIE_MAX_AGE ?? 86400 * 30, // 30 days in seconds
};

// JWT token payload type
export interface JwtPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// OAuth provider types
export enum OAuthProvider {
  GOOGLE = 'google',
  APPLE = 'apple',
}

// OAuth profile type
export interface OAuthProfile {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  provider: OAuthProvider;
  picture?: string;
}
