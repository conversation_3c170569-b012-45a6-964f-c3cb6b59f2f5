// Authentication routes
import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { type OAuthProfile, OAuthProvider } from '../config/auth.js';
import { authConfig } from '../config/auth.js';
import { handleOAuthLogin } from '../services/auth/auth.service.js';

// Type definitions for OAuth query parameters
interface OAuthQueryParams {
  error?: string;
  error_description?: string;
  code?: string;
  platform?: string;
}

interface OAuthBodyParams {
  error?: string;
  error_description?: string;
  code?: string;
}

// Register authentication routes
export async function authRoutes(fastify: FastifyInstance) {
  // Google OAuth routes
  fastify.get('/auth/google', async (request, reply) => {
    // Check if we're in test mode
    const isTest = process.env.NODE_ENV === 'test';

    if (isTest) {
      // For E2E tests, redirect to a real-looking Google OAuth URL
      const googleOAuthUrl = `https://accounts.google.com/oauth/authorize?client_id=mock-client-id&redirect_uri=${encodeURIComponent(
        `${request.protocol}://${request.hostname}/auth/google/callback`
      )}&response_type=code&scope=email%20profile&state=mock-state`;
      return reply.redirect(googleOAuthUrl);
    }
    // For development/production, redirect to mock callback
    const redirectUrl = '/auth/google/callback?code=mock-auth-code';
    return reply.redirect(redirectUrl);
  });

  fastify.get('/auth/google/callback', async (request, reply) => {
    try {
      // Check for error parameters
      const query = (request.query as OAuthQueryParams) || {};

      if (query.error) {
        const error = query.error;
        const errorDescription =
          query.error_description || 'OAuth authentication failed';
        request.log.error(`Google OAuth error: ${error} - ${errorDescription}`);
        return reply.redirect(
          `/auth/error?error=${encodeURIComponent(String(error))}`
        );
      }

      // Check for authorization code
      const code = query.code;
      if (!code) {
        request.log.error('No authorization code received from Google');
        return reply.redirect('/auth/error?error=no_code');
      }

      // In a real implementation, we would exchange the code for a token
      // and get the user profile from Google
      // For now, we'll create a mock profile
      const mockProfile: OAuthProfile = {
        id: 'google-123456',
        email: '<EMAIL>',
        firstName: 'Google',
        lastName: 'User',
        provider: OAuthProvider.GOOGLE,
        picture: 'https://example.com/avatar.jpg',
      };

      // Handle OAuth login
      const { token, user } = await handleOAuthLogin(mockProfile);

      // Set cookie
      // @ts-ignore - setCookie is added by the cookie plugin
      reply.setCookie(authConfig.AUTH_COOKIE_NAME, token, {
        path: '/',
        httpOnly: authConfig.AUTH_COOKIE_HTTP_ONLY,
        secure: authConfig.AUTH_COOKIE_SECURE,
        maxAge: authConfig.AUTH_COOKIE_MAX_AGE,
        domain: authConfig.AUTH_COOKIE_DOMAIN,
      });

      // Redirect to mobile app or web app
      const isMobile = query.platform === 'mobile';
      const redirectUrl = isMobile
        ? `${authConfig.AUTH_MOBILE_REDIRECT_URL}?token=${token}`
        : '/';

      return reply.redirect(redirectUrl);
    } catch (error) {
      request.log.error(error);
      return reply.redirect('/auth/error');
    }
  });

  // Apple Sign In routes
  fastify.get('/auth/apple', async (request, reply) => {
    // Check if we're in test mode
    const isTest = process.env.NODE_ENV === 'test';

    if (isTest) {
      // For E2E tests, redirect to a real-looking Apple OAuth URL
      const appleOAuthUrl = `https://appleid.apple.com/auth/authorize?client_id=mock-client-id&redirect_uri=${encodeURIComponent(
        `${request.protocol}://${request.hostname}/auth/apple/callback`
      )}&response_type=code&scope=email%20name&response_mode=form_post&state=mock-state`;
      return reply.redirect(appleOAuthUrl);
    }
    // For development/production, redirect to mock callback
    const redirectUrl = '/auth/apple/callback?code=mock-auth-code';
    return reply.redirect(redirectUrl);
  });

  // Handle Apple callback for both GET and POST
  const handleAppleCallback = async (
    request: FastifyRequest,
    reply: FastifyReply
  ) => {
    try {
      // Check for error parameters
      const query = (request.query as OAuthQueryParams) || {};
      const body = (request.body as OAuthBodyParams) || {};

      if (query.error || body.error) {
        const error = query.error || body.error;
        const errorDescription =
          query.error_description ||
          body.error_description ||
          'OAuth authentication failed';
        request.log.error(`Apple OAuth error: ${error} - ${errorDescription}`);
        return reply.redirect(
          `/auth/error?error=${encodeURIComponent(String(error))}`
        );
      }

      // Check for authorization code
      const code = query.code || body.code;
      if (!code) {
        request.log.error('No authorization code received from Apple');
        return reply.redirect('/auth/error?error=no_code');
      }

      // In a real implementation, we would exchange the code for a token
      // and get the user profile from Apple
      // For now, we'll create a mock profile
      const mockProfile: OAuthProfile = {
        id: 'apple-123456',
        email: '<EMAIL>',
        firstName: 'Apple',
        lastName: 'User',
        provider: OAuthProvider.APPLE,
      };

      // Handle OAuth login
      const { token, user } = await handleOAuthLogin(mockProfile);

      // Set cookie
      // @ts-ignore - setCookie is added by the cookie plugin
      reply.setCookie(authConfig.AUTH_COOKIE_NAME, token, {
        path: '/',
        httpOnly: authConfig.AUTH_COOKIE_HTTP_ONLY,
        secure: authConfig.AUTH_COOKIE_SECURE,
        maxAge: authConfig.AUTH_COOKIE_MAX_AGE,
        domain: authConfig.AUTH_COOKIE_DOMAIN,
      });

      // Redirect to mobile app or web app
      const isMobile = query.platform === 'mobile';
      const redirectUrl = isMobile
        ? `${authConfig.AUTH_MOBILE_REDIRECT_URL}?token=${token}`
        : '/';

      return reply.redirect(redirectUrl);
    } catch (error) {
      request.log.error(error);
      return reply.redirect('/auth/error');
    }
  };

  fastify.get('/auth/apple/callback', handleAppleCallback);
  fastify.post('/auth/apple/callback', handleAppleCallback);

  // Error route
  fastify.get('/auth/error', async (request, reply) => {
    return { error: 'Authentication failed' };
  });
}
