# Integration Testing Performance Improvement Guide

## Problem Statement

The previous integration testing approach was inefficient because it:
1. **Reset the entire database before each test** via `resetTestDb()` in `beforeEach()`
2. **Truncated all tables** for every single test
3. **Lost all seed data** and had to recreate test data from scratch
4. **Scaled poorly** - each new test file made the suite slower

This approach was fundamentally flawed and would become a major bottleneck as the project grows.

## New Approach: Transaction-Based Test Isolation

### Key Improvements

1. **Database prepared once**: Setup and seed database in global setup
2. **Transaction isolation**: Each test runs in its own transaction that gets rolled back
3. **Preserve seed data**: Seed data remains available across all tests
4. **Massive performance gain**: ~10-100x faster than table truncation
5. **Perfect isolation**: Tests cannot interfere with each other

### How It Works

```typescript
// Before (SLOW - truncates tables)
beforeEach(async () => {
  await resetTestDb(); // Truncates ALL tables, loses seed data
});

// After (FAST - transaction rollback)
it('should test something', async () => {
  await withTransaction(async (db) => {
    // All database operations happen in a transaction
    // Transaction is automatically rolled back after test
    const user = await db.insert(users).values(testData).returning();
    expect(user).toBeDefined();
  });
});
```

## Available Test Utilities

### 1. `withTransaction(testFn)` - Primary Method
**Use for**: Tests that modify data and need perfect isolation

```typescript
import { withTransaction } from '../../../src/test-utils/db.js';

it('should create and modify data', async () => {
  await withTransaction(async (db) => {
    // Create test data
    const user = await db.insert(users).values(userData).returning();
    
    // Modify data
    await db.update(users).set({ verified: true }).where(eq(users.id, user.id));
    
    // All changes are automatically rolled back after test
  });
});
```

### 2. `withSeededData(testFn)` - Read-Only Tests
**Use for**: Tests that only read existing seed data

```typescript
import { withSeededData } from '../../../src/test-utils/db.js';

it('should read seed data', async () => {
  await withSeededData(async (db) => {
    // Read existing seed data (no transaction needed)
    const users = await db.select().from(users);
    expect(users.length).toBeGreaterThan(0);
  });
});
```

### 3. `withSharedTransaction(setupFn, testFns)` - Shared Setup
**Use for**: Multiple tests that need the same complex setup

```typescript
import { withSharedTransaction } from '../../../src/test-utils/db.js';

describe('Complex scenario tests', () => {
  it('should handle multiple related tests', async () => {
    await withSharedTransaction(
      // Setup function (runs once)
      async (db) => {
        const user = await db.insert(users).values(userData).returning();
        await db.insert(accounts).values({ userId: user.id, ...accountData });
      },
      // Test functions (all share the same setup)
      [
        async (db) => {
          const accounts = await db.select().from(accounts);
          expect(accounts).toHaveLength(1);
        },
        async (db) => {
          const users = await db.select().from(users);
          expect(users).toHaveLength(1);
        }
      ]
    );
  });
});
```

## Migration Guide

### Step 1: Update Test Imports
```typescript
// Remove unused beforeEach import
import { describe, it, expect } from 'vitest';

// Update db utility imports
import { withTransaction, withSeededData } from '../../../src/test-utils/db.js';
```

### Step 2: Remove beforeEach Database Reset
```typescript
// DELETE THIS - no longer needed
beforeEach(async () => {
  await resetTestDb(); // REMOVE
});
```

### Step 3: Wrap Tests in Transactions
```typescript
// Before
it('should test something', async () => {
  const db = getTestDb();
  const user = await db.insert(users).values(userData).returning();
  // Test continues...
});

// After
it('should test something', async () => {
  await withTransaction(async (db) => {
    const user = await db.insert(users).values(userData).returning();
    // Test continues...
  });
});
```

## Performance Comparison

| Approach | Time per Test | Scalability | Seed Data |
|----------|---------------|-------------|-----------|
| **Old (Truncate)** | ~500-2000ms | Poor | Lost each test |
| **New (Transaction)** | ~10-50ms | Excellent | Preserved |

**Expected improvement**: 10-100x faster test execution

## Best Practices

### 1. Choose the Right Utility
- **Modifying data**: Use `withTransaction()`
- **Reading only**: Use `withSeededData()`
- **Complex setup**: Use `withSharedTransaction()`

### 2. Test Organization
```typescript
describe('Database Integration - Users Table', () => {
  // No beforeEach needed!
  
  describe('User Creation', () => {
    it('should create user', async () => {
      await withTransaction(async (db) => {
        // Test implementation
      });
    });
  });
  
  describe('User Reading', () => {
    it('should read seed data', async () => {
      await withSeededData(async (db) => {
        // Read-only test
      });
    });
  });
});
```

### 3. Error Handling
Transactions are automatically rolled back even if tests fail:

```typescript
it('should handle errors gracefully', async () => {
  await withTransaction(async (db) => {
    // Even if this throws an error...
    throw new Error('Test failed');
    // ...the transaction is still rolled back
  });
});
```

## Backward Compatibility

The old `resetTestDb()` function is still available for emergency cleanup, but should not be used in regular tests:

```typescript
// Legacy function - avoid using
export const withDatabaseCleanup = async (testFn) => {
  // Uses slow table truncation
};
```

## Implementation Details

### Transaction Lifecycle
1. **Global Setup**: Database is prepared and seeded once
2. **Test Start**: `BEGIN` transaction
3. **Test Execution**: All operations within transaction
4. **Test End**: `ROLLBACK` transaction (always)
5. **Next Test**: Fresh transaction, seed data still available

### Connection Management
- Single database connection with `max: 1` for transaction safety
- Connection reused across all tests
- Proper cleanup in global teardown

## Troubleshooting

### Issue: "Transaction already in progress"
**Solution**: Make sure you're not nesting transactions

```typescript
// Wrong - nesting transactions
await withTransaction(async (db) => {
  await withTransaction(async (db2) => { // ERROR
    // ...
  });
});

// Right - single transaction
await withTransaction(async (db) => {
  // All operations in one transaction
});
```

### Issue: "Data persists between tests"
**Solution**: Check that you're using `withTransaction()` not `withSeededData()`

```typescript
// Wrong for data modification
await withSeededData(async (db) => {
  await db.insert(users).values(data); // Persists!
});

// Right for data modification
await withTransaction(async (db) => {
  await db.insert(users).values(data); // Rolled back
});
```

## Next Steps

1. **Update existing tests** to use the new transaction-based approach
2. **Remove beforeEach database resets** from all integration test files
3. **Monitor performance improvements** in CI/CD pipeline
4. **Update team documentation** and coding standards

This change will dramatically improve development velocity and CI/CD performance while maintaining perfect test isolation.
