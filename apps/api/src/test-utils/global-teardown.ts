// Global teardown for Vitest
import { teardownTestDb } from './db.js';

// This file is run once after all tests
export default async function () {
  console.log('🧹 Global teardown starting...');

  // Close database connections
  console.log('Closing database connections...');
  const dbTeardown = await teardownTestDb();
  if (!dbTeardown) {
    console.error('Failed to tear down test database');
  }

  console.log('✅ Global teardown completed successfully');
}
