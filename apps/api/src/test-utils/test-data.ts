// Utilities for generating test data
import { faker } from '@faker-js/faker';
import type { RecurringPattern } from '../database/schema.js';
import { hashPassword } from '../lib/auth/password.js';

/**
 * Generate a test user
 */
export const generateTestUser = async (overrides: Partial<TestUser> = {}) => {
  const firstName =
    overrides.firstName !== undefined
      ? overrides.firstName
      : faker.person.firstName().substring(0, 255);
  const lastName =
    overrides.lastName !== undefined
      ? overrides.lastName
      : faker.person.lastName().substring(0, 255);
  const phoneNumber =
    overrides.phoneNumber !== undefined
      ? overrides.phoneNumber
      : faker.phone.number().substring(0, 20);

  return {
    id: overrides.id || faker.string.uuid(),
    email:
      overrides.email ||
      faker.internet
        .email({ firstName: firstName || 'test', lastName: lastName || 'user' })
        .toLowerCase()
        .substring(0, 255),
    passwordHash:
      overrides.passwordHash ||
      (await hashPassword(overrides.password || 'Password123!')),
    firstName,
    lastName,
    role: (overrides.role === 'admin' ? 'admin' : 'user') as 'user' | 'admin',
    emailVerified: overrides.emailVerified ?? false,
    phoneNumber,
    phoneVerified: overrides.phoneVerified ?? false,
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

/**
 * Generate test user settings
 */
export const generateTestUserSettings = (
  userId: string,
  overrides: Partial<TestUserSettings> = {}
) => {
  return {
    userId,
    defaultCurrency: overrides.defaultCurrency || 'USD',
    theme: overrides.theme || 'light',
    dateFormat: overrides.dateFormat || 'MM/DD/YYYY',
    timeFormat: overrides.timeFormat || '12h',
    notificationPreferences: overrides.notificationPreferences || {
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    },
    privacySettings: overrides.privacySettings || {
      hideBalances: false,
      requireAuthForSensitiveOperations: true,
    },
    appSettings: overrides.appSettings || {
      defaultView: 'dashboard',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'month',
    },
  };
};

/**
 * Generate a test account
 */
export const generateTestAccount = (
  userId: string,
  overrides: Partial<TestAccount> = {}
) => {
  const initialBalance =
    overrides.initialBalance !== undefined ? overrides.initialBalance : '0.00';
  const currentBalance =
    overrides.currentBalance !== undefined
      ? overrides.currentBalance
      : initialBalance;

  return {
    id: overrides.id || faker.string.uuid(),
    userId,
    name: overrides.name || faker.finance.accountName(),
    type: overrides.type || 'checking',
    currency: overrides.currency || 'USD',
    initialBalance,
    currentBalance,
    isArchived: overrides.isArchived ?? false,
    notes: overrides.notes !== undefined ? overrides.notes : null,
    icon: overrides.icon || 'bank',
    color: overrides.color || '#4285F4'.substring(0, 7),
    includeInNetWorth: overrides.includeInNetWorth ?? true,
    displayOrder:
      overrides.displayOrder !== undefined ? overrides.displayOrder : 0,
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

/**
 * Generate a test category
 */
export const generateTestCategory = (
  userId: string,
  overrides: Partial<TestCategory> = {}
) => {
  return {
    id: overrides.id || faker.string.uuid(),
    userId,
    parentId: overrides.parentId || null,
    name: overrides.name || faker.commerce.department().substring(0, 255),
    type: overrides.type || 'expense',
    icon: overrides.icon || 'tag',
    color: overrides.color || '#EA4335'.substring(0, 7),
    isDefault: overrides.isDefault ?? false,
    isSystem: overrides.isSystem ?? false,
    isArchived: overrides.isArchived ?? false,
    displayOrder:
      overrides.displayOrder !== undefined ? overrides.displayOrder : 0,
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

// Type definitions for test data
interface TestUser {
  id: string;
  email: string;
  passwordHash: string | null;
  password?: string;
  firstName: string | null;
  lastName: string | null;
  role: 'user' | 'admin';
  emailVerified: boolean;
  phoneNumber?: string | null;
  phoneVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface TestUserSettings {
  userId: string;
  defaultCurrency: string;
  theme: string;
  dateFormat: string;
  timeFormat: string;
  notificationPreferences: {
    budgetAlerts: boolean;
    lowBalanceAlerts: boolean;
    goalAchievedAlerts: boolean;
    weeklyReports: boolean;
    marketingEmails: boolean;
  };
  privacySettings: {
    hideBalances: boolean;
    requireAuthForSensitiveOperations: boolean;
  };
  appSettings: {
    defaultView: string;
    defaultAccountView: string;
    defaultTransactionPeriod: string;
  };
}

interface TestAccount {
  id: string;
  userId: string;
  name: string;
  type: string;
  currency: string;
  initialBalance: string;
  currentBalance: string;
  isArchived: boolean;
  notes?: string;
  icon?: string;
  color?: string;
  includeInNetWorth: boolean;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

interface TestCategory {
  id: string;
  userId: string;
  parentId: string | null;
  name: string;
  type: string;
  icon?: string;
  color?: string;
  isDefault: boolean;
  isSystem: boolean;
  isArchived: boolean;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Generate a test journal entry
 */
export const generateTestJournalEntry = (
  userId: string,
  overrides: Partial<TestJournalEntry> = {}
) => {
  return {
    id: overrides.id || faker.string.uuid(),
    userId,
    description:
      overrides.description || faker.lorem.sentence().substring(0, 255),
    date: overrides.date || new Date(),
    notes: overrides.notes !== undefined ? overrides.notes : null,
    isRecurring: overrides.isRecurring ?? false,
    recurringPattern: overrides.recurringPattern || null,
    status: overrides.status || 'completed',
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

/**
 * Generate a test journal line
 */
export const generateTestJournalLine = (
  journalEntryId: string,
  accountId: string,
  overrides: Partial<TestJournalLine> = {}
) => {
  return {
    id: overrides.id || faker.string.uuid(),
    journalEntryId,
    accountId,
    categoryId: overrides.categoryId || null,
    amount:
      overrides.amount || faker.finance.amount({ min: 1, max: 1000, dec: 2 }),
    type: overrides.type || 'debit',
    notes: overrides.notes !== undefined ? overrides.notes : null,
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

/**
 * Generate a test budget
 */
export const generateTestBudget = (
  userId: string,
  overrides: Partial<TestBudget> = {}
) => {
  return {
    id: overrides.id || faker.string.uuid(),
    userId,
    name: overrides.name || faker.lorem.words(3).substring(0, 255),
    amount:
      overrides.amount || faker.finance.amount({ min: 100, max: 5000, dec: 2 }),
    period: overrides.period || 'monthly',
    categoryId: overrides.categoryId || null,
    startDate: overrides.startDate || new Date(),
    endDate: overrides.endDate || null,
    isRecurring: overrides.isRecurring ?? false,
    isArchived: overrides.isArchived ?? false,
    notes: overrides.notes !== undefined ? overrides.notes : null,
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

/**
 * Generate a test goal
 */
export const generateTestGoal = (
  userId: string,
  overrides: Partial<TestGoal> = {}
) => {
  const targetAmount =
    overrides.targetAmount ||
    faker.finance.amount({ min: 1000, max: 50000, dec: 2 });

  return {
    id: overrides.id || faker.string.uuid(),
    userId,
    name: overrides.name || faker.lorem.words(3).substring(0, 255),
    type: overrides.type || 'savings',
    targetAmount,
    currentAmount: overrides.currentAmount || '0.00',
    currency: overrides.currency || 'USD',
    deadline: overrides.deadline !== undefined ? overrides.deadline : null,
    accountId: overrides.accountId || null,
    isArchived: overrides.isArchived ?? false,
    isCompleted: overrides.isCompleted ?? false,
    notes: overrides.notes !== undefined ? overrides.notes : null,
    icon: overrides.icon || 'target',
    color: overrides.color || '#4CAF50'.substring(0, 7),
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date(),
  };
};

interface TestJournalEntry {
  id: string;
  userId: string;
  description: string;
  date: Date;
  notes?: string;
  isRecurring: boolean;
  recurringPattern?: RecurringPattern | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TestJournalLine {
  id: string;
  journalEntryId: string;
  accountId: string;
  categoryId?: string | null;
  amount: string;
  type: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TestBudget {
  id: string;
  userId: string;
  name: string;
  amount: string;
  period: string;
  categoryId?: string | null;
  startDate: Date;
  endDate?: Date | null;
  isRecurring: boolean;
  isArchived: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TestGoal {
  id: string;
  userId: string;
  name: string;
  type: string;
  targetAmount: string;
  currentAmount: string;
  currency: string;
  deadline?: Date | null;
  accountId?: string | null;
  isArchived: boolean;
  isCompleted: boolean;
  notes?: string;
  icon?: string;
  color?: string;
  createdAt: Date;
  updatedAt: Date;
}
