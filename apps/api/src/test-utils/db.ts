import { exec } from 'node:child_process';
import { resolve } from 'node:path';
import { promisify } from 'node:util';
import { drizzle } from 'drizzle-orm/postgres-js';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
// Database utilities for testing
import { createClient } from '../database/client.js';
import * as schema from '../database/schema.js';

const execAsync = promisify(exec);

// Database client instance for tests
let dbClient: ReturnType<typeof postgres> | null = null;
let db: ReturnType<typeof drizzle> | null = null;

// Get the database client for testing
export const getTestDb = () => {
  // Ensure we're using the test database
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('getTestDb should only be called in test environment');
  }

  if (!db) {
    // Create a new database client if one doesn't exist
    if (!dbClient) {
      const databaseUrl = process.env.DATABASE_URL;
      if (!databaseUrl) {
        throw new Error('DATABASE_URL environment variable is not set');
      }
      // Configure postgres client to suppress NOTICE messages during tests
      dbClient = postgres(databaseUrl, {
        max: 1, // Use max: 1 for proper transaction support in tests
        // Suppress NOTICE messages (like CASCADE truncate notices) during tests
        onnotice: process.env.VITEST_VERBOSE === 'true' ? undefined : () => {},
        // Add connection timeout and retry settings for E2E tests
        connect_timeout: 5, // 5 second connection timeout
        idle_timeout: 10, // 10 second idle timeout
        max_lifetime: 60, // 60 second max connection lifetime
        // Retry connection once on failure
        connection: {
          application_name: 'budapp_test',
        },
      });
    }

    // Create a new Drizzle instance
    db = drizzle(dbClient, { schema });
  }

  return db;
};

/**
 * Set up the test database by running migrations
 */
export const setupTestDb = async () => {
  console.log('Setting up test database...');

  try {
    // Run migrations on the test database
    await execAsync('pnpm db:migrate', {
      env: { ...process.env, NODE_ENV: 'test' },
    });

    console.log('Test database migrations applied successfully');
    return true;
  } catch (error) {
    console.error('Failed to set up test database:', error);
    return false;
  }
};

/**
 * Seed the test database with test data
 */
export const seedTestDb = async () => {
  console.log('Seeding test database...');

  try {
    // Run the test seed script
    await execAsync('pnpm db:seed:test', {
      env: { ...process.env, NODE_ENV: 'test' },
    });

    console.log('Test database seeded successfully');
    return true;
  } catch (error) {
    console.error('Failed to seed test database:', error);
    return false;
  }
};

/**
 * Reset the test database by truncating all tables
 * NOTE: This is now only used for emergency cleanup, not regular test isolation
 */
export const resetTestDb = async () => {
  // Skip database reset for unit tests that use fake DATABASE_URL
  const databaseUrl = process.env.DATABASE_URL;

  // Check for unit test environment using multiple indicators
  const isUnitTest =
    process.env.TEST_SUITE_TYPE === 'unit' ||
    // Check if DATABASE_URL contains test credentials (indicates unit test)
    databaseUrl?.includes('postgresql://test:test@localhost:5432/test_db') ||
    // Check if we're running unit tests by looking at the call stack
    new Error().stack?.includes('/__tests__/unit/');

  if (isUnitTest) {
    // Unit tests should use mocks and not connect to real databases
    return true;
  }

  // Only log database reset in verbose mode or when there are issues
  const isVerbose =
    process.env.VITEST_VERBOSE === 'true' || process.env.DEBUG === 'true';
  if (isVerbose) {
    console.log('Resetting test database...');
  }

  const db = getTestDb();

  try {
    // Get a list of all tables in the public schema
    const result = await db.execute(
      `SELECT tablename FROM pg_tables WHERE schemaname = 'public'`
    );

    // Cast result to the expected type
    const tables = (result as unknown as Array<{ tablename: string }>).map(
      row => row.tablename
    );

    // Skip certain tables if needed
    const tablesToSkip = ['drizzle_migrations']; // Corrected table name for Drizzle
    const tablesToTruncate = tables.filter(
      (table: string) => !tablesToSkip.includes(table)
    );

    if (tablesToTruncate.length > 0) {
      // Disable foreign key constraints, truncate tables, then re-enable constraints
      await db.execute(`SET session_replication_role = 'replica'`);

      for (const table of tablesToTruncate) {
        await db.execute(`TRUNCATE TABLE "public"."${table}" CASCADE`); // Specify public schema
      }

      await db.execute(`SET session_replication_role = 'origin'`);
    }

    if (isVerbose) {
      console.log('Test database reset successfully');
    }
    return true;
  } catch (error) {
    console.error('Failed to reset test database:', error);
    return false;
  }
};

/**
 * Reset the database client cache
 * This is useful when the DATABASE_URL changes and we need to create a new connection
 */
export const resetDbClient = async () => {
  try {
    if (dbClient) {
      await dbClient.end();
    }
  } catch (error) {
    // Ignore errors when closing the connection
    console.warn('Warning: Error closing database connection:', error);
  } finally {
    dbClient = null;
    db = null;
  }
};

/**
 * Tear down the test database connection
 */
export const teardownTestDb = async () => {
  console.log('Tearing down test database connection...');

  try {
    await resetDbClient();
    console.log('Test database connection closed');
    return true;
  } catch (error) {
    console.error('Failed to tear down test database:', error);
    return false;
  }
};

/**
 * Create a transaction for a test
 * This is useful for tests that need to make changes to the database
 * but want to roll them back after the test is complete
 */
export const createTestTransaction = async () => {
  const db = getTestDb();

  // Start a transaction
  await db.execute('BEGIN');

  return {
    // Commit the transaction
    commit: async () => {
      await db.execute('COMMIT');
    },
    // Rollback the transaction
    rollback: async () => {
      await db.execute('ROLLBACK');
    },
  };
};

/**
 * Run a test with automatic transaction rollback for perfect isolation
 * This provides fast test isolation without truncating tables
 *
 * @param testFn The test function to run within a transaction
 */
export const withTransaction = async <T>(
  testFn: (db: ReturnType<typeof getTestDb>) => Promise<T>
): Promise<T> => {
  const db = getTestDb();

  try {
    // Start a transaction with timeout handling
    await db.execute('BEGIN');
  } catch (error) {
    // If we can't start a transaction due to connection issues,
    // provide a more helpful error message
    if (error instanceof Error && error.message.includes('CONNECT_TIMEOUT')) {
      throw new Error(
        'Database connection timeout. Please ensure the test database is available. ' +
          'For local development, consider setting up a local PostgreSQL instance.'
      );
    }
    throw error;
  }

  try {
    // Run the test function
    const result = await testFn(db);
    return result;
  } finally {
    // Always rollback the transaction to ensure test isolation
    // This is much faster than truncating tables
    try {
      await db.execute('ROLLBACK');
    } catch (rollbackError) {
      // If rollback fails, log the error but don't throw
      console.warn('Warning: Failed to rollback transaction:', rollbackError);
    }
  }
};

/**
 * Legacy function for backward compatibility
 * @deprecated Use withTransaction instead for better performance
 */
export const withDatabaseCleanup = async <T>(
  testFn: (db: ReturnType<typeof getTestDb>) => Promise<T>
): Promise<T> => {
  const db = getTestDb();

  try {
    // Run the test function
    const result = await testFn(db);
    return result;
  } finally {
    // Reset the database after the test (slow method)
    await resetTestDb();
  }
};

/**
 * Get access to seeded test data without isolation (read-only operations)
 * This is useful for tests that only need to read existing seed data
 * and don't need transaction isolation
 */
export const withSeededData = async <T>(
  testFn: (db: ReturnType<typeof getTestDb>) => Promise<T>
): Promise<T> => {
  const db = getTestDb();

  // Just run the test function with access to seeded data
  // No transaction needed for read-only operations
  return await testFn(db);
};

/**
 * Run multiple tests that can share the same transaction
 * Useful for test suites that need to set up complex data once
 * and run multiple assertions against it
 */
export const withSharedTransaction = async <T>(
  setupFn: (db: ReturnType<typeof getTestDb>) => Promise<void>,
  testFns: Array<(db: ReturnType<typeof getTestDb>) => Promise<T>>
): Promise<T[]> => {
  const db = getTestDb();

  // Start a transaction
  await db.execute('BEGIN');

  try {
    // Run setup once
    await setupFn(db);

    // Run all test functions
    const results: T[] = [];
    for (const testFn of testFns) {
      const result = await testFn(db);
      results.push(result);
    }

    return results;
  } finally {
    // Always rollback the transaction
    try {
      await db.execute('ROLLBACK');
    } catch (rollbackError) {
      console.warn(
        'Warning: Failed to rollback shared transaction:',
        rollbackError
      );
    }
  }
};
