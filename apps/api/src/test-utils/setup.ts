// This file is run before each test file
// It's a good place to set up per-test configuration

// Import vitest globals
import { afterEach, beforeEach, vi } from 'vitest';
import { resetTestDb } from './db.js';

// Set up global mocks if needed
// For example, you might want to mock console.log to reduce noise in tests
vi.spyOn(console, 'log').mockImplementation(() => {});
vi.spyOn(console, 'info').mockImplementation(() => {});
// Keep warnings and errors visible for debugging
// vi.spyOn(console, 'warn').mockImplementation(() => {});
// vi.spyOn(console, 'error').mockImplementation(() => {});

// Mock timers for consistent date/time handling in tests
// vi.useFakeTimers(); // Temporarily comment out
// vi.setSystemTime(new Date('2023-01-01T00:00:00.000Z')); // Temporarily comment out

// Per-test setup and teardown
beforeEach(async () => {
  // Ensure environment variables are loaded, especially if resetModules was used by a test file's own beforeEach
  await import('../config/env.js');

  // This runs before each test

  // NOTE: We no longer reset the database here for performance reasons.
  // Integration tests should use withTransaction() for test isolation,
  // which is much faster than truncating tables.

  // Unit tests should use mocks and not connect to a real database anyway.
});

afterEach(async () => {
  // This runs after each test

  // Clear all mocks between tests
  vi.clearAllMocks();

  // Reset any timers
  // vi.useRealTimers();
});
