// Global setup for Vitest
import { seedTestDb, setupTestDb } from './db.js';

// This file is run once before all tests
export default async function () {
  console.log('🚀 Global setup starting...');

  // Set NODE_ENV to test
  process.env.NODE_ENV = 'test';

  // Import environment variables
  await import('../config/env.js');

  // Set up the test database
  console.log('Setting up test database...');
  const dbSetup = await setupTestDb();
  if (!dbSetup) {
    throw new Error('Failed to set up test database');
  }

  // Seed the test database with test data
  console.log('Seeding test database...');
  const dbSeed = await seedTestDb();
  if (!dbSeed) {
    throw new Error('Failed to seed test database');
  }

  console.log('✅ Global setup completed successfully');
}
