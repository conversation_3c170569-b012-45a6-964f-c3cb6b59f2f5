import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
// Mock utilities for testing
import { vi } from 'vitest';
import { createMockJwtPayload } from '../auth-helpers.js';

// Export all mocking utilities
export * from './http.js';
export * from './graphql.js';
export * from './database.js';
export * from './services.js';

// Re-export auth.js with explicit exports to avoid naming conflicts
import { mockAuthService, mockJwtUtils, mockPasswordUtils } from './auth.js';

export { mockJwtUtils, mockPasswordUtils, mockAuthService };

/**
 * Create a mock Fastify request
 */
export const createMockRequest = (
  overrides: Partial<MockRequestOptions> = {}
): FastifyRequest => {
  const headers: Record<string, string> = {
    'content-type': 'application/json',
    ...(overrides.headers || {}),
  };

  if (overrides.token) {
    headers.authorization = `Bearer ${overrides.token}`;
  }

  return {
    headers,
    query: overrides.query || {},
    params: overrides.params || {},
    body: overrides.body || {},
    cookies: overrides.cookies || {},
    ip: overrides.ip || '127.0.0.1',
    method: overrides.method || 'GET',
    url: overrides.url || '/',
    log: {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
      fatal: vi.fn(),
    },
    server: overrides.server || ({} as FastifyInstance),
    id: overrides.id || 'request-id',
    raw: {} as unknown,
    context: overrides.context || {},
  } as unknown as FastifyRequest;
};

/**
 * Create a mock Fastify reply
 */
export const createMockReply = (): FastifyReply => {
  return {
    code: vi.fn().mockReturnThis(),
    header: vi.fn().mockReturnThis(),
    headers: vi.fn().mockReturnThis(),
    status: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    type: vi.fn().mockReturnThis(),
    redirect: vi.fn().mockReturnThis(),
    setCookie: vi.fn().mockReturnThis(),
    clearCookie: vi.fn().mockReturnThis(),
    serialize: vi.fn().mockReturnValue('{}'),
    log: {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
      fatal: vi.fn(),
    },
    raw: {} as unknown,
  } as unknown as FastifyReply;
};

/**
 * Create a mock GraphQL context
 */
export const createMockGraphQLContext = (
  overrides: Partial<MockGraphQLContextOptions> = {}
) => {
  const request = createMockRequest({
    token: overrides.token,
    headers: overrides.headers,
  });

  return {
    app: overrides.app || ({} as FastifyInstance),
    reply: overrides.reply || createMockReply(),
    request,
    userId:
      overrides.userId ||
      (overrides.authenticated
        ? '00000000-0000-0000-0000-000000000001'
        : undefined),
    user:
      overrides.user ||
      (overrides.authenticated
        ? createMockJwtPayload({ userId: overrides.userId })
        : undefined),
    authenticated: overrides.authenticated ?? !!overrides.userId,
  };
};

/**
 * Mock OAuth providers
 */
export const mockOAuthProviders = () => {
  // Mock Google OAuth
  vi.mock('passport-google-oauth20', () => {
    return {
      Strategy: vi.fn().mockImplementation((options, verify) => {
        return {
          name: 'google',
          authenticate: vi.fn().mockImplementation(function (
            this: {
              success: (user: unknown, info?: unknown) => void;
              error: (err: Error) => void;
            },
            req: unknown
          ) {
            const profile = {
              id: 'google-123456',
              displayName: 'Test Google User',
              name: { givenName: 'Test', familyName: 'User' },
              emails: [{ value: '<EMAIL>' }],
              photos: [{ value: 'https://example.com/photo.jpg' }],
              provider: 'google',
            };

            const accessToken = 'mock-google-access-token';
            const refreshToken = 'mock-google-refresh-token';

            verify(
              accessToken,
              refreshToken,
              profile,
              (err: Error | null, user: unknown) => {
                if (err) {
                  this.error(err);
                } else {
                  this.success(user);
                }
              }
            );
          }),
        };
      }),
    };
  });

  // Mock Apple OAuth
  vi.mock('passport-apple', () => {
    return {
      Strategy: vi.fn().mockImplementation((options, verify) => {
        return {
          name: 'apple',
          authenticate: vi.fn().mockImplementation(function (
            this: {
              success: (user: unknown, info?: unknown) => void;
              error: (err: Error) => void;
            },
            req: unknown
          ) {
            const profile = {
              id: 'apple-123456',
              displayName: 'Test Apple User',
              name: { givenName: 'Test', familyName: 'User' },
              emails: [{ value: '<EMAIL>' }],
              provider: 'apple',
            };

            const accessToken = 'mock-apple-access-token';
            const refreshToken = 'mock-apple-refresh-token';

            verify(
              accessToken,
              refreshToken,
              profile,
              (err: Error | null, user: unknown) => {
                if (err) {
                  this.error(err);
                } else {
                  this.success(user);
                }
              }
            );
          }),
        };
      }),
    };
  });
};

// Type definitions
interface MockRequestOptions {
  headers?: Record<string, string>;
  query?: Record<string, unknown>;
  params?: Record<string, unknown>;
  body?: unknown;
  cookies?: Record<string, string>;
  ip?: string;
  method?: string;
  url?: string;
  server?: FastifyInstance;
  id?: string;
  token?: string;
  context?: Record<string, unknown>;
}

interface MockGraphQLContextOptions {
  app?: FastifyInstance;
  reply?: FastifyReply;
  request?: FastifyRequest;
  userId?: string;
  user?: unknown;
  authenticated?: boolean;
  token?: string;
  headers?: Record<string, string>;
}
