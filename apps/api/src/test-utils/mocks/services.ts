import { http, HttpResponse } from 'msw';
// External services mocking utilities
import { vi } from 'vitest';
import { createMockServer, errorResponse, jsonResponse } from './http.js';

/**
 * Create mocks for email service (Resend)
 */
export const createEmailServiceMocks = () => {
  return [
    // Mock Resend API for sending emails
    http.post('https://api.resend.com/emails', async ({ request }) => {
      // Type the request body properly
      type EmailRequest = {
        from: string;
        to: string;
        subject: string;
        text?: string;
        html?: string;
      };

      const body = (await request.json()) as EmailRequest;

      // Validate required fields
      if (!body.from || !body.to || !body.subject) {
        return errorResponse('Missing required fields', 400);
      }

      return jsonResponse({
        id: 'mock-email-id',
        from: body.from,
        to: body.to,
        subject: body.subject,
        status: 'sent',
      });
    }),
  ];
};

/**
 * Create mocks for payment service (RevenueCat)
 */
export const createPaymentServiceMocks = () => {
  return [
    // Mock RevenueCat API for checking subscription status
    http.get('https://api.revenuecat.com/v1/subscribers/:userId', () => {
      return jsonResponse({
        subscriber: {
          original_app_user_id: 'mock-user-id',
          subscriptions: {
            premium_monthly: {
              expires_date: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              purchase_date: new Date(
                Date.now() - 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              store: 'app_store',
              is_active: true,
            },
          },
          entitlements: {
            premium: {
              expires_date: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              product_identifier: 'premium_monthly',
              purchase_date: new Date(
                Date.now() - 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              is_active: true,
            },
          },
        },
      });
    }),

    // Mock RevenueCat API for creating a subscription
    http.post('https://api.revenuecat.com/v1/receipts', async () => {
      return jsonResponse({
        subscriber: {
          original_app_user_id: 'mock-user-id',
          subscriptions: {
            premium_monthly: {
              expires_date: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              purchase_date: new Date().toISOString(),
              store: 'app_store',
              is_active: true,
            },
          },
          entitlements: {
            premium: {
              expires_date: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              product_identifier: 'premium_monthly',
              purchase_date: new Date().toISOString(),
              is_active: true,
            },
          },
        },
      });
    }),
  ];
};

/**
 * Create mocks for notification service (Firebase Cloud Messaging)
 */
export const createNotificationServiceMocks = () => {
  return [
    // Mock Firebase Cloud Messaging API for sending notifications
    http.post(
      'https://fcm.googleapis.com/v1/projects/*/messages:send',
      async ({ request }) => {
        // Type the request body properly
        type FCMRequest = {
          message: {
            token: string;
            notification?: {
              title?: string;
              body?: string;
            };
            data?: Record<string, string>;
          };
        };

        const body = (await request.json()) as FCMRequest;

        // Validate required fields
        if (!body.message || !body.message.token) {
          return errorResponse('Missing required fields', 400);
        }

        return jsonResponse({
          name: 'projects/budapp/messages/mock-message-id',
        });
      }
    ),
  ];
};

/**
 * Create mocks for all external services
 */
export const createAllServiceMocks = () => {
  return [
    ...createEmailServiceMocks(),
    ...createPaymentServiceMocks(),
    ...createNotificationServiceMocks(),
  ];
};

/**
 * Example usage:
 *
 * import { describe, it, expect } from 'vitest';
 * import { createMockServer } from '../test-utils/mocks/http';
 * import { createEmailServiceMocks } from '../test-utils/mocks/services';
 * import { EmailService } from '../services/email.service';
 *
 * // Set up the mock server with email service mocks
 * createMockServer(createEmailServiceMocks());
 *
 * describe('EmailService', () => {
 *   it('should send an email', async () => {
 *     const emailService = new EmailService();
 *
 *     const result = await emailService.sendEmail({
 *       from: '<EMAIL>',
 *       to: '<EMAIL>',
 *       subject: 'Test Email',
 *       text: 'This is a test email',
 *     });
 *
 *     expect(result).toEqual({
 *       id: 'mock-email-id',
 *       status: 'sent',
 *     });
 *   });
 * });
 */
