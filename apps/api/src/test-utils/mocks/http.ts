// HTTP mocking utilities using MSW (Mock Service Worker)
import {
  http,
  HttpResponse,
  type RequestHandler,
  delay,
  passthrough,
} from 'msw';
import { setupServer } from 'msw/node';
import { afterAll, afterEach, beforeAll } from 'vitest';

/**
 * Create a mock HTTP server using MSW
 * @param handlers - Array of MSW request handlers
 * @returns MSW server instance
 */
export const createMockServer = (handlers: RequestHandler[] = []) => {
  const server = setupServer(...handlers);

  // Start the server before all tests
  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }));

  // Reset handlers after each test
  afterEach(() => server.resetHandlers());

  // Close the server after all tests
  afterAll(() => server.close());

  return server;
};

/**
 * Create a JSON response
 * @param data - Response data
 * @param status - HTTP status code
 * @returns MSW HTTP response
 */
export const jsonResponse = (data: unknown, status = 200) => {
  return HttpResponse.json(data as Record<string, unknown> | unknown[], {
    status,
  });
};

/**
 * Create an error response
 * @param message - Error message
 * @param status - HTTP status code
 * @returns MSW HTTP response
 */
export const errorResponse = (message: string, status = 500) => {
  return HttpResponse.json({ error: { message } }, { status });
};

/**
 * Create a mock for OAuth providers
 */
export const createOAuthMocks = () => {
  return [
    // Mock Google OAuth token endpoint
    http.post('https://oauth2.googleapis.com/token', async () => {
      await delay(100); // Add a small delay to simulate network latency
      return jsonResponse({
        access_token: 'mock-google-access-token',
        refresh_token: 'mock-google-refresh-token',
        id_token: 'mock-google-id-token',
        token_type: 'Bearer',
        expires_in: 3600,
      });
    }),

    // Mock Google user info endpoint
    http.get('https://www.googleapis.com/oauth2/v3/userinfo', async () => {
      await delay(100);
      return jsonResponse({
        sub: 'google-123456',
        name: 'Test Google User',
        given_name: 'Test',
        family_name: 'User',
        email: '<EMAIL>',
        email_verified: true,
        picture: 'https://example.com/photo.jpg',
      });
    }),

    // Mock Apple OAuth token endpoint
    http.post('https://appleid.apple.com/auth/token', async () => {
      await delay(100);
      return jsonResponse({
        access_token: 'mock-apple-access-token',
        refresh_token: 'mock-apple-refresh-token',
        id_token: 'mock-apple-id-token',
        token_type: 'Bearer',
        expires_in: 3600,
      });
    }),

    // Mock Apple user info endpoint (Apple doesn't have a dedicated endpoint, but we mock it for consistency)
    http.post('https://appleid.apple.com/auth/userinfo', async () => {
      await delay(100);
      return jsonResponse({
        sub: 'apple-123456',
        name: 'Test Apple User',
        email: '<EMAIL>',
        email_verified: true,
      });
    }),
  ];
};

/**
 * Example usage in a test file:
 *
 * import { describe, it, expect } from 'vitest';
 * import { http, HttpResponse } from 'msw';
 * import { createMockServer, jsonResponse } from '../test-utils/mocks/http';
 *
 * // Create mock handlers
 * const handlers = [
 *   http.get('https://api.example.com/users', () => {
 *     return jsonResponse([{ id: 1, name: 'Test User' }]);
 *   })
 * ];
 *
 * // Set up the mock server
 * createMockServer(handlers);
 *
 * describe('API Client', () => {
 *   it('fetches users', async () => {
 *     const response = await fetch('https://api.example.com/users');
 *     const data = await response.json();
 *     expect(data).toEqual([{ id: 1, name: 'Test User' }]);
 *   });
 * });
 */
