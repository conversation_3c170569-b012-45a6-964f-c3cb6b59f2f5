import { http, HttpResponse } from 'msw';
// Authentication mocking utilities
import { vi } from 'vitest';
import type { JwtPayload } from '../../lib/auth/jwt.js';
import type {
  LoginInput,
  RegisterInput,
} from '../../services/auth/auth.types.js';
import { createMockJwtPayload } from '../auth-helpers.js';
import { errorResponse, jsonResponse } from './http.js';

/**
 * Create mocks for JWT utilities
 */
export const mockJwtUtils = () => {
  vi.mock('../../lib/auth/jwt.js', () => ({
    generateToken: vi.fn().mockImplementation((payload: JwtPayload) => {
      return `mock-jwt-token-${payload.userId}`;
    }),
    verifyToken: vi.fn().mockImplementation((token: string) => {
      if (!token || token === 'invalid-token') {
        return null;
      }

      // Extract user ID from the mock token
      const match = token.match(/mock-jwt-token-(.+)/);
      const userId = match ? match[1] : '00000000-0000-0000-0000-000000000001';

      return createMockJwtPayload({ userId });
    }),
    JwtPayload: vi.fn(),
  }));
};

/**
 * Create mocks for password utilities
 */
export const mockPasswordUtils = () => {
  vi.mock('../../lib/auth/password.js', () => ({
    hashPassword: vi.fn().mockImplementation(async (password: string) => {
      return `hashed-${password}`;
    }),
    verifyPassword: vi
      .fn()
      .mockImplementation(async (password: string, hash: string) => {
        return hash === `hashed-${password}`;
      }),
  }));
};

/**
 * Create mocks for OAuth providers
 */
export const createOAuthMocks = () => {
  return [
    // Mock Google OAuth token endpoint
    http.post('https://oauth2.googleapis.com/token', async () => {
      return jsonResponse({
        access_token: 'mock-google-access-token',
        refresh_token: 'mock-google-refresh-token',
        id_token: 'mock-google-id-token',
        token_type: 'Bearer',
        expires_in: 3600,
      });
    }),

    // Mock Google user info endpoint
    http.get('https://www.googleapis.com/oauth2/v3/userinfo', async () => {
      return jsonResponse({
        sub: 'google-123456',
        name: 'Test Google User',
        given_name: 'Test',
        family_name: 'User',
        email: '<EMAIL>',
        email_verified: true,
        picture: 'https://example.com/photo.jpg',
      });
    }),

    // Mock Apple OAuth token endpoint
    http.post('https://appleid.apple.com/auth/token', async () => {
      return jsonResponse({
        access_token: 'mock-apple-access-token',
        refresh_token: 'mock-apple-refresh-token',
        id_token: 'mock-apple-id-token',
        token_type: 'Bearer',
        expires_in: 3600,
      });
    }),

    // Mock Apple user info endpoint (Apple doesn't have a dedicated endpoint, but we mock it for consistency)
    http.post('https://appleid.apple.com/auth/userinfo', async () => {
      return jsonResponse({
        sub: 'apple-123456',
        name: 'Test Apple User',
        email: '<EMAIL>',
        email_verified: true,
      });
    }),
  ];
};

/**
 * Mock the authentication service
 */
export const mockAuthService = () => {
  vi.mock('../../services/auth/auth.service.js', () => ({
    AuthService: vi.fn().mockImplementation(() => ({
      register: vi.fn().mockImplementation(async (input: RegisterInput) => {
        return {
          user: {
            id: 'mock-user-id',
            email: input.email,
            firstName: input.firstName,
            lastName: input.lastName,
            role: 'user',
            emailVerified: false,
          },
          token: 'mock-jwt-token-mock-user-id',
        };
      }),
      login: vi.fn().mockImplementation(async (input: LoginInput) => {
        if (input.email === '<EMAIL>') {
          throw new Error('Invalid credentials');
        }

        return {
          user: {
            id: 'mock-user-id',
            email: input.email,
            firstName: 'Test',
            lastName: 'User',
            role: 'user',
            emailVerified: true,
          },
          token: 'mock-jwt-token-mock-user-id',
        };
      }),
      refreshToken: vi.fn().mockImplementation(async () => {
        return {
          user: {
            id: 'mock-user-id',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            role: 'user',
            emailVerified: true,
          },
          token: 'mock-jwt-token-mock-user-id',
        };
      }),
      logout: vi.fn().mockResolvedValue(true),
      getUserById: vi.fn().mockImplementation(async (id: string) => {
        if (id === 'not-found') {
          return null;
        }

        return {
          id,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: 'user',
          emailVerified: true,
        };
      }),
      handleOAuthCallback: vi
        .fn()
        .mockImplementation(async (provider: string) => {
          return {
            user: {
              id: 'mock-oauth-user-id',
              email: `${provider}-<EMAIL>`,
              firstName: 'Test',
              lastName: 'User',
              role: 'user',
              emailVerified: true,
            },
            token: 'mock-jwt-token-mock-oauth-user-id',
          };
        }),
    })),
  }));
};

/**
 * Example usage:
 *
 * import { describe, it, expect, beforeEach, afterEach } from 'vitest';
 * import { createMockServer } from '../test-utils/mocks/http';
 * import { createOAuthMocks, mockJwtUtils, mockPasswordUtils } from '../test-utils/mocks/auth';
 * import { AuthService } from '../services/auth/auth.service';
 *
 * describe('AuthService', () => {
 *   beforeEach(() => {
 *     // Mock JWT and password utilities
 *     mockJwtUtils();
 *     mockPasswordUtils();
 *
 *     // Set up the mock server with OAuth mocks
 *     createMockServer(createOAuthMocks());
 *   });
 *
 *   it('should register a new user', async () => {
 *     const authService = new AuthService();
 *
 *     const result = await authService.register({
 *       email: '<EMAIL>',
 *       password: 'Password123!',
 *       firstName: 'Test',
 *       lastName: 'User',
 *     });
 *
 *     expect(result.user.email).toBe('<EMAIL>');
 *     expect(result.token).toBeDefined();
 *   });
 * });
 */
