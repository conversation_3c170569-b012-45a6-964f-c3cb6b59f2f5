import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
// Database mocking utilities
import { vi } from 'vitest';
import * as schema from '../../database/schema.js';
import { getTestDb } from '../db.js';

/**
 * Create a mock database client
 * @returns Mock database client
 */
export const createMockDb = () => {
  // Create a mock of all the Drizzle ORM methods
  const mockSelect = vi.fn().mockReturnThis();
  const mockInsert = vi.fn().mockReturnThis();
  const mockUpdate = vi.fn().mockReturnThis();
  const mockDelete = vi.fn().mockReturnThis();

  const mockDb = {
    select: mockSelect,
    selectDistinct: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    offset: vi.fn().mockReturnThis(),
    groupBy: vi.fn().mockReturnThis(),
    having: vi.fn().mockReturnThis(),
    with: vi.fn().mockReturnThis(),
    insert: mockInsert,
    update: mockUpdate,
    delete: mockDelete,
    values: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
    execute: vi.fn().mockResolvedValue([]),
    get: vi.fn().mockResolvedValue(null),
    all: vi.fn().mockResolvedValue([]),
    run: vi.fn().mockResolvedValue({ rowCount: 0 }),
    transaction: vi.fn().mockImplementation(callback => {
      return Promise.resolve(callback(mockDb));
    }),
  };

  return mockDb as unknown as PostgresJsDatabase<typeof schema>;
};

/**
 * Mock the database client for a specific test
 * @param mockImplementation - Mock implementation for the database client
 * @returns Cleanup function to restore the original implementation
 */
export const mockDatabase = (
  mockImplementation: Partial<ReturnType<typeof createMockDb>> = {}
) => {
  // Save the original getTestDb function
  const originalGetTestDb = vi.fn();

  // Create a mock database client with the provided implementation
  const mockDb = {
    ...createMockDb(),
    ...mockImplementation,
  };

  // Mock the getTestDb function to return our mock database client
  vi.mock('../db.js', () => ({
    getTestDb: vi.fn().mockReturnValue(mockDb),
  }));

  // Return a cleanup function to restore the original implementation
  return () => {
    vi.restoreAllMocks();
  };
};

/**
 * Mock a specific table operation
 * @param table - Table to mock
 * @param operation - Operation to mock (select, insert, update, delete)
 * @param returnValue - Value to return from the operation
 * @returns Mock implementation
 */
export const mockTableOperation = (
  table: keyof typeof schema,
  operation: 'select' | 'insert' | 'update' | 'delete',
  returnValue: unknown
) => {
  const mockDb = createMockDb();

  // Create a chain of mock functions that eventually returns the provided value
  switch (operation) {
    case 'select': {
      // Use vi.fn() to create a new mock function that we can add implementation to
      const mockSelectFn = vi.fn().mockImplementation(() => ({
        from: vi.fn().mockImplementation(t => {
          if (t === schema[table]) {
            return {
              where: vi.fn().mockReturnThis(),
              orderBy: vi.fn().mockReturnThis(),
              limit: vi.fn().mockReturnThis(),
              offset: vi.fn().mockReturnThis(),
              groupBy: vi.fn().mockReturnThis(),
              having: vi.fn().mockReturnThis(),
              execute: vi.fn().mockResolvedValue(returnValue),
              get: vi
                .fn()
                .mockResolvedValue(
                  Array.isArray(returnValue) ? returnValue[0] || null : null
                ),
              all: vi.fn().mockResolvedValue(returnValue),
            };
          }
          return mockDb;
        }),
      }));
      // Replace the select method with our mock implementation
      Object.defineProperty(mockDb, 'select', {
        value: mockSelectFn,
        configurable: true,
      });
      break;
    }

    case 'insert': {
      // Use vi.fn() to create a new mock function that we can add implementation to
      const mockInsertFn = vi.fn().mockImplementation(t => {
        if (t === schema[table]) {
          return {
            values: vi.fn().mockReturnThis(),
            returning: vi.fn().mockReturnThis(),
            execute: vi.fn().mockResolvedValue(returnValue),
            run: vi.fn().mockResolvedValue({
              rowCount: Array.isArray(returnValue) ? returnValue.length : 1,
            }),
          };
        }
        return mockDb;
      });
      // Replace the insert method with our mock implementation
      Object.defineProperty(mockDb, 'insert', {
        value: mockInsertFn,
        configurable: true,
      });
      break;
    }

    case 'update': {
      // Use vi.fn() to create a new mock function that we can add implementation to
      const mockUpdateFn = vi.fn().mockImplementation(t => {
        if (t === schema[table]) {
          return {
            set: vi.fn().mockReturnThis(),
            where: vi.fn().mockReturnThis(),
            returning: vi.fn().mockReturnThis(),
            execute: vi.fn().mockResolvedValue(returnValue),
            run: vi.fn().mockResolvedValue({
              rowCount: Array.isArray(returnValue) ? returnValue.length : 1,
            }),
          };
        }
        return mockDb;
      });
      // Replace the update method with our mock implementation
      Object.defineProperty(mockDb, 'update', {
        value: mockUpdateFn,
        configurable: true,
      });
      break;
    }

    case 'delete': {
      // Use vi.fn() to create a new mock function that we can add implementation to
      const mockDeleteFn = vi.fn().mockImplementation(t => {
        if (t === schema[table]) {
          return {
            where: vi.fn().mockReturnThis(),
            returning: vi.fn().mockReturnThis(),
            execute: vi.fn().mockResolvedValue(returnValue),
            run: vi.fn().mockResolvedValue({
              rowCount: Array.isArray(returnValue) ? returnValue.length : 1,
            }),
          };
        }
        return mockDb;
      });
      // Replace the delete method with our mock implementation
      Object.defineProperty(mockDb, 'delete', {
        value: mockDeleteFn,
        configurable: true,
      });
      break;
    }
  }

  return mockDb;
};

/**
 * Example usage:
 *
 * import { describe, it, expect, beforeEach, afterEach } from 'vitest';
 * import { mockDatabase, mockTableOperation } from '../test-utils/mocks/database';
 * import { users } from '../../database/schema.js';
 * import { UserService } from '../services/user.service';
 *
 * describe('UserService', () => {
 *   let cleanup: () => void;
 *
 *   beforeEach(() => {
 *     // Mock the database for this test suite
 *     cleanup = mockDatabase();
 *   });
 *
 *   afterEach(() => {
 *     // Restore the original implementation
 *     cleanup();
 *   });
 *
 *   it('should get a user by ID', async () => {
 *     // Mock the users.select operation to return a specific user
 *     const mockUser = { id: '123', email: '<EMAIL>', name: 'Test User' };
 *     mockDatabase(mockTableOperation('users', 'select', [mockUser]));
 *
 *     // Call the service method
 *     const userService = new UserService();
 *     const user = await userService.getUserById('123');
 *
 *     // Verify the result
 *     expect(user).toEqual(mockUser);
 *   });
 * });
 */
