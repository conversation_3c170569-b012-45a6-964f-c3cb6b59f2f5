import { http, HttpResponse } from 'msw';
// GraphQL mocking utilities
import { vi } from 'vitest';
import { errorResponse, jsonResponse } from './http.js';
import { createMockGraphQLContext } from './index.js';

/**
 * Create a mock GraphQL response
 * @param data - Response data
 * @param errors - GraphQL errors
 * @returns MSW HTTP response
 */
interface GraphQLError {
  message: string;
  extensions?: { code?: string };
  path?: string[];
}

interface GraphQLResponse {
  data?: unknown;
  errors?: GraphQLError[];
}

export const graphqlResponse = (
  data: unknown = null,
  errors: GraphQLError[] = []
) => {
  const response: GraphQLResponse = {};

  if (data !== null) {
    response.data = data;
  }

  if (errors && errors.length > 0) {
    response.errors = errors;
  }

  return HttpResponse.json(response);
};

/**
 * Create a GraphQL error
 * @param message - Error message
 * @param code - Error code
 * @param path - Error path
 * @returns GraphQL error object
 */
export const graphqlError = (
  message: string,
  code = 'INTERNAL_SERVER_ERROR',
  path: string[] = []
) => {
  return {
    message,
    extensions: { code },
    path,
  };
};

/**
 * Create a mock for GraphQL operations
 * @param url - GraphQL endpoint URL
 * @param operations - Map of operation names to handlers
 * @returns Array of MSW handlers
 */
type MockOperation = (variables: Record<string, unknown>) => Response;

export const createGraphQLMocks = (
  url = 'http://localhost/graphql',
  operations: Record<string, MockOperation> = {}
) => {
  return [
    http.post(url, async ({ request }) => {
      // Type the request body properly
      type GraphQLRequest = {
        query: string;
        variables?: Record<string, unknown>;
        operationName?: string;
      };

      const body = (await request.json()) as GraphQLRequest;
      const { query, variables, operationName } = body;

      // Extract operation name from the query if not provided
      const extractedOperationName =
        operationName || extractOperationName(query);

      // Check if we have a mock for this operation
      if (extractedOperationName && operations[extractedOperationName]) {
        const mockFn = operations[extractedOperationName];

        // Call the mock function with variables
        return mockFn(variables || {});
      }

      // If no mock is found, return an error
      return graphqlResponse(null, [
        graphqlError(
          `No mock defined for GraphQL operation: ${extractedOperationName || 'unknown'}`,
          'NOT_IMPLEMENTED'
        ),
      ]);
    }),
  ];
};

/**
 * Extract operation name from a GraphQL query string
 * @param query - GraphQL query string
 * @returns Operation name or null if not found
 */
export const extractOperationName = (query: string): string | null => {
  // Simple regex to extract operation name
  // This is a basic implementation and might not work for all cases
  const match = query.match(
    /(?:query|mutation|subscription)\s+([A-Za-z0-9_]+)/
  );
  return match ? match[1] : null;
};

/**
 * Create a mock resolver function
 * @param returnValue - Value to return from the resolver
 * @returns Mock resolver function
 */
export const mockResolver = (returnValue: unknown) => {
  return vi.fn().mockResolvedValue(returnValue);
};

/**
 * Create a mock resolver that throws an error
 * @param message - Error message
 * @param code - Error code
 * @returns Mock resolver function
 */
export const mockResolverWithError = (
  message: string,
  code = 'INTERNAL_SERVER_ERROR'
) => {
  return vi.fn().mockRejectedValue(new Error(message));
};

/**
 * Create a mock GraphQL context with authentication
 * @param authenticated - Whether the user is authenticated
 * @param role - User role
 * @returns Mock GraphQL context
 */
export const mockAuthenticatedContext = (
  authenticated = true,
  role = 'user'
) => {
  return createMockGraphQLContext({
    authenticated,
    user: authenticated ? { role } : undefined,
  });
};

/**
 * Example usage:
 *
 * import { describe, it, expect } from 'vitest';
 * import { createMockServer } from '../test-utils/mocks/http';
 * import { createGraphQLMocks, graphqlResponse, graphqlError } from '../test-utils/mocks/graphql';
 *
 * // Create mock handlers for GraphQL operations
 * const graphqlMocks = createGraphQLMocks('/graphql', {
 *   // Mock the 'GetUser' query
 *   GetUser: (variables) => {
 *     if (variables.id === '123') {
 *       return graphqlResponse({
 *         user: { id: '123', name: 'Test User' }
 *       });
 *     }
 *
 *     return graphqlResponse(null, [
 *       graphqlError('User not found', 'NOT_FOUND', ['user'])
 *     ]);
 *   },
 *
 *   // Mock the 'CreateUser' mutation
 *   CreateUser: (variables) => {
 *     return graphqlResponse({
 *       createUser: { id: '456', name: variables.input.name }
 *     });
 *   }
 * });
 *
 * // Set up the mock server
 * createMockServer(graphqlMocks);
 *
 * describe('GraphQL Client', () => {
 *   it('fetches a user', async () => {
 *     const response = await fetch('/graphql', {
 *       method: 'POST',
 *       headers: { 'Content-Type': 'application/json' },
 *       body: JSON.stringify({
 *         query: `query GetUser($id: ID!) { user(id: $id) { id name } }`,
 *         variables: { id: '123' }
 *       })
 *     });
 *
 *     const result = await response.json();
 *     expect(result.data.user).toEqual({ id: '123', name: 'Test User' });
 *   });
 * });
 */
