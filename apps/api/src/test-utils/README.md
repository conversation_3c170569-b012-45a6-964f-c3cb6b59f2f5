# BudApp Testing Utilities

This directory contains utilities for testing the BudApp API. These utilities are designed to make it easier to write tests for the API by providing common functionality for mocking, database management, and more.

## Directory Structure

- `auth-helpers.js`: Utilities for authentication in tests
- `db.js`: Utilities for database management in tests
- `gql-client.ts`: GraphQL client for testing
- `global-setup.ts`: Global setup for Vitest
- `global-teardown.ts`: Global teardown for Vitest
- `setup.ts`: Setup for individual test files
- `test-data.ts`: Utilities for generating test data
- `mocks/`: Mocking utilities
  - `index.ts`: Main entry point for mocking utilities
  - `http.ts`: HTTP mocking utilities using MSW
  - `graphql.ts`: GraphQL mocking utilities
  - `database.ts`: Database mocking utilities
  - `auth.ts`: Authentication mocking utilities
  - `services.ts`: External services mocking utilities

## Usage

### Database Utilities

The `db.js` file provides utilities for managing the test database:

```typescript
import { getTestDb, setupTestDb, seedTestDb, resetTestDb } from '../test-utils/db.js';

// Get the test database client
const db = getTestDb();

// Set up the test database
await setupTestDb();

// Seed the test database with test data
await seedTestDb();

// Reset the test database to a clean state
await resetTestDb();
```

### GraphQL Client

The `gql-client.ts` file provides a GraphQL client for testing:

```typescript
import { createTestClient, gqlQueries } from '../test-utils/gql-client.js';

// Create a test GraphQL client
const client = await createTestClient();

// Log in as a user
await client.loginAsUser();

// Make a GraphQL query
const response = await client.query(gqlQueries.me);

// Make a GraphQL mutation
const response = await client.mutate(gqlQueries.login, {
  input: {
    email: '<EMAIL>',
    password: 'Password123!'
  }
});
```

### Mocking Utilities

The `mocks/` directory provides utilities for mocking HTTP requests, GraphQL operations, database operations, and more:

```typescript
import {
  createMockServer,
  jsonResponse,
  createGraphQLMocks,
  graphqlResponse,
  mockDatabase,
  mockTableOperation,
  mockJwtUtils,
  mockPasswordUtils,
  createOAuthMocks,
  createEmailServiceMocks
} from '../test-utils/mocks';

// Create a mock server with HTTP handlers
const httpHandlers = [
  http.get('/api/users', () => {
    return jsonResponse([{ id: 1, name: 'Test User' }]);
  })
];

createMockServer(httpHandlers);

// Create a mock server with GraphQL handlers
const graphqlHandlers = createGraphQLMocks('/graphql', {
  GetUser: (variables) => {
    return graphqlResponse({
      user: { id: variables.id, name: 'Test User' }
    });
  }
});

createMockServer(graphqlHandlers);

// Mock database operations
mockDatabase(mockTableOperation('users', 'select', [{ id: 1, name: 'Test User' }]));

// Mock JWT and password utilities
mockJwtUtils();
mockPasswordUtils();

// Mock OAuth providers
createMockServer(createOAuthMocks());

// Mock external services
createMockServer(createEmailServiceMocks());
```

### Authentication Helpers

The `auth-helpers.js` file provides utilities for authentication in tests:

```typescript
import {
  createTestUser,
  generateTestToken,
  createTestUserWithToken,
  createTestAdminWithToken,
  createMockJwtPayload
} from '../test-utils/auth-helpers.js';

// Create a test user in the database
const user = await createTestUser();

// Generate a JWT token for a user
const token = generateTestToken(user.id, user.email, user.role);

// Create a test user and generate a token
const { user, token } = await createTestUserWithToken();

// Create a test admin user and generate a token
const { user, token } = await createTestAdminWithToken();

// Create a mock JWT payload
const payload = createMockJwtPayload({ userId: '123' });
```

### Test Data

The `test-data.ts` file provides utilities for generating test data:

```typescript
import { generateTestUser } from '../test-utils/test-data.js';

// Generate a test user
const user = await generateTestUser({
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>'
});
```

## Global Setup and Teardown

The `global-setup.ts` file is run once before all tests, and the `setup.ts` file is run before each test file. These files set up the test environment, including the database, mocks, and more. The `global-teardown.ts` file is run once after all tests to clean up resources, such as closing database connections.

## Coverage Reporting

The test suite is configured to generate coverage reports using Istanbul. To run tests with coverage:

```bash
pnpm test:coverage
```

This will generate a coverage report in the `coverage` directory. You can also run:

```bash
pnpm test:coverage:summary
```

For a more concise summary of the coverage report, or:

```bash
pnpm test:coverage:json
```

To generate a JSON summary that can be used by CI tools.

The coverage configuration includes:
- Minimum thresholds (80% for statements, functions, and lines; 70% for branches)
- Exclusion of test files, type definitions, and test utilities
- Text, summary, and JSON report formats

## Example Tests

For examples of how to use these utilities, see the `__tests__/examples/` directory.

## Documentation

For more detailed documentation on the mocking strategy, see the `docs/testing/mocking-strategy.md` file.
