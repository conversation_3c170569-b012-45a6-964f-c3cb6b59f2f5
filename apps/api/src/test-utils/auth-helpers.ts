import { userRoleEnum, userSettings, users } from '../database/schema.js';
import type { User } from '../database/types.js';
// Authentication helpers for testing
import { type JwtPayload, generateToken } from '../lib/auth/jwt.js';
import { getTestDb } from './db.js';
import { generateTestUser } from './test-data.js';

/**
 * Create a test user in the database
 */
export const createTestUser = async (overrides: Partial<User> = {}) => {
  const db = getTestDb();

  // Generate a test user
  const user = await generateTestUser(overrides);

  // Ensure role is valid using the enum values
  const role =
    user.role === 'admin'
      ? userRoleEnum.enumValues[1]
      : userRoleEnum.enumValues[0];

  // Insert the user into the database with proper type casting
  await db.insert(users).values({
    id: user.id,
    email: user.email,
    passwordHash: user.passwordHash,
    firstName: user.firstName,
    lastName: user.lastName,
    role: role,
    emailVerified: user.emailVerified,
    phoneNumber: user.phoneNumber,
    phoneVerified: user.phoneVerified,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  });

  // Generate user settings
  const settings = {
    userId: user.id,
    defaultCurrency: 'USD',
    theme: 'light',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    notificationPreferences: {
      budgetAlerts: true,
      lowBalanceAlerts: true,
      goalAchievedAlerts: true,
      weeklyReports: true,
      marketingEmails: false,
    },
    privacySettings: {
      hideBalances: false,
      requireAuthForSensitiveOperations: true,
    },
    appSettings: {
      defaultView: 'dashboard',
      defaultAccountView: 'all',
      defaultTransactionPeriod: 'month',
    },
  };

  // Insert the user settings into the database
  await db.insert(userSettings).values(settings);

  return user;
};

/**
 * Generate a JWT token for a user
 */
export const generateTestToken = (
  userId: string,
  email: string,
  role = 'user'
) => {
  const payload: JwtPayload = {
    userId,
    email,
    role,
  };

  return generateToken(payload);
};

/**
 * Create a test user and generate a token
 */
export const createTestUserWithToken = async (
  overrides: Partial<User> = {}
) => {
  const user = await createTestUser(overrides);
  const token = generateTestToken(user.id, user.email, user.role);

  return { user, token };
};

/**
 * Create a test admin user and generate a token
 */
export const createTestAdminWithToken = async (
  overrides: Partial<User> = {}
) => {
  const user = await createTestUser({ ...overrides, role: 'admin' });
  const token = generateTestToken(user.id, user.email, user.role);

  return { user, token };
};

/**
 * Create a mock JWT payload for testing
 */
export const createMockJwtPayload = (
  overrides: Partial<JwtPayload> = {}
): JwtPayload => {
  return {
    userId: overrides.userId || '00000000-0000-0000-0000-000000000001',
    email: overrides.email || '<EMAIL>',
    role: overrides.role || 'user',
    iat: overrides.iat || Math.floor(Date.now() / 1000),
    exp: overrides.exp || Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
  };
};
