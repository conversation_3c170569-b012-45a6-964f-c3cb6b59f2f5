import fastifyCookie from '@fastify/cookie';
import fastifyCors from '@fastify/cors';
import fastifyJwt from '@fastify/jwt';
// Import dependencies
import { makeExecutableSchema } from '@graphql-tools/schema';
import { AltairFastify } from 'altair-fastify-plugin';
import Fastify, {
  type FastifyPluginAsync,
  type FastifyRequest,
  type FastifyReply,
} from 'fastify';
import mercurius from 'mercurius';
import { authConfig } from './config/auth.js';
import { resolvers } from './graphql/resolvers.js';
import { authTypeDefs } from './graphql/schema/auth.js';
import { accountTypeDefs } from './graphql/schema/accounts.js';
import { categoryTypeDefs } from './graphql/schema/categories.js';
import { transactionTypeDefs } from './graphql/schema/transactions.js';

import { logger } from './utils/logger.js';

import type { MercuriusApolloTracingOptions } from 'mercurius-apollo-tracing';
// Conditionally import optional plugins
import type { MercuriusCacheOptions } from 'mercurius-cache';

let mercuriusCache: FastifyPluginAsync<MercuriusCacheOptions> | undefined;
let mercuriusApolloTracing:
  | FastifyPluginAsync<MercuriusApolloTracingOptions>
  | undefined;

// Promise to track plugin loading
let pluginLoadingPromise: Promise<void> = Promise.resolve();

// Only import these plugins if we're in production or if APOLLO_KEY is set
if (process.env.NODE_ENV === 'production' || process.env.APOLLO_KEY) {
  // Dynamic imports for optional plugins
  const importCache = import('mercurius-cache')
    .then(module => {
      // Check if the default export has a nested default (common CJS interop)
      const plugin =
        (module.default as { default?: unknown }).default || module.default;
      mercuriusCache = plugin as FastifyPluginAsync<MercuriusCacheOptions>;
      logger.info('Mercurius Cache plugin loaded');
    })
    .catch(err => {
      logger.warn('Failed to load Mercurius Cache plugin:', err.message);
    });

  const importTracing = import('mercurius-apollo-tracing')
    .then(module => {
      // Check if the default export has a nested default (common CJS interop)
      const plugin =
        (module.default as { default?: unknown }).default || module.default;
      mercuriusApolloTracing =
        plugin as FastifyPluginAsync<MercuriusApolloTracingOptions>;
      logger.info('Mercurius Apollo Tracing plugin loaded');
    })
    .catch(err => {
      logger.warn(
        'Failed to load Mercurius Apollo Tracing plugin:',
        err.message
      );
    });

  // Wait for both imports to complete
  pluginLoadingPromise = Promise.all([importCache, importTracing])
    .then(() => {
      logger.info('All optional plugins loaded successfully');
    })
    .catch(err => {
      logger.error('Error loading optional plugins:', err);
    });
}

import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
// OpenTelemetry imports
import { NodeSDK } from '@opentelemetry/sdk-node';

// Initialize OpenTelemetry
export const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url:
      process.env.OTEL_EXPORTER_OTLP_ENDPOINT ||
      'http://localhost:4318/v1/traces',
  }),
  instrumentations: [
    getNodeAutoInstrumentations(), // This includes Fastify and GraphQL instrumentation
  ],
});

// Start OpenTelemetry - only in production to avoid overhead during development
if (process.env.NODE_ENV === 'production') {
  sdk.start();
}

export async function createServer() {
  // Wait for optional plugins to load before creating the server
  await pluginLoadingPromise;
  
  // Create fastify instance with appropriate logging level for environment
  const isTest = process.env.NODE_ENV === 'test';
  const isVerbose =
    process.env.VITEST_VERBOSE === 'true' || process.env.DEBUG === 'true';

  const app = Fastify({
    logger: !isTest || isVerbose,
  });

  // Register plugins
  await app.register(fastifyCors, {
    origin: true, // Reflect the request origin
  });

  // Add request validation middleware for malformed GraphQL requests
  app.addHook('preHandler', async (request, reply) => {
    // Only validate GraphQL POST requests
    if (request.url.startsWith('/graphql') && request.method === 'POST') {
      const contentType = request.headers['content-type'];
      const contentLength = request.headers['content-length'];

      // Check for missing body
      if (!contentLength || contentLength === '0') {
        reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'GraphQL POST requests must include a request body',
        });
        return;
      }

      // Check for invalid content type
      if (
        contentType &&
        !contentType.includes('application/json') &&
        !contentType.includes('application/graphql')
      ) {
        reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message:
            'Content-Type must be application/json for GraphQL POST requests',
        });
        return;
      }
    }

    // Allow all other requests to pass through
  });

  // Register JWT plugin
  await app.register(fastifyJwt, {
    secret: authConfig.JWT_SECRET,
  });

  // Register cookie plugin
  await app.register(fastifyCookie, {
    secret: authConfig.JWT_SECRET,
    hook: 'onRequest',
    parseOptions: {
      // These options are for parsing incoming cookies, not setting outgoing ones
      // Keep minimal configuration for parsing
    },
  });

  // Setup authentication context function
  const buildContext = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // For GraphQL Playground and direct API calls
      if (!request || !request.headers) {
        return { userId: null, request, reply };
      }

      // Extract token from Authorization header
      const authHeader = request.headers.authorization || '';
      const token = authHeader.startsWith('Bearer ')
        ? authHeader.substring(7)
        : null;

      if (!token) {
        return { userId: null, request, reply };
      }

      try {
        // Verify the token
        const decoded = app.jwt.verify(token);
        // Extract userId from the decoded token
        const userId =
          typeof decoded === 'object' && decoded !== null && 'userId' in decoded
            ? (decoded as { userId: string }).userId
            : null;
        return { userId, request, reply };
      } catch (error) {
        logger.error('Error verifying token:', error);
        return { userId: null, request, reply };
      }
    } catch (error) {
      logger.error('Error in context function:', error);
      return { userId: null, request, reply };
    }
  };

  // Register Mercurius (GraphQL)
  // Define base schema with Query and Mutation types
  const baseTypeDefs = `
    type Query {
      hello: String!
    }

    type Mutation {
      _empty: String
    }
  `;

  // Create executable schema with auth, account, category, and transaction types and resolvers
  const schema = makeExecutableSchema({
    typeDefs: [baseTypeDefs, authTypeDefs, accountTypeDefs, categoryTypeDefs, transactionTypeDefs],
    resolvers,
  });

  await app.register(mercurius, {
    schema,
    context: buildContext,
    graphiql: true, // Enable GraphiQL interface
    ide: 'graphiql', // Explicitly set to use GraphiQL
    path: '/graphql',
    // errorFormatter, // Disable error formatter for now
    // Add validation for malformed requests
    validationRules: [],
    // Enable query validation
    queryDepth: 10,
  });

  // Register optional plugins if they were loaded
  // Use a safer approach to avoid race conditions
  if (mercuriusCache && process.env.NODE_ENV === 'production') {
    try {
      await app.register(mercuriusCache, {
        ttl: 60, // Cache time to live in seconds
        // Add cache policy for specific queries if needed
        policy: {
          Query: {
            hello: { ttl: 60 }, // Cache the 'hello' query for 60 seconds
          }
        }
      });
      logger.info('Mercurius Cache plugin registered');
    } catch (error) {
      logger.error('Failed to register Mercurius Cache plugin:', error);
    }
  }

  if (mercuriusApolloTracing && process.env.APOLLO_KEY) {
    try {
      await app.register(mercuriusApolloTracing, {
        apiKey: process.env.APOLLO_KEY,
        graphRef: process.env.APOLLO_GRAPH_REF || 'budapp@current',
      });
      logger.info('Mercurius Apollo Tracing plugin registered');
    } catch (error) {
      logger.error('Failed to register Apollo Tracing plugin:', error);
    }
  }

  logger.info('GraphQL server is running at /graphql');

  // Health check route
  app.get('/health', async () => {
    return { status: 'ok' };
  });

  // Register Altair GraphQL Client (development and test environments)
  if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
    await app.register(AltairFastify, {
      path: '/altair',
      baseURL: '/altair/',
      endpointURL: '/graphql',
      initialSettings: {
        theme: 'dark',
      },
    });
    logger.info('Altair GraphQL Client is available at /altair');
  }

  // Register authentication routes
  const { authRoutes } = await import('./routes/auth.js');
  await app.register(authRoutes);

  return app;
}
