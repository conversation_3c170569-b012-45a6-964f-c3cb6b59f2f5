import { http } from 'msw';
// Example test file demonstrating how to use the mocking utilities
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  mockJwtUtils,
  mockPasswordUtils,
} from '../../src/test-utils/mocks/auth.js';
import {
  createEmailServiceMocks,
  createGraphQLMocks,
  createMockServer,
  createOAuthMocks,
  graphqlError,
  graphqlResponse,
  jsonResponse,
  mockDatabase,
  mockTableOperation,
} from '../../src/test-utils/mocks/index.js';

// Example service that we'll test
class ExampleService {
  async fetchUsers() {
    const response = await fetch('https://api.example.com/users');
    if (!response.ok) {
      throw new Error('Failed to fetch users');
    }
    return response.json();
  }

  async getUser(id: string) {
    const response = await fetch('http://localhost/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: 'query GetUser($id: ID!) { user(id: $id) { id name email } }',
        variables: { id },
      }),
    });

    const result = await response.json();

    if (result.errors) {
      throw new Error(result.errors[0].message);
    }

    return result.data.user;
  }

  async sendEmail(to: string, subject: string, body: string) {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        from: '<EMAIL>',
        to,
        subject,
        text: body,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send email');
    }

    return response.json();
  }
}

describe('Mocking Examples', () => {
  // Example 1: Mocking HTTP requests
  describe('HTTP Mocking', () => {
    // Set up the mock server with HTTP handlers
    const httpHandlers = [
      http.get('https://api.example.com/users', () => {
        return jsonResponse([
          { id: '1', name: 'User 1' },
          { id: '2', name: 'User 2' },
        ]);
      }),
    ];

    createMockServer(httpHandlers);

    it('should fetch users', async () => {
      const service = new ExampleService();
      const users = await service.fetchUsers();

      expect(users).toEqual([
        { id: '1', name: 'User 1' },
        { id: '2', name: 'User 2' },
      ]);
    });
  });

  // Example 2: Mocking GraphQL operations
  describe('GraphQL Mocking', () => {
    // Set up the mock server with GraphQL handlers
    const graphqlHandlers = createGraphQLMocks('http://localhost/graphql', {
      GetUser: variables => {
        if (variables.id === '123') {
          return graphqlResponse({
            user: { id: '123', name: 'Test User', email: '<EMAIL>' },
          });
        }

        return graphqlResponse(null, [
          graphqlError('User not found', 'NOT_FOUND', ['user']),
        ]);
      },
    });

    createMockServer(graphqlHandlers);

    it('should get a user by ID', async () => {
      const service = new ExampleService();
      const user = await service.getUser('123');

      expect(user).toEqual({
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
      });
    });

    it('should handle errors when user is not found', async () => {
      const service = new ExampleService();

      await expect(service.getUser('456')).rejects.toThrow('User not found');
    });
  });

  // Example 3: Mocking external services
  describe('External Services Mocking', () => {
    // Set up the mock server with email service mocks
    createMockServer(createEmailServiceMocks());

    it('should send an email', async () => {
      const service = new ExampleService();

      const result = await service.sendEmail(
        '<EMAIL>',
        'Test Email',
        'This is a test email'
      );

      expect(result).toEqual({
        id: 'mock-email-id',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Test Email',
        status: 'sent',
      });
    });
  });

  // Example 4: Combining different types of mocks
  describe('Combined Mocking', () => {
    // Mock JWT and password utilities
    beforeEach(() => {
      mockJwtUtils();
      mockPasswordUtils();
    });

    // Set up the mock server with multiple handlers
    const combinedHandlers = [
      ...createOAuthMocks(),
      ...createEmailServiceMocks(),

      // Additional custom handlers
      http.post('/api/register', async ({ request }) => {
        const body = await request.json();

        return jsonResponse({
          id: 'new-user-id',
          email: body.email,
          name: `${body.firstName} ${body.lastName}`,
        });
      }),
    ];

    createMockServer(combinedHandlers);

    it('should demonstrate combined mocking', async () => {
      // This test is just a placeholder to demonstrate how to combine different types of mocks
      expect(true).toBe(true);
    });
  });

  // Example 5: Mocking database operations
  describe('Database Mocking', () => {
    let cleanup: (() => void) | undefined;

    beforeEach(() => {
      // Mock the database for this test suite
      cleanup = mockDatabase();
    });

    afterEach(() => {
      // Restore the original implementation
      cleanup();
    });

    it('should demonstrate database mocking', async () => {
      // Mock a specific table operation
      const mockUsers = [
        { id: '1', email: '<EMAIL>', name: 'User 1' },
        { id: '2', email: '<EMAIL>', name: 'User 2' },
      ];

      mockDatabase(mockTableOperation('users', 'select', mockUsers));

      // This test is just a placeholder to demonstrate how to mock database operations
      expect(true).toBe(true);
    });
  });
});
