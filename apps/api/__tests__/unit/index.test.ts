import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies
vi.mock('../../src/config/env.js');
vi.mock('../../src/server.js');
vi.mock('../../src/utils/logger.js');

const mockServer = {
  listen: vi.fn(),
  close: vi.fn(),
};

const mockSdk = {
  shutdown: vi.fn(),
};

const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
};

const mockCreateServer = vi.fn();

vi.doMock('../../src/server.js', () => ({
  createServer: mockCreateServer,
  sdk: mockSdk,
}));

vi.doMock('../../src/utils/logger.js', () => ({
  logger: mockLogger,
}));

describe('Application Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
    vi.restoreAllMocks();
  });

  describe('Port Configuration', () => {
    it('should use default port 3000 when PORT is not set', () => {
      delete process.env.PORT;
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      expect(port).toBe(3000);
    });

    it('should use custom port when PORT is set', () => {
      process.env.PORT = '8080';
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      expect(port).toBe(8080);
    });

    it('should handle invalid PORT gracefully', () => {
      process.env.PORT = 'invalid';
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      expect(port).toBeNaN();
    });

    it('should parse PORT as integer', () => {
      process.env.PORT = '9000';
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      expect(port).toBe(9000);
      expect(typeof port).toBe('number');
    });
  });

  describe('Host Configuration', () => {
    it('should use default host 0.0.0.0 when HOST is not set', () => {
      delete process.env.HOST;
      
      const host = process.env.HOST || '0.0.0.0';
      expect(host).toBe('0.0.0.0');
    });

    it('should use custom host when HOST is set', () => {
      process.env.HOST = 'localhost';
      
      const host = process.env.HOST || '0.0.0.0';
      expect(host).toBe('localhost');
    });

    it('should handle empty HOST gracefully', () => {
      process.env.HOST = '';
      
      const host = process.env.HOST || '0.0.0.0';
      expect(host).toBe('0.0.0.0');
    });
  });

  describe('Environment Variables', () => {
    it('should handle missing environment variables', () => {
      delete process.env.PORT;
      delete process.env.HOST;
      delete process.env.NODE_ENV;
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      const host = process.env.HOST || '0.0.0.0';
      const nodeEnv = process.env.NODE_ENV;
      
      expect(port).toBe(3000);
      expect(host).toBe('0.0.0.0');
      expect(nodeEnv).toBeUndefined();
    });

    it('should handle production environment', () => {
      process.env.NODE_ENV = 'production';
      
      const nodeEnv = process.env.NODE_ENV;
      const isProduction = nodeEnv === 'production';
      
      expect(nodeEnv).toBe('production');
      expect(isProduction).toBe(true);
    });

    it('should handle development environment', () => {
      process.env.NODE_ENV = 'development';
      
      const nodeEnv = process.env.NODE_ENV;
      const isProduction = nodeEnv === 'production';
      
      expect(nodeEnv).toBe('development');
      expect(isProduction).toBe(false);
    });
  });

  describe('Server Configuration Object', () => {
    it('should create correct server configuration with defaults', () => {
      delete process.env.PORT;
      delete process.env.HOST;
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      const host = process.env.HOST || '0.0.0.0';
      
      const config = { port, host };
      
      expect(config).toEqual({
        port: 3000,
        host: '0.0.0.0'
      });
    });

    it('should create correct server configuration with custom values', () => {
      process.env.PORT = '4000';
      process.env.HOST = 'custom-host';
      
      const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 3000;
      const host = process.env.HOST || '0.0.0.0';
      
      const config = { port, host };
      
      expect(config).toEqual({
        port: 4000,
        host: 'custom-host'
      });
    });
  });

  describe('Logging Configuration', () => {
    it('should format server startup message correctly', () => {
      const host = '0.0.0.0';
      const port = 3000;
      
      const message = `Server listening on ${host}:${port}`;
      expect(message).toBe('Server listening on 0.0.0.0:3000');
    });

    it('should format server startup message with custom values', () => {
      const host = 'localhost';
      const port = 8080;
      
      const message = `Server listening on ${host}:${port}`;
      expect(message).toBe('Server listening on localhost:8080');
    });
  });

  describe('Shutdown Configuration', () => {
    it('should determine OpenTelemetry shutdown based on environment', () => {
      process.env.NODE_ENV = 'production';
      
      const shouldShutdownOtel = process.env.NODE_ENV === 'production';
      expect(shouldShutdownOtel).toBe(true);
    });

    it('should not shutdown OpenTelemetry in non-production', () => {
      process.env.NODE_ENV = 'development';
      
      const shouldShutdownOtel = process.env.NODE_ENV === 'production';
      expect(shouldShutdownOtel).toBe(false);
    });

    it('should handle missing NODE_ENV for OpenTelemetry shutdown', () => {
      delete process.env.NODE_ENV;
      
      const shouldShutdownOtel = process.env.NODE_ENV === 'production';
      expect(shouldShutdownOtel).toBe(false);
    });
  });
}); 