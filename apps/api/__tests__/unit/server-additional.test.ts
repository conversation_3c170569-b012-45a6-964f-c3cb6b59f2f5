import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { createServer } from '../../src/server.js';

// Mock dependencies
vi.mock('../../src/config/auth.js', () => ({
  authConfig: {
    JWT_SECRET: 'test-secret-key-for-testing-purposes-only',
  },
}));

vi.mock('../../src/utils/logger.js', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

vi.mock('../../src/graphql/resolvers.js', () => ({
  resolvers: {
    Query: {
      hello: () => 'Hello from BudApp API!',
    },
    Mutation: {
      _empty: () => null,
    },
  },
}));

vi.mock('../../src/graphql/schema/auth.js', () => ({
  authTypeDefs: 'type User { id: ID! }',
}));

vi.mock('../../src/graphql/schema/accounts.js', () => ({
  accountTypeDefs: 'type Account { id: ID! }',
}));

vi.mock('../../src/graphql/schema/categories.js', () => ({
  categoryTypeDefs: 'type Category { id: ID! }',
}));

vi.mock('../../src/routes/auth.js', () => ({
  authRoutes: async function (fastify: any) {
    fastify.post('/auth/login', async () => ({ success: true }));
    fastify.post('/auth/register', async () => ({ success: true }));
  },
}));

describe('Server Additional Tests', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
    vi.restoreAllMocks();
  });

  describe('GraphQL Request Validation Middleware', () => {
    it('should reject GraphQL POST requests without body', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
          'content-length': '0',
        },
      });

      expect(response.statusCode).toBe(400);
      const result = JSON.parse(response.body);
      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('Body cannot be empty');

      await server.close();
    });

    it('should handle GraphQL POST requests with invalid content type', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'text/plain',
        },
        payload: 'some data',
      });

      // Fastify/Mercurius may handle this differently - just verify it responds
      expect([200, 400, 415]).toContain(response.statusCode);
      // The response format depends on how Fastify/Mercurius handles the content type

      await server.close();
    });

    it('should allow GraphQL POST requests with valid content type', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          query: '{ hello }',
        }),
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });

    it('should allow GraphQL POST requests with application/graphql content type', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/graphql',
        },
        payload: '{ hello }',
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });

    it('should allow non-GraphQL requests to pass through', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'GET',
        url: '/health',
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual({ status: 'ok' });

      await server.close();
    });

    it('should allow GraphQL GET requests to pass through', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'GET',
        url: '/graphql?query={ hello }',
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });
  });

  describe('Authentication Context', () => {
    it('should return null userId when no authorization header is present', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          query: '{ hello }',
        }),
      });

      expect(response.statusCode).toBe(200);
      // The context should have userId: null, but the query should still work
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });

    it('should return null userId when authorization header is malformed', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
          'authorization': 'InvalidFormat token123',
        },
        payload: JSON.stringify({
          query: '{ hello }',
        }),
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });

    it('should return null userId when JWT token is invalid', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
          'authorization': 'Bearer invalid.jwt.token',
        },
        payload: JSON.stringify({
          query: '{ hello }',
        }),
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });

    it('should extract userId from valid JWT token', async () => {
      const server = await createServer();
      
      // Create a valid JWT token
      const token = server.jwt.sign({ userId: 'test-user-123' });
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
          'authorization': `Bearer ${token}`,
        },
        payload: JSON.stringify({
          query: '{ hello }',
        }),
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');

      await server.close();
    });
  });

  describe('Environment-specific Configuration', () => {
    it('should configure server for test environment', async () => {
      process.env.NODE_ENV = 'test';
      
      const server = await createServer();
      expect(server).toBeDefined();
      
      await server.close();
    });

    it('should configure server for development environment', async () => {
      process.env.NODE_ENV = 'development';
      
      const server = await createServer();
      expect(server).toBeDefined();
      
      // Should register Altair in development
      const response = await server.inject({
        method: 'GET',
        url: '/altair',
      });
      
      expect(response.statusCode).toBe(200);
      
      await server.close();
    });

    it('should configure server for production environment', async () => {
      process.env.NODE_ENV = 'production';
      
      const server = await createServer();
      expect(server).toBeDefined();
      
      await server.close();
    });

    it('should handle verbose logging in test environment', async () => {
      process.env.NODE_ENV = 'test';
      process.env.VITEST_VERBOSE = 'true';
      
      const server = await createServer();
      expect(server).toBeDefined();
      
      await server.close();
    });

    it('should handle debug mode', async () => {
      process.env.DEBUG = 'true';
      
      const server = await createServer();
      expect(server).toBeDefined();
      
      await server.close();
    });
  });

  describe('Health Check Endpoint', () => {
    it('should return health status', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'GET',
        url: '/health',
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual({ status: 'ok' });

      await server.close();
    });
  });

  describe('Authentication Routes', () => {
    it('should register authentication routes', async () => {
      const server = await createServer();
      
      const loginResponse = await server.inject({
        method: 'POST',
        url: '/auth/login',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
        }),
      });

      expect(loginResponse.statusCode).toBe(200);
      expect(JSON.parse(loginResponse.body)).toEqual({ success: true });

      const registerResponse = await server.inject({
        method: 'POST',
        url: '/auth/register',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Test',
          lastName: 'User',
        }),
      });

      expect(registerResponse.statusCode).toBe(200);
      expect(JSON.parse(registerResponse.body)).toEqual({ success: true });

      await server.close();
    });
  });

  describe('GraphQL Schema and Resolvers', () => {
    it('should execute GraphQL queries successfully', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          query: '{ hello }',
        }),
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.hello).toBe('Hello from BudApp API!');
      expect(result.errors).toBeUndefined();

      await server.close();
    });

    it('should handle GraphQL introspection queries', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          query: `
            query IntrospectionQuery {
              __schema {
                types {
                  name
                }
              }
            }
          `,
        }),
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.data.__schema).toBeDefined();
      expect(result.data.__schema.types).toBeInstanceOf(Array);

      await server.close();
    });

    it('should handle invalid GraphQL queries', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'POST',
        url: '/graphql',
        headers: {
          'content-type': 'application/json',
        },
        payload: JSON.stringify({
          query: '{ invalidField }',
        }),
      });

      // GraphQL validation errors can return either 200 or 400 depending on the error type
      expect([200, 400]).toContain(response.statusCode);
      const result = JSON.parse(response.body);
      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('Cannot query field "invalidField"');

      await server.close();
    });
  });

  describe('CORS Configuration', () => {
    it('should handle CORS preflight requests', async () => {
      const server = await createServer();
      
      const response = await server.inject({
        method: 'OPTIONS',
        url: '/graphql',
        headers: {
          'origin': 'http://localhost:3000',
          'access-control-request-method': 'POST',
          'access-control-request-headers': 'content-type',
        },
      });

      expect(response.statusCode).toBe(204);
      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:3000');

      await server.close();
    });
  });

  describe('Plugin Loading and Error Handling', () => {
    it('should handle plugin loading gracefully', async () => {
      // This tests the plugin loading promise mechanism
      const server = await createServer();
      expect(server).toBeDefined();
      
      await server.close();
    });

    it('should handle Apollo key configuration', async () => {
      process.env.APOLLO_KEY = 'test-apollo-key';
      process.env.APOLLO_GRAPH_REF = 'test-graph@current';
      
      const server = await createServer();
      expect(server).toBeDefined();
      
      await server.close();
      
      delete process.env.APOLLO_KEY;
      delete process.env.APOLLO_GRAPH_REF;
    });
  });
}); 