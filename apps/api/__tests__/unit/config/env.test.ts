import { existsSync } from 'node:fs';
// Unit tests for the environment configuration
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type { MockedFunction } from 'vitest';

// Mock fs
vi.mock('fs', () => ({
  existsSync: vi.fn(),
}));

// Define a spy function that will be used as the mocked dotenv.config
const mockedDotenvConfigSpy = vi.fn();

// Mock dotenv
// src/config/env.js uses `import dotenv from 'dotenv';` then `dotenv.config()`
// So, the mock must provide a default export, which is an object with a 'config' method.
vi.mock('dotenv', () => ({
  default: {
    config: mockedDotenvConfigSpy,
  },
}));

describe('Environment Configuration', () => {
  const originalNodeEnv = process.env.NODE_ENV;
  const originalEnvVars = {
    DATABASE_URL: process.env.DATABASE_URL,
    JWT_SECRET: process.env.JWT_SECRET,
    VITEST_VERBOSE: process.env.VITEST_VERBOSE,
    DEBUG: process.env.DEBUG,
    TEST_SUITE_TYPE: process.env.TEST_SUITE_TYPE,
  };

  beforeEach(() => {
    // Reset all mocks (this includes mockedDotenvConfigSpy)
    vi.resetAllMocks();
    process.env.NODE_ENV = originalNodeEnv;
    // Clear the module cache so src/config/env.js is fresh and uses the new mock state
    vi.resetModules();

    // Mock console methods to capture output
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore original environment variables
    for (const [key, value] of Object.entries(originalEnvVars)) {
      if (value !== undefined) {
        process.env[key] = value;
      } else {
        delete process.env[key];
      }
    }

    // Restore console methods
    vi.restoreAllMocks();
  });

  describe('Environment file loading', () => {
    it('should load environment variables from .env.test in test environment', async () => {
      process.env.NODE_ENV = 'test';
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env.test')
      );

      // Import the module under test. It will use the mocked dotenv.default.config (mockedDotenvConfigSpy).
      await import('../../../src/config/env.js');

      expect(mockedDotenvConfigSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('.env.test'),
        })
      );
    });

    it('should load environment variables from .env in development environment', async () => {
      process.env.NODE_ENV = 'development';
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env') && !path.includes('.env.test')
      );

      await import('../../../src/config/env.js');

      expect(mockedDotenvConfigSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('.env'),
        })
      );
      // Ensure it was called with .env, and not .env.test in this scenario
      expect(mockedDotenvConfigSpy).not.toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('.env.test'),
        })
      );
    });

    it('should default to development environment when NODE_ENV is not set', async () => {
      process.env.NODE_ENV = undefined;
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env') && !path.includes('.env.test')
      );

      await import('../../../src/config/env.js');

      expect(mockedDotenvConfigSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('.env'),
        })
      );
    });

    it('should try multiple paths and load the first existing file', async () => {
      process.env.NODE_ENV = 'development';
      // Mock first path doesn't exist, second path exists
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => {
          return path.includes(process.cwd()) && path.includes('.env');
        }
      );

      await import('../../../src/config/env.js');

      expect(mockedDotenvConfigSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('.env'),
        })
      );
    });

    it('should not call dotenv.config if no .env file exists', async () => {
      process.env.NODE_ENV = 'development';
      (existsSync as MockedFunction<typeof existsSync>).mockReturnValue(false);

      await import('../../../src/config/env.js');

      expect(mockedDotenvConfigSpy).not.toHaveBeenCalled();
    });
  });

  describe('Verbose logging', () => {
    it('should log environment loading in verbose mode', async () => {
      process.env.NODE_ENV = 'development';
      process.env.VITEST_VERBOSE = 'true';
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env')
      );

      await import('../../../src/config/env.js');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Loading environment variables from')
      );
    });

    it('should log environment loading in debug mode', async () => {
      process.env.NODE_ENV = 'production';
      process.env.DEBUG = 'true';
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env')
      );

      await import('../../../src/config/env.js');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Loading environment variables from')
      );
    });

    it('should log environment loading in development environment', async () => {
      process.env.NODE_ENV = 'development';
      process.env.VITEST_VERBOSE = undefined;
      process.env.DEBUG = undefined;
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env')
      );

      await import('../../../src/config/env.js');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Loading environment variables from')
      );
    });

    it('should not log environment loading in production without verbose/debug flags', async () => {
      process.env.NODE_ENV = 'production';
      process.env.VITEST_VERBOSE = undefined;
      process.env.DEBUG = undefined;
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env')
      );

      await import('../../../src/config/env.js');

      expect(console.log).not.toHaveBeenCalledWith(
        expect.stringContaining('Loading environment variables from')
      );
    });
  });

  describe('Unit test context detection', () => {
    it('should preserve environment variables in unit test context with TEST_SUITE_TYPE', async () => {
      process.env.NODE_ENV = 'test';
      process.env.TEST_SUITE_TYPE = 'unit';
      process.env.DATABASE_URL = 'fake-database-url';
      process.env.JWT_SECRET = 'fake-jwt-secret';
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env.test')
      );

      await import('../../../src/config/env.js');

      expect(process.env.DATABASE_URL).toBe('fake-database-url');
      expect(process.env.JWT_SECRET).toBe('fake-jwt-secret');
    });

    it('should preserve environment variables when call stack includes unit test path', async () => {
      process.env.NODE_ENV = 'test';
      process.env.TEST_SUITE_TYPE = undefined;
      process.env.DATABASE_URL = 'fake-database-url';
      process.env.JWT_SECRET = 'fake-jwt-secret';
      (existsSync as MockedFunction<typeof existsSync>).mockImplementation(
        (path: string) => path.includes('.env.test')
      );

      // Mock Error.stack to simulate unit test context
      const originalError = Error;
      global.Error = class extends originalError {
        constructor(message?: string) {
          super(message);
          this.stack =
            'Error\n    at Object.<anonymous> (/path/to/__tests__/unit/some-test.js:10:5)';
        }
      } as typeof Error;

      await import('../../../src/config/env.js');

      // Restore original Error
      global.Error = originalError;

      expect(process.env.DATABASE_URL).toBe('fake-database-url');
      expect(process.env.JWT_SECRET).toBe('fake-jwt-secret');
    });
  });

  describe('Environment variable validation', () => {
    it('should warn about missing required environment variables', async () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = undefined;
      process.env.JWT_SECRET = undefined;
      (existsSync as MockedFunction<typeof existsSync>).mockReturnValue(false); // No .env file exists

      await import('../../../src/config/env.js');

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining(
          'Missing required environment variables: DATABASE_URL, JWT_SECRET'
        )
      );
    });

    it('should warn about partially missing required environment variables', async () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'some-database-url';
      process.env.JWT_SECRET = undefined;
      (existsSync as MockedFunction<typeof existsSync>).mockReturnValue(false); // No .env file exists

      await import('../../../src/config/env.js');

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining(
          'Missing required environment variables: JWT_SECRET'
        )
      );
    });

    it('should not warn when all required environment variables are present', async () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'some-database-url';
      process.env.JWT_SECRET = 'some-jwt-secret';
      (existsSync as MockedFunction<typeof existsSync>).mockReturnValue(false); // No .env file exists

      await import('../../../src/config/env.js');

      expect(console.warn).not.toHaveBeenCalledWith(
        expect.stringContaining('Missing required environment variables')
      );
    });
  });

  describe('Module exports', () => {
    it('should export process.env as default', async () => {
      process.env.NODE_ENV = 'development';
      (existsSync as MockedFunction<typeof existsSync>).mockReturnValue(false);

      const envModule = await import('../../../src/config/env.js');

      expect(envModule.default).toBe(process.env);
    });
  });
});
