import { describe, it, expect, vi, beforeEach, afterEach, type MockedFunction } from 'vitest';
import { logger } from '../../src/utils/logger.js';

// Mock dependencies
vi.mock('../../src/config/env.js', () => ({}));
vi.mock('../../src/server.js', () => ({
  createServer: vi.fn(),
  sdk: {
    shutdown: vi.fn(),
  },
}));
vi.mock('../../src/utils/logger.js', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

interface MockServer {
  listen: MockedFunction<any>;
  close: MockedFunction<any>;
}

describe('Main Entry Point (index.ts)', () => {
  let mockServer: MockServer;
  let originalEnv: NodeJS.ProcessEnv;
  let originalExit: typeof process.exit;
  let originalOn: typeof process.on;
  let exitSpy: MockedFunction<any>;
  let processOnSpy: MockedFunction<any>;

  beforeEach(() => {
    // Store original values
    originalEnv = { ...process.env };
    originalExit = process.exit;
    originalOn = process.on;

    // Mock process.exit
    exitSpy = vi.fn();
    process.exit = exitSpy as any;

    // Mock process.on
    processOnSpy = vi.fn();
    process.on = processOnSpy as any;

    // Create mock server
    mockServer = {
      listen: vi.fn(),
      close: vi.fn(),
    };

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Restore original values
    process.env = originalEnv;
    process.exit = originalExit;
    process.on = originalOn;
    vi.resetModules();
  });

  describe('Server startup', () => {
    it('should start server with default port and host', async () => {
      // Arrange
      delete process.env.PORT;
      delete process.env.HOST;
      
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(createServer).toHaveBeenCalled();
      expect(mockServer.listen).toHaveBeenCalledWith({
        port: 3000,
        host: '0.0.0.0',
      });
      expect(logger.info).toHaveBeenCalledWith('Server listening on 0.0.0.0:3000');
    });

    it('should start server with custom port and host from environment', async () => {
      // Arrange
      process.env.PORT = '4000';
      process.env.HOST = '127.0.0.1';
      
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(mockServer.listen).toHaveBeenCalledWith({
        port: 4000,
        host: '127.0.0.1',
      });
      expect(logger.info).toHaveBeenCalledWith('Server listening on 127.0.0.1:4000');
    });

    it('should handle invalid port number gracefully', async () => {
      // Arrange
      process.env.PORT = 'invalid';
      
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert - parseInt returns NaN for invalid strings
      expect(mockServer.listen).toHaveBeenCalledWith({
        port: NaN,
        host: '0.0.0.0',
      });
    });
  });

  describe('Error handling', () => {
    it('should handle server startup errors', async () => {
      // Arrange
      const error = new Error('Server startup failed');
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockRejectedValue(error);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(logger.error).toHaveBeenCalledWith(error);
      expect(exitSpy).toHaveBeenCalledWith(1);
    });

    it('should handle non-Error objects in catch block', async () => {
      // Arrange
      const errorString = 'String error';
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockRejectedValue(errorString);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(logger.error).toHaveBeenCalledWith(errorString);
      expect(exitSpy).toHaveBeenCalledWith(1);
    });


  });

  describe('Signal handlers', () => {
    it('should register SIGTERM and SIGINT handlers', async () => {
      // Arrange
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(processOnSpy).toHaveBeenCalledWith('SIGTERM', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('SIGINT', expect.any(Function));
    });

    it('should handle SIGTERM gracefully in development', async () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const { createServer, sdk } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);
      mockServer.close.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Get the SIGTERM handler
      const sigtermCall = processOnSpy.mock.calls.find((call: any[]) => call[0] === 'SIGTERM');
      expect(sigtermCall).toBeDefined();
      
      const shutdownHandler = sigtermCall![1] as () => Promise<void>;
      await shutdownHandler();

      // Assert
      expect(logger.info).toHaveBeenCalledWith('Shutting down server...');
      expect(mockServer.close).toHaveBeenCalled();
      expect(sdk.shutdown).not.toHaveBeenCalled(); // Should not shutdown in development
      expect(exitSpy).toHaveBeenCalledWith(0);
    });

    it('should handle SIGTERM gracefully in production', async () => {
      // Arrange
      process.env.NODE_ENV = 'production';
      const { createServer, sdk } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);
      mockServer.close.mockResolvedValue(undefined);
      (sdk.shutdown as MockedFunction<any>).mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Get the SIGTERM handler
      const sigtermCall = processOnSpy.mock.calls.find((call: any[]) => call[0] === 'SIGTERM');
      expect(sigtermCall).toBeDefined();
      
      const shutdownHandler = sigtermCall![1] as () => Promise<void>;
      await shutdownHandler();

      // Assert
      expect(logger.info).toHaveBeenCalledWith('Shutting down server...');
      expect(mockServer.close).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Shutting down OpenTelemetry...');
      expect(sdk.shutdown).toHaveBeenCalled();
      expect(exitSpy).toHaveBeenCalledWith(0);
    });

    it('should handle SIGINT gracefully', async () => {
      // Arrange
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);
      mockServer.close.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Get the SIGINT handler
      const sigintCall = processOnSpy.mock.calls.find((call: any[]) => call[0] === 'SIGINT');
      expect(sigintCall).toBeDefined();
      
      const shutdownHandler = sigintCall![1] as () => Promise<void>;
      await shutdownHandler();

      // Assert
      expect(logger.info).toHaveBeenCalledWith('Shutting down server...');
      expect(mockServer.close).toHaveBeenCalled();
      expect(exitSpy).toHaveBeenCalledWith(0);
    });
  });

  describe('Environment variable parsing', () => {
    it('should handle zero port', async () => {
      // Arrange
      process.env.PORT = '0';
      
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(mockServer.listen).toHaveBeenCalledWith({
        port: 0,
        host: '0.0.0.0',
      });
    });

    it('should handle negative port', async () => {
      // Arrange
      process.env.PORT = '-1';
      
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(mockServer.listen).toHaveBeenCalledWith({
        port: -1,
        host: '0.0.0.0',
      });
    });

    it('should handle empty HOST environment variable', async () => {
      // Arrange
      process.env.HOST = '';
      
      const { createServer } = await import('../../src/server.js');
      (createServer as MockedFunction<any>).mockResolvedValue(mockServer);
      mockServer.listen.mockResolvedValue(undefined);

      // Act
      await import('../../src/index.js');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 10));

      // Assert
      expect(mockServer.listen).toHaveBeenCalledWith({
        port: 3000,
        host: '0.0.0.0', // Should use default when empty
      });
    });
  });
}); 