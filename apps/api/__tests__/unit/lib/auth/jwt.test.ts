import jsonwebtoken from 'jsonwebtoken';
// Unit tests for JWT utilities
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  type JwtPayload,
  extractTokenFromHeader,
  generateRefreshToken,
  generateToken,
  verifyToken,
} from '../../../../src/lib/auth/jwt.js';

// Mock the auth config
vi.mock('../../../../src/config/auth.js', () => ({
  authConfig: {
    JWT_SECRET: 'test-jwt-secret-that-is-at-least-32-characters-long',
    JWT_EXPIRES_IN: '1h',
  },
}));

describe('JWT Utilities', () => {
  const mockPayload: JwtPayload = {
    userId: '00000000-0000-0000-0000-000000000001',
    email: '<EMAIL>',
    role: 'user',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Date.now for consistent timestamps
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-01-01T00:00:00Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('generateToken', () => {
    it('should generate a valid JWT token', () => {
      const token = generateToken(mockPayload);

      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts separated by dots
    });

    it('should include the correct payload in the token', () => {
      const token = generateToken(mockPayload);
      const decoded = jsonwebtoken.verify(
        token,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      ) as JwtPayload;

      expect(decoded.userId).toBe(mockPayload.userId);
      expect(decoded.email).toBe(mockPayload.email);
      expect(decoded.role).toBe(mockPayload.role);
    });

    it('should set the correct expiration time', () => {
      const token = generateToken(mockPayload);
      const decoded = jsonwebtoken.verify(
        token,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      ) as JwtPayload;

      expect(decoded.exp).toBeDefined();
      expect(decoded.iat).toBeDefined();
      // Should expire in 1 hour (3600 seconds)
      expect(decoded.exp - decoded.iat).toBe(3600);
    });

    it('should generate different tokens for different payloads', () => {
      const payload1 = { ...mockPayload, userId: 'user1' };
      const payload2 = { ...mockPayload, userId: 'user2' };

      const token1 = generateToken(payload1);
      const token2 = generateToken(payload2);

      expect(token1).not.toBe(token2);
    });

    it('should handle minimal payload', () => {
      const minimalPayload: JwtPayload = {
        userId: 'test-user',
        email: '<EMAIL>',
        role: 'user',
      };

      const token = generateToken(minimalPayload);
      const decoded = jsonwebtoken.verify(
        token,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      ) as JwtPayload;

      expect(decoded.userId).toBe(minimalPayload.userId);
      expect(decoded.email).toBe(minimalPayload.email);
      expect(decoded.role).toBe(minimalPayload.role);
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', () => {
      const token = generateToken(mockPayload);
      const result = verifyToken(token);

      expect(result).not.toBeNull();
      expect(result?.userId).toBe(mockPayload.userId);
      expect(result?.email).toBe(mockPayload.email);
      expect(result?.role).toBe(mockPayload.role);
    });

    it('should return null for an invalid token', () => {
      const result = verifyToken('invalid-token');

      expect(result).toBeNull();
    });

    it('should return null for a malformed token', () => {
      const result = verifyToken('not.a.jwt');

      expect(result).toBeNull();
    });

    it('should return null for an empty token', () => {
      const result = verifyToken('');

      expect(result).toBeNull();
    });

    it('should return null for a token with wrong secret', () => {
      const token = jsonwebtoken.sign(mockPayload, 'wrong-secret');
      const result = verifyToken(token);

      expect(result).toBeNull();
    });

    it('should return null for an expired token', () => {
      // Create an expired token
      const expiredToken = jsonwebtoken.sign(
        { ...mockPayload, exp: Math.floor(Date.now() / 1000) - 3600 }, // Expired 1 hour ago
        'test-jwt-secret-that-is-at-least-32-characters-long'
      );

      const result = verifyToken(expiredToken);

      expect(result).toBeNull();
    });

    it('should handle token with additional claims', () => {
      const payloadWithClaims = {
        ...mockPayload,
        customClaim: 'customValue',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      };

      const token = jsonwebtoken.sign(
        payloadWithClaims,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      );
      const result = verifyToken(token);

      expect(result).not.toBeNull();
      expect(result?.userId).toBe(mockPayload.userId);
      expect(
        (result as JwtPayload & { customClaim?: string })?.customClaim
      ).toBe('customValue');
    });
  });

  describe('extractTokenFromHeader', () => {
    it('should extract token from valid Bearer header', () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const authHeader = `Bearer ${token}`;

      const result = extractTokenFromHeader(authHeader);

      expect(result).toBe(token);
    });

    it('should return null for undefined header', () => {
      const result = extractTokenFromHeader(undefined);

      expect(result).toBeNull();
    });

    it('should return null for empty header', () => {
      const result = extractTokenFromHeader('');

      expect(result).toBeNull();
    });

    it('should return null for header without Bearer prefix', () => {
      const result = extractTokenFromHeader(
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token'
      );

      expect(result).toBeNull();
    });

    it('should return null for header with wrong prefix', () => {
      const result = extractTokenFromHeader(
        'Basic eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token'
      );

      expect(result).toBeNull();
    });

    it('should return null for Bearer header without token', () => {
      const result = extractTokenFromHeader('Bearer');

      expect(result).toBeNull();
    });

    it('should return null for Bearer header with empty token', () => {
      const result = extractTokenFromHeader('Bearer ');

      expect(result).toBeNull();
    });

    it('should return null for Bearer header with extra spaces', () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const authHeader = `Bearer  ${token}`; // Two spaces

      const result = extractTokenFromHeader(authHeader);

      // The current implementation splits on single space, so extra spaces cause issues
      expect(result).toBeNull();
    });

    it('should handle case-sensitive Bearer prefix', () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const authHeader = `bearer ${token}`;

      const result = extractTokenFromHeader(authHeader);

      expect(result).toBeNull(); // Should be case-sensitive
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate a valid refresh token', () => {
      const userId = '00000000-0000-0000-0000-000000000001';
      const refreshToken = generateRefreshToken(userId);

      expect(refreshToken).toBeDefined();
      expect(typeof refreshToken).toBe('string');
      expect(refreshToken.split('.')).toHaveLength(3); // JWT has 3 parts
    });

    it('should include the correct userId in the refresh token', () => {
      const userId = '00000000-0000-0000-0000-000000000001';
      const refreshToken = generateRefreshToken(userId);
      const decoded = jsonwebtoken.verify(
        refreshToken,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      ) as { userId: string; iat?: number; exp?: number };

      expect(decoded.userId).toBe(userId);
    });

    it('should set longer expiration for refresh token', () => {
      const userId = '00000000-0000-0000-0000-000000000001';
      const refreshToken = generateRefreshToken(userId);
      const decoded = jsonwebtoken.verify(
        refreshToken,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      ) as { userId: string; iat?: number; exp?: number };

      expect(decoded.exp).toBeDefined();
      expect(decoded.iat).toBeDefined();
      // Should expire in 30 days (30 * 24 * 60 * 60 = 2592000 seconds)
      expect(decoded.exp - decoded.iat).toBe(2592000);
    });

    it('should generate different refresh tokens for different users', () => {
      const userId1 = 'user1';
      const userId2 = 'user2';

      const refreshToken1 = generateRefreshToken(userId1);
      const refreshToken2 = generateRefreshToken(userId2);

      expect(refreshToken1).not.toBe(refreshToken2);
    });

    it('should handle empty userId', () => {
      const refreshToken = generateRefreshToken('');
      const decoded = jsonwebtoken.verify(
        refreshToken,
        'test-jwt-secret-that-is-at-least-32-characters-long'
      ) as { userId: string; iat?: number; exp?: number };

      expect(decoded.userId).toBe('');
    });
  });

  describe('Integration tests', () => {
    it('should work with generated and verified tokens end-to-end', () => {
      // Generate a token
      const token = generateToken(mockPayload);

      // Verify the token
      const verified = verifyToken(token);

      expect(verified).not.toBeNull();
      expect(verified?.userId).toBe(mockPayload.userId);
      expect(verified?.email).toBe(mockPayload.email);
      expect(verified?.role).toBe(mockPayload.role);
    });

    it('should work with extracted and verified tokens end-to-end', () => {
      // Generate a token
      const token = generateToken(mockPayload);
      const authHeader = `Bearer ${token}`;

      // Extract the token
      const extractedToken = extractTokenFromHeader(authHeader);

      // Verify the extracted token
      const verified = verifyToken(extractedToken!);

      expect(verified).not.toBeNull();
      expect(verified?.userId).toBe(mockPayload.userId);
    });

    it('should handle refresh token verification', () => {
      const userId = '00000000-0000-0000-0000-000000000001';

      // Generate refresh token
      const refreshToken = generateRefreshToken(userId);

      // Verify refresh token using verifyToken (should work since it's still a JWT)
      const verified = verifyToken(refreshToken);

      expect(verified).not.toBeNull();
      expect((verified as JwtPayload & { userId: string })?.userId).toBe(
        userId
      );
    });
  });
});
