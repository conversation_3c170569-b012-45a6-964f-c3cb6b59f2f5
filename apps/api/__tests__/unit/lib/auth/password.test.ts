import bcrypt from 'bcrypt';
// Unit tests for password utilities
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  generateRandomPassword,
  hashPassword,
  verifyPassword,
} from '../../../../src/lib/auth/password.js';

describe('Password Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('hashPassword', () => {
    it('should hash a password', async () => {
      const password = 'testPassword123!';
      const hash = await hashPassword(password);

      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(0);
    });

    it('should generate different hashes for the same password', async () => {
      const password = 'testPassword123!';
      const hash1 = await hashPassword(password);
      const hash2 = await hashPassword(password);

      expect(hash1).not.toBe(hash2); // bcrypt uses salt, so hashes should be different
    });

    it('should handle empty password', async () => {
      const hash = await hashPassword('');

      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBeGreaterThan(0);
    });

    it('should handle very long passwords', async () => {
      const longPassword = 'a'.repeat(1000);
      const hash = await hashPassword(longPassword);

      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBeGreaterThan(0);
    });

    it('should handle special characters', async () => {
      const specialPassword = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const hash = await hashPassword(specialPassword);

      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBeGreaterThan(0);
    });

    it('should handle unicode characters', async () => {
      const unicodePassword = 'пароль123🔒';
      const hash = await hashPassword(unicodePassword);

      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBeGreaterThan(0);
    });

    it('should use the correct number of salt rounds', async () => {
      const password = 'testPassword123!';

      // Spy on bcrypt.hash to verify salt rounds
      const hashSpy = vi.spyOn(bcrypt, 'hash');

      await hashPassword(password);

      expect(hashSpy).toHaveBeenCalledWith(password, 10);
    });

    it('should produce bcrypt-compatible hashes', async () => {
      const password = 'testPassword123!';
      const hash = await hashPassword(password);

      // bcrypt hashes start with $2b$ (or $2a$, $2y$)
      expect(hash).toMatch(/^\$2[aby]\$/);
    });
  });

  describe('verifyPassword', () => {
    it('should verify a correct password', async () => {
      const password = 'testPassword123!';
      const hash = await hashPassword(password);

      const isValid = await verifyPassword(password, hash);

      expect(isValid).toBe(true);
    });

    it('should reject an incorrect password', async () => {
      const password = 'testPassword123!';
      const wrongPassword = 'wrongPassword456!';
      const hash = await hashPassword(password);

      const isValid = await verifyPassword(wrongPassword, hash);

      expect(isValid).toBe(false);
    });

    it('should handle empty password verification', async () => {
      const emptyHash = await hashPassword('');

      const isValidEmpty = await verifyPassword('', emptyHash);
      const isValidNonEmpty = await verifyPassword('notEmpty', emptyHash);

      expect(isValidEmpty).toBe(true);
      expect(isValidNonEmpty).toBe(false);
    });

    it('should handle case-sensitive passwords', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);

      const isValidSame = await verifyPassword('TestPassword123!', hash);
      const isValidDifferentCase = await verifyPassword(
        'testpassword123!',
        hash
      );

      expect(isValidSame).toBe(true);
      expect(isValidDifferentCase).toBe(false);
    });

    it('should handle special characters in verification', async () => {
      const specialPassword = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const hash = await hashPassword(specialPassword);

      const isValid = await verifyPassword(specialPassword, hash);

      expect(isValid).toBe(true);
    });

    it('should handle unicode characters in verification', async () => {
      const unicodePassword = 'пароль123🔒';
      const hash = await hashPassword(unicodePassword);

      const isValid = await verifyPassword(unicodePassword, hash);

      expect(isValid).toBe(true);
    });

    it('should reject invalid hash format', async () => {
      const password = 'testPassword123!';
      const invalidHash = 'not-a-valid-hash';

      const isValid = await verifyPassword(password, invalidHash);

      expect(isValid).toBe(false);
    });

    it('should reject empty hash', async () => {
      const password = 'testPassword123!';

      const isValid = await verifyPassword(password, '');

      expect(isValid).toBe(false);
    });

    it('should use bcrypt.compare internally', async () => {
      const password = 'testPassword123!';
      const hash = await hashPassword(password);

      // Spy on bcrypt.compare to verify it's being called
      const compareSpy = vi.spyOn(bcrypt, 'compare');

      await verifyPassword(password, hash);

      expect(compareSpy).toHaveBeenCalledWith(password, hash);
    });

    it('should handle bcrypt errors gracefully', async () => {
      const password = 'testPassword123!';
      const hash = 'malformed-hash';

      // This should not throw, but return false
      const isValid = await verifyPassword(password, hash);

      expect(isValid).toBe(false);
    });
  });

  describe('generateRandomPassword', () => {
    it('should generate a password with default length', () => {
      const password = generateRandomPassword();

      expect(password).toBeDefined();
      expect(typeof password).toBe('string');
      expect(password.length).toBe(12); // Default length
    });

    it('should generate a password with specified length', () => {
      const length = 20;
      const password = generateRandomPassword(length);

      expect(password.length).toBe(length);
    });

    it('should generate different passwords on each call', () => {
      const password1 = generateRandomPassword();
      const password2 = generateRandomPassword();

      expect(password1).not.toBe(password2);
    });

    it('should handle minimum length', () => {
      const password = generateRandomPassword(1);

      expect(password.length).toBe(1);
    });

    it('should handle zero length', () => {
      const password = generateRandomPassword(0);

      expect(password.length).toBe(0);
      expect(password).toBe('');
    });

    it('should handle very long passwords', () => {
      const length = 1000;
      const password = generateRandomPassword(length);

      expect(password.length).toBe(length);
    });

    it('should contain only valid characters', () => {
      const password = generateRandomPassword(100);
      const validCharset =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+~`|}{[]:;?><,./-=';

      for (const char of password) {
        expect(validCharset).toContain(char);
      }
    });

    it('should include various character types', () => {
      // Generate a long password to increase chances of getting all character types
      const password = generateRandomPassword(200);

      // Check for lowercase letters
      expect(password).toMatch(/[a-z]/);

      // Check for uppercase letters
      expect(password).toMatch(/[A-Z]/);

      // Check for numbers
      expect(password).toMatch(/[0-9]/);

      // Check for special characters
      expect(password).toMatch(/[!@#$%^&*()_+~`|}{[\]:;?><,./-=]/);
    });

    it('should use Math.random for randomness', () => {
      // Spy on Math.random to verify it's being used
      const randomSpy = vi.spyOn(Math, 'random').mockReturnValue(0.5);

      generateRandomPassword(5);

      expect(randomSpy).toHaveBeenCalled();

      randomSpy.mockRestore();
    });

    it('should handle negative length gracefully', () => {
      const password = generateRandomPassword(-5);

      // Should handle negative length (implementation dependent)
      expect(typeof password).toBe('string');
    });
  });

  describe('Integration tests', () => {
    it('should work with hash and verify end-to-end', async () => {
      const password = 'integrationTestPassword123!';

      // Hash the password
      const hash = await hashPassword(password);

      // Verify the correct password
      const isValidCorrect = await verifyPassword(password, hash);

      // Verify an incorrect password
      const isValidIncorrect = await verifyPassword('wrongPassword', hash);

      expect(isValidCorrect).toBe(true);
      expect(isValidIncorrect).toBe(false);
    });

    it('should work with generated random passwords', async () => {
      const randomPassword = generateRandomPassword(16);

      // Hash the random password
      const hash = await hashPassword(randomPassword);

      // Verify the random password
      const isValid = await verifyPassword(randomPassword, hash);

      expect(isValid).toBe(true);
    });

    it('should handle multiple password operations', async () => {
      const passwords = [
        'password1',
        'password2',
        'password3',
        generateRandomPassword(),
        generateRandomPassword(20),
      ];

      const hashes = await Promise.all(passwords.map(hashPassword));

      // Verify each password with its corresponding hash
      for (let i = 0; i < passwords.length; i++) {
        const isValid = await verifyPassword(passwords[i], hashes[i]);
        expect(isValid).toBe(true);
      }

      // Verify that passwords don't match other hashes
      for (let i = 0; i < passwords.length; i++) {
        for (let j = 0; j < hashes.length; j++) {
          if (i !== j) {
            const isValid = await verifyPassword(passwords[i], hashes[j]);
            expect(isValid).toBe(false);
          }
        }
      }
    });

    it('should maintain consistency across multiple calls', async () => {
      const password = 'consistencyTest123!';
      const hash = await hashPassword(password);

      // Verify multiple times
      const results = await Promise.all([
        verifyPassword(password, hash),
        verifyPassword(password, hash),
        verifyPassword(password, hash),
      ]);

      expect(results).toEqual([true, true, true]);
    });
  });
});
