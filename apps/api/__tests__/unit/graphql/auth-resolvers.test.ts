// Unit tests for GraphQL auth resolvers
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock all dependencies before importing
vi.mock('../../../src/services/auth/auth.service.js', () => ({
  registerUser: vi.fn(),
  loginUser: vi.fn(),
  getUserById: vi.fn(),
  validateJwtPayload: vi.fn(),
}));

vi.mock('../../../src/lib/auth/jwt.js', () => ({
  verifyToken: vi.fn(),
  extractTokenFromHeader: vi.fn(),
}));

vi.mock('../../../src/services/auth/auth.types.js', () => ({
  registerInputSchema: {
    parse: vi.fn(),
  },
  loginInputSchema: {
    parse: vi.fn(),
  },
}));

import {
  extractTokenFromHeader,
  verifyToken,
} from '../../../src/lib/auth/jwt.js';
// Import mocked modules
import {
  getUserById,
  loginUser,
  registerUser,
  validateJwtPayload,
} from '../../../src/services/auth/auth.service.js';
import {
  loginInputSchema,
  registerInputSchema,
} from '../../../src/services/auth/auth.types.js';

// Import the resolvers under test
import {
  authContext,
  authResolvers,
} from '../../../src/graphql/resolvers/auth.js';

import {
  AuthenticationError,
  DatabaseError,
  EmailAlreadyExistsError,
  InvalidCredentialsError,
  ValidationError,
  WeakPasswordError,
} from '../../../src/utils/error-handler.js';

describe('Auth Resolvers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Query resolvers', () => {
    describe('me', () => {
      it('should return user when userId is provided in context', async () => {
        const mockUser = {
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: 'user',
          emailVerified: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const mockContext = { userId: '00000000-0000-0000-0000-000000000001' };

        vi.mocked(getUserById).mockResolvedValue(mockUser);

        const result = await authResolvers.Query.me(null, {}, mockContext);

        expect(result).toEqual(mockUser);
        expect(getUserById).toHaveBeenCalledWith(
          '00000000-0000-0000-0000-000000000001'
        );
      });

      it('should return null when userId is not provided in context', async () => {
        const mockContext = { userId: null };

        const result = await authResolvers.Query.me(null, {}, mockContext);

        expect(result).toBeNull();
        expect(getUserById).not.toHaveBeenCalled();
      });

      it('should return null when userId is undefined in context', async () => {
        const mockContext = {};

        const result = await authResolvers.Query.me(null, {}, mockContext);

        expect(result).toBeNull();
        expect(getUserById).not.toHaveBeenCalled();
      });

      it('should handle getUserById errors gracefully', async () => {
        const mockContext = { userId: '00000000-0000-0000-0000-000000000001' };

        vi.mocked(getUserById).mockRejectedValue(
          new DatabaseError('Database connection failed')
        );

        await expect(
          authResolvers.Query.me(null, {}, mockContext)
        ).rejects.toThrow(DatabaseError);
        expect(getUserById).toHaveBeenCalledWith(
          '00000000-0000-0000-0000-000000000001'
        );
      });
    });
  });

  describe('Mutation resolvers', () => {
    describe('register', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Test',
        lastName: 'User',
      };

      const mockUser = {
        id: '00000000-0000-0000-0000-000000000001',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'user',
        emailVerified: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockToken = 'mock-jwt-token';

      it('should register user successfully with valid input', async () => {
        vi.mocked(registerInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(registerUser).mockResolvedValue({
          user: mockUser,
          token: mockToken,
        });

        const result = await authResolvers.Mutation.register(null, {
          input: validInput,
        });

        expect(result).toEqual({
          user: mockUser,
          token: mockToken,
        });

        expect(registerInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(registerUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!',
          'Test',
          'User'
        );
      });

      it('should handle validation errors', async () => {
        const invalidInput = {
          email: 'invalid-email',
          password: 'short',
        };

        vi.mocked(registerInputSchema.parse).mockImplementation(() => {
          throw new ValidationError('Invalid input');
        });

        await expect(
          authResolvers.Mutation.register(null, { input: invalidInput })
        ).rejects.toThrow(ValidationError);

        expect(registerInputSchema.parse).toHaveBeenCalledWith(invalidInput);
        expect(registerUser).not.toHaveBeenCalled();
      });

      it('should handle email already exists error', async () => {
        vi.mocked(registerInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(registerUser).mockRejectedValue(
          new EmailAlreadyExistsError('Email already exists')
        );

        await expect(
          authResolvers.Mutation.register(null, { input: validInput })
        ).rejects.toThrow(EmailAlreadyExistsError);

        expect(registerInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(registerUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!',
          'Test',
          'User'
        );
      });

      it('should handle weak password error', async () => {
        vi.mocked(registerInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(registerUser).mockRejectedValue(
          new WeakPasswordError('Password is too weak')
        );

        await expect(
          authResolvers.Mutation.register(null, { input: validInput })
        ).rejects.toThrow(WeakPasswordError);

        expect(registerInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(registerUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!',
          'Test',
          'User'
        );
      });

      it('should handle database errors', async () => {
        vi.mocked(registerInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(registerUser).mockRejectedValue(
          new DatabaseError('Database connection failed')
        );

        await expect(
          authResolvers.Mutation.register(null, { input: validInput })
        ).rejects.toThrow(DatabaseError);

        expect(registerInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(registerUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!',
          'Test',
          'User'
        );
      });

      it('should handle missing optional fields', async () => {
        const inputWithoutOptionalFields = {
          email: '<EMAIL>',
          password: 'Password123!',
        };

        const parsedInput = {
          email: '<EMAIL>',
          password: 'Password123!',
          firstName: undefined,
          lastName: undefined,
        };

        vi.mocked(registerInputSchema.parse).mockReturnValue(parsedInput);
        vi.mocked(registerUser).mockResolvedValue({
          user: { ...mockUser, firstName: null, lastName: null },
          token: mockToken,
        });

        const result = await authResolvers.Mutation.register(null, {
          input: inputWithoutOptionalFields,
        });

        expect(result).toEqual({
          user: { ...mockUser, firstName: null, lastName: null },
          token: mockToken,
        });

        expect(registerInputSchema.parse).toHaveBeenCalledWith(
          inputWithoutOptionalFields
        );
        expect(registerUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!',
          undefined,
          undefined
        );
      });
    });

    describe('login', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const mockUser = {
        id: '00000000-0000-0000-0000-000000000001',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'user',
        emailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockToken = 'mock-jwt-token';

      it('should login user successfully with valid credentials', async () => {
        vi.mocked(loginInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(loginUser).mockResolvedValue({
          user: mockUser,
          token: mockToken,
        });

        const result = await authResolvers.Mutation.login(null, {
          input: validInput,
        });

        expect(result).toEqual({
          user: mockUser,
          token: mockToken,
        });

        expect(loginInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(loginUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!'
        );
      });

      it('should handle validation errors', async () => {
        const invalidInput = {
          email: 'invalid-email',
          password: '',
        };

        vi.mocked(loginInputSchema.parse).mockImplementation(() => {
          throw new ValidationError('Invalid input');
        });

        await expect(
          authResolvers.Mutation.login(null, { input: invalidInput })
        ).rejects.toThrow(ValidationError);

        expect(loginInputSchema.parse).toHaveBeenCalledWith(invalidInput);
        expect(loginUser).not.toHaveBeenCalled();
      });

      it('should handle invalid credentials error', async () => {
        vi.mocked(loginInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(loginUser).mockRejectedValue(
          new InvalidCredentialsError('Invalid credentials')
        );

        await expect(
          authResolvers.Mutation.login(null, { input: validInput })
        ).rejects.toThrow(InvalidCredentialsError);

        expect(loginInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(loginUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!'
        );
      });

      it('should handle database errors', async () => {
        vi.mocked(loginInputSchema.parse).mockReturnValue(validInput);
        vi.mocked(loginUser).mockRejectedValue(
          new DatabaseError('Database connection failed')
        );

        await expect(
          authResolvers.Mutation.login(null, { input: validInput })
        ).rejects.toThrow(DatabaseError);

        expect(loginInputSchema.parse).toHaveBeenCalledWith(validInput);
        expect(loginUser).toHaveBeenCalledWith(
          '<EMAIL>',
          'Password123!'
        );
      });
    });

    describe('refreshToken', () => {
      const mockUser = {
        id: '00000000-0000-0000-0000-000000000001',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'user',
        emailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      it('should refresh token successfully for authenticated user', async () => {
        const mockContext = { userId: '00000000-0000-0000-0000-000000000001' };

        vi.mocked(getUserById).mockResolvedValue(mockUser);

        const result = await authResolvers.Mutation.refreshToken(
          null,
          {},
          mockContext
        );

        expect(result).toEqual({
          user: mockUser,
          token: 'mock-refreshed-token',
        });

        expect(getUserById).toHaveBeenCalledWith(
          '00000000-0000-0000-0000-000000000001'
        );
      });

      it('should throw error when user is not authenticated', async () => {
        const mockContext = { userId: null };

        await expect(
          authResolvers.Mutation.refreshToken(null, {}, mockContext)
        ).rejects.toThrow('Not authenticated');

        expect(getUserById).not.toHaveBeenCalled();
      });

      it('should throw error when userId is undefined', async () => {
        const mockContext = {};

        await expect(
          authResolvers.Mutation.refreshToken(null, {}, mockContext)
        ).rejects.toThrow('Not authenticated');

        expect(getUserById).not.toHaveBeenCalled();
      });

      it('should throw error when user is not found', async () => {
        const mockContext = { userId: '00000000-0000-0000-0000-000000000001' };

        vi.mocked(getUserById).mockResolvedValue(null);

        await expect(
          authResolvers.Mutation.refreshToken(null, {}, mockContext)
        ).rejects.toThrow('User not found');

        expect(getUserById).toHaveBeenCalledWith(
          '00000000-0000-0000-0000-000000000001'
        );
      });

      it('should handle database errors', async () => {
        const mockContext = { userId: '00000000-0000-0000-0000-000000000001' };

        vi.mocked(getUserById).mockRejectedValue(
          new DatabaseError('Database connection failed')
        );

        await expect(
          authResolvers.Mutation.refreshToken(null, {}, mockContext)
        ).rejects.toThrow(DatabaseError);

        expect(getUserById).toHaveBeenCalledWith(
          '00000000-0000-0000-0000-000000000001'
        );
      });
    });

    describe('logout', () => {
      it('should logout successfully', async () => {
        const result = await authResolvers.Mutation.logout();

        expect(result).toBe(true);
      });

      it('should always return true regardless of context', async () => {
        const mockContext = { userId: '00000000-0000-0000-0000-000000000001' };

        const result = await authResolvers.Mutation.logout(
          null,
          {},
          mockContext
        );

        expect(result).toBe(true);
      });
    });
  });

  describe('authContext', () => {
    const mockPayload = {
      userId: '00000000-0000-0000-0000-000000000001',
      email: '<EMAIL>',
      role: 'user',
    };

    it('should return userId when valid token is provided', async () => {
      const mockRequest = {
        req: {
          headers: {
            authorization: 'Bearer valid-token',
          },
        },
      };

      vi.mocked(extractTokenFromHeader).mockReturnValue('valid-token');
      vi.mocked(verifyToken).mockReturnValue(mockPayload);
      vi.mocked(validateJwtPayload).mockResolvedValue(true);

      const result = await authContext(mockRequest);

      expect(result).toEqual({
        userId: '00000000-0000-0000-0000-000000000001',
      });
      expect(extractTokenFromHeader).toHaveBeenCalledWith('Bearer valid-token');
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
      expect(validateJwtPayload).toHaveBeenCalledWith(mockPayload);
    });

    it('should return null userId when request is missing', async () => {
      const result = await authContext(null);

      expect(result).toEqual({ userId: null });
      expect(extractTokenFromHeader).not.toHaveBeenCalled();
      expect(verifyToken).not.toHaveBeenCalled();
      expect(validateJwtPayload).not.toHaveBeenCalled();
    });

    it('should return null userId when request.req is missing', async () => {
      const mockRequest = {};

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(extractTokenFromHeader).not.toHaveBeenCalled();
      expect(verifyToken).not.toHaveBeenCalled();
      expect(validateJwtPayload).not.toHaveBeenCalled();
    });

    it('should return null userId when headers are missing', async () => {
      const mockRequest = {
        req: {},
      };

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(extractTokenFromHeader).not.toHaveBeenCalled();
      expect(verifyToken).not.toHaveBeenCalled();
      expect(validateJwtPayload).not.toHaveBeenCalled();
    });

    it('should return null userId when no token is extracted', async () => {
      const mockRequest = {
        req: {
          headers: {
            authorization: 'Invalid header',
          },
        },
      };

      vi.mocked(extractTokenFromHeader).mockReturnValue(null);

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(extractTokenFromHeader).toHaveBeenCalledWith('Invalid header');
      expect(verifyToken).not.toHaveBeenCalled();
      expect(validateJwtPayload).not.toHaveBeenCalled();
    });

    it('should return null userId when token verification fails', async () => {
      const mockRequest = {
        req: {
          headers: {
            authorization: 'Bearer invalid-token',
          },
        },
      };

      vi.mocked(extractTokenFromHeader).mockReturnValue('invalid-token');
      vi.mocked(verifyToken).mockReturnValue(null);

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(extractTokenFromHeader).toHaveBeenCalledWith(
        'Bearer invalid-token'
      );
      expect(verifyToken).toHaveBeenCalledWith('invalid-token');
      expect(validateJwtPayload).not.toHaveBeenCalled();
    });

    it('should return null userId when payload validation fails', async () => {
      const mockRequest = {
        req: {
          headers: {
            authorization: 'Bearer valid-token',
          },
        },
      };

      vi.mocked(extractTokenFromHeader).mockReturnValue('valid-token');
      vi.mocked(verifyToken).mockReturnValue(mockPayload);
      vi.mocked(validateJwtPayload).mockResolvedValue(false);

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(extractTokenFromHeader).toHaveBeenCalledWith('Bearer valid-token');
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
      expect(validateJwtPayload).toHaveBeenCalledWith(mockPayload);
    });

    it('should return null userId and log error when exception occurs', async () => {
      const mockRequest = {
        req: {
          headers: {
            authorization: 'Bearer valid-token',
          },
        },
      };

      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      vi.mocked(extractTokenFromHeader).mockReturnValue('valid-token');
      vi.mocked(verifyToken).mockImplementation(() => {
        throw new Error('Token verification failed');
      });

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error in auth context:',
        expect.any(Error)
      );
      expect(extractTokenFromHeader).toHaveBeenCalledWith('Bearer valid-token');
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
      expect(validateJwtPayload).not.toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });

    it('should handle validateJwtPayload errors gracefully', async () => {
      const mockRequest = {
        req: {
          headers: {
            authorization: 'Bearer valid-token',
          },
        },
      };

      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      vi.mocked(extractTokenFromHeader).mockReturnValue('valid-token');
      vi.mocked(verifyToken).mockReturnValue(mockPayload);
      vi.mocked(validateJwtPayload).mockRejectedValue(
        new Error('Database error')
      );

      const result = await authContext(mockRequest);

      expect(result).toEqual({ userId: null });
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error in auth context:',
        expect.any(Error)
      );
      expect(extractTokenFromHeader).toHaveBeenCalledWith('Bearer valid-token');
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
      expect(validateJwtPayload).toHaveBeenCalledWith(mockPayload);

      consoleErrorSpy.mockRestore();
    });
  });
});
