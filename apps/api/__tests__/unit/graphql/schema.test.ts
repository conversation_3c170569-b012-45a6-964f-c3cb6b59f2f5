import { describe, it, expect } from 'vitest';
import { schema } from '../../../src/graphql/schema.js';

describe('GraphQL Schema', () => {
  describe('Schema Creation', () => {
    it('should create a valid GraphQL schema', () => {
      expect(schema).toBeDefined();
      expect(schema).toHaveProperty('_queryType');
      expect(schema).toHaveProperty('_typeMap');
    });

    it('should have Query type defined', () => {
      const queryType = schema.getQueryType();
      expect(queryType).toBeDefined();
      expect(queryType?.name).toBe('Query');
    });

    it('should have hello field in Query type', () => {
      const queryType = schema.getQueryType();
      const fields = queryType?.getFields();
      expect(fields?.hello).toBeDefined();
      expect(fields?.hello.type.toString()).toBe('String!');
    });

    it('should have correct schema structure', () => {
      expect(schema.getQueryType()).toBeDefined();
      expect(schema.getMutationType()).toBeUndefined();
      expect(schema.getSubscriptionType()).toBeUndefined();
    });

    it('should have hello resolver function', () => {
      const queryType = schema.getQueryType();
      const fields = queryType?.getFields();
      expect(fields?.hello).toBeDefined();
      expect(fields?.hello.resolve).toBeDefined();
      expect(typeof fields?.hello.resolve).toBe('function');
    });

    it('should have hello resolver that returns correct value', () => {
      const queryType = schema.getQueryType();
      const fields = queryType?.getFields();
      const helloResolver = fields?.hello.resolve;
      
      expect(helloResolver).toBeDefined();
      expect(typeof helloResolver).toBe('function');
      // Note: We can't easily test the resolver execution without proper GraphQL context
      // This is covered by integration tests
    });
  });

  describe('Schema Properties', () => {
    it('should have expected type map entries', () => {
      const typeMap = schema.getTypeMap();
      expect(typeMap).toHaveProperty('Query');
      expect(typeMap).toHaveProperty('String');
      expect(typeMap).toHaveProperty('Boolean');
    });

    it('should have proper directives', () => {
      const directives = schema.getDirectives();
      expect(directives).toBeDefined();
      expect(Array.isArray(directives)).toBe(true);
      expect(directives.length).toBeGreaterThan(0);
    });

    it('should validate schema structure', () => {
      // Basic validation that schema has required properties
      expect(schema.getQueryType()).toBeTruthy();
      expect(schema.getTypeMap().Query).toBeTruthy();
      expect(schema.getDirectives()).toBeTruthy();
    });
  });
}); 