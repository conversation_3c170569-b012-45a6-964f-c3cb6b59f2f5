import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ZodError } from 'zod';
import { categoryResolvers } from '../../../../src/graphql/resolvers/categories.js';
import * as categoriesService from '../../../../src/services/categories/categories.service.js';
import {
  AuthenticationError,
  DatabaseError,
  ValidationError,
  formatZodError,
} from '../../../../src/utils/error-handler.js';
import { logger } from '../../../../src/utils/logger.js';

// Mock dependencies
vi.mock('../../../../src/services/categories/categories.service.js');
vi.mock('../../../../src/utils/logger.js');
vi.mock('../../../../src/utils/error-handler.js');

const mockCategoriesService = categoriesService as any;
const mockLogger = logger as any;
const mockFormatZodError = formatZodError as any;

describe('Category Resolvers', () => {
  const mockUserId = 'user-123';
  const mockCategoryId = 'category-123';
  const mockContext = { userId: mockUserId };
  const mockContextNoAuth = {};

  const mockCategory = {
    id: mockCategoryId,
    userId: mockUserId,
    name: 'Test Category',
    type: 'expense',
    parentId: null,
    isSystem: false,
    isArchived: false,
    displayOrder: 0,
    icon: 'shopping',
    color: '#FF5733',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLogger.error = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Query resolvers', () => {
    describe('categories', () => {
      it('should get user categories successfully without filter', async () => {
        const mockCategories = [mockCategory];
        mockCategoriesService.getUserCategories = vi.fn().mockResolvedValue(mockCategories);

        const result = await categoryResolvers.Query.categories(null, {}, mockContext);

        expect(result).toEqual(mockCategories);
        expect(mockCategoriesService.getUserCategories).toHaveBeenCalledWith(mockUserId, undefined);
      });

             it('should get user categories successfully with filter', async () => {
         const mockCategories = [mockCategory];
         const filter = { type: 'expense' as const, includeArchived: false, includeSystem: true };
         mockCategoriesService.getUserCategories = vi.fn().mockResolvedValue(mockCategories);

        const result = await categoryResolvers.Query.categories(
          null,
          { filter },
          mockContext
        );

        expect(result).toEqual(mockCategories);
        expect(mockCategoriesService.getUserCategories).toHaveBeenCalledWith(mockUserId, filter);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          categoryResolvers.Query.categories(null, {}, mockContextNoAuth)
        ).rejects.toThrow();

        expect(mockCategoriesService.getUserCategories).not.toHaveBeenCalled();
      });

      it('should handle Zod validation errors for filter', async () => {
        const zodError = new ZodError([]);
        const formattedError = new ValidationError('Validation failed');
        mockFormatZodError.mockReturnValue(formattedError);

                 // Mock the filter validation to throw ZodError
         const invalidFilter = { type: 'invalid', includeArchived: false, includeSystem: true };

         await expect(
           categoryResolvers.Query.categories(null, { filter: invalidFilter as any }, mockContext)
         ).rejects.toThrow();

        expect(mockFormatZodError).toHaveBeenCalled();
        expect(mockLogger.error).toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Service validation error');
        mockCategoriesService.getUserCategories = vi.fn().mockRejectedValue(serviceError);

        await expect(
          categoryResolvers.Query.categories(null, {}, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching categories:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockCategoriesService.getUserCategories = vi.fn().mockRejectedValue(unknownError);

        await expect(
          categoryResolvers.Query.categories(null, {}, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching categories:', unknownError);
      });
    });

    describe('categoryTree', () => {
      it('should get category tree successfully without filter', async () => {
        const mockTree = [{ ...mockCategory, children: [] }];
        mockCategoriesService.getCategoryTree = vi.fn().mockResolvedValue(mockTree);

        const result = await categoryResolvers.Query.categoryTree(null, {}, mockContext);

        expect(result).toEqual(mockTree);
        expect(mockCategoriesService.getCategoryTree).toHaveBeenCalledWith(mockUserId, undefined);
      });

             it('should get category tree successfully with filter', async () => {
         const mockTree = [{ ...mockCategory, children: [] }];
         const filter = { type: 'expense' as const, includeArchived: false, includeSystem: true };
         mockCategoriesService.getCategoryTree = vi.fn().mockResolvedValue(mockTree);

        const result = await categoryResolvers.Query.categoryTree(
          null,
          { filter },
          mockContext
        );

        expect(result).toEqual(mockTree);
        expect(mockCategoriesService.getCategoryTree).toHaveBeenCalledWith(mockUserId, filter);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          categoryResolvers.Query.categoryTree(null, {}, mockContextNoAuth)
        ).rejects.toThrow();

        expect(mockCategoriesService.getCategoryTree).not.toHaveBeenCalled();
      });

      it('should handle Zod validation errors for filter', async () => {
        const zodError = new ZodError([]);
        const formattedError = new ValidationError('Validation failed');
        mockFormatZodError.mockReturnValue(formattedError);

                 // Mock the filter validation to throw ZodError
         const invalidFilter = { type: 'invalid', includeArchived: false, includeSystem: true };

         await expect(
           categoryResolvers.Query.categoryTree(null, { filter: invalidFilter as any }, mockContext)
         ).rejects.toThrow();

        expect(mockFormatZodError).toHaveBeenCalled();
        expect(mockLogger.error).toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Service validation error');
        mockCategoriesService.getCategoryTree = vi.fn().mockRejectedValue(serviceError);

        await expect(
          categoryResolvers.Query.categoryTree(null, {}, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching category tree:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockCategoriesService.getCategoryTree = vi.fn().mockRejectedValue(unknownError);

        await expect(
          categoryResolvers.Query.categoryTree(null, {}, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching category tree:', unknownError);
      });
    });

    describe('category', () => {
      it('should get category by id successfully', async () => {
        mockCategoriesService.getCategoryById = vi.fn().mockResolvedValue(mockCategory);

        const result = await categoryResolvers.Query.category(
          null,
          { id: mockCategoryId },
          mockContext
        );

        expect(result).toEqual(mockCategory);
        expect(mockCategoriesService.getCategoryById).toHaveBeenCalledWith(mockCategoryId, mockUserId);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          categoryResolvers.Query.category(null, { id: mockCategoryId }, mockContextNoAuth)
        ).rejects.toThrow();

        expect(mockCategoriesService.getCategoryById).not.toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Category not found');
        mockCategoriesService.getCategoryById = vi.fn().mockRejectedValue(serviceError);

        await expect(
          categoryResolvers.Query.category(null, { id: mockCategoryId }, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching category:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockCategoriesService.getCategoryById = vi.fn().mockRejectedValue(unknownError);

        await expect(
          categoryResolvers.Query.category(null, { id: mockCategoryId }, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching category:', unknownError);
      });
    });
  });

  describe('Mutation resolvers', () => {
    describe('createCategory', () => {
      const createInput = {
        name: 'New Category',
        type: 'expense' as const,
        icon: 'shopping',
        color: '#FF5733',
        displayOrder: 0,
      };

      it('should create category successfully', async () => {
        const createdCategory = { ...mockCategory, ...createInput };
        mockCategoriesService.createCategory = vi.fn().mockResolvedValue(createdCategory);

        const result = await categoryResolvers.Mutation.createCategory(
          null,
          { input: createInput },
          mockContext
        );

        expect(result).toEqual(createdCategory);
        expect(mockCategoriesService.createCategory).toHaveBeenCalledWith(mockUserId, createInput);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          categoryResolvers.Mutation.createCategory(
            null,
            { input: createInput },
            mockContextNoAuth
          )
        ).rejects.toThrow();

        expect(mockCategoriesService.createCategory).not.toHaveBeenCalled();
      });

      it('should handle Zod validation errors', async () => {
        const zodError = new ZodError([]);
        const formattedError = new ValidationError('Validation failed');
        mockFormatZodError.mockReturnValue(formattedError);

        mockCategoriesService.createCategory = vi.fn().mockImplementation(() => {
          throw zodError;
        });

        await expect(
          categoryResolvers.Mutation.createCategory(
            null,
            { input: createInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockFormatZodError).toHaveBeenCalled();
        expect(mockLogger.error).toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Service validation error');
        mockCategoriesService.createCategory = vi.fn().mockRejectedValue(serviceError);

        await expect(
          categoryResolvers.Mutation.createCategory(
            null,
            { input: createInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error creating category:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockCategoriesService.createCategory = vi.fn().mockRejectedValue(unknownError);

        await expect(
          categoryResolvers.Mutation.createCategory(
            null,
            { input: createInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error creating category:', unknownError);
      });
    });

    describe('updateCategory', () => {
      const updateInput = {
        name: 'Updated Category',
        color: '#00FF00',
      };

      it('should update category successfully', async () => {
        const updatedCategory = { ...mockCategory, ...updateInput };
        mockCategoriesService.updateCategory = vi.fn().mockResolvedValue(updatedCategory);

        const result = await categoryResolvers.Mutation.updateCategory(
          null,
          { id: mockCategoryId, input: updateInput },
          mockContext
        );

        expect(result).toEqual(updatedCategory);
        expect(mockCategoriesService.updateCategory).toHaveBeenCalledWith(
          mockCategoryId,
          mockUserId,
          updateInput
        );
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          categoryResolvers.Mutation.updateCategory(
            null,
            { id: mockCategoryId, input: updateInput },
            mockContextNoAuth
          )
        ).rejects.toThrow();

        expect(mockCategoriesService.updateCategory).not.toHaveBeenCalled();
      });

      it('should handle Zod validation errors', async () => {
        const zodError = new ZodError([]);
        const formattedError = new ValidationError('Validation failed');
        mockFormatZodError.mockReturnValue(formattedError);

        mockCategoriesService.updateCategory = vi.fn().mockImplementation(() => {
          throw zodError;
        });

        await expect(
          categoryResolvers.Mutation.updateCategory(
            null,
            { id: mockCategoryId, input: updateInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockFormatZodError).toHaveBeenCalled();
        expect(mockLogger.error).toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Category not found');
        mockCategoriesService.updateCategory = vi.fn().mockRejectedValue(serviceError);

        await expect(
          categoryResolvers.Mutation.updateCategory(
            null,
            { id: mockCategoryId, input: updateInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error updating category:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockCategoriesService.updateCategory = vi.fn().mockRejectedValue(unknownError);

        await expect(
          categoryResolvers.Mutation.updateCategory(
            null,
            { id: mockCategoryId, input: updateInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error updating category:', unknownError);
      });
    });

    describe('deleteCategory', () => {
      it('should delete category successfully', async () => {
        mockCategoriesService.deleteCategory = vi.fn().mockResolvedValue(true);

        const result = await categoryResolvers.Mutation.deleteCategory(
          null,
          { id: mockCategoryId },
          mockContext
        );

        expect(result).toBe(true);
        expect(mockCategoriesService.deleteCategory).toHaveBeenCalledWith(mockCategoryId, mockUserId);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          categoryResolvers.Mutation.deleteCategory(
            null,
            { id: mockCategoryId },
            mockContextNoAuth
          )
        ).rejects.toThrow();

        expect(mockCategoriesService.deleteCategory).not.toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Category not found');
        mockCategoriesService.deleteCategory = vi.fn().mockRejectedValue(serviceError);

        await expect(
          categoryResolvers.Mutation.deleteCategory(
            null,
            { id: mockCategoryId },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error deleting category:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockCategoriesService.deleteCategory = vi.fn().mockRejectedValue(unknownError);

        await expect(
          categoryResolvers.Mutation.deleteCategory(
            null,
            { id: mockCategoryId },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error deleting category:', unknownError);
      });
    });
  });
}); 