import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ZodError } from 'zod';
import { accountResolvers } from '../../../../src/graphql/resolvers/accounts.js';
import * as accountsService from '../../../../src/services/accounts/accounts.service.js';
import {
  AuthenticationError,
  DatabaseError,
  ValidationError,
  formatZodError,
} from '../../../../src/utils/error-handler.js';
import { logger } from '../../../../src/utils/logger.js';

// Mock dependencies
vi.mock('../../../../src/services/accounts/accounts.service.js');
vi.mock('../../../../src/utils/logger.js');
vi.mock('../../../../src/utils/error-handler.js');

const mockAccountsService = accountsService as any;
const mockLogger = logger as any;
const mockFormatZodError = formatZodError as any;

describe('Account Resolvers', () => {
  const mockUserId = 'user-123';
  const mockAccountId = 'account-123';
  const mockContext = { userId: mockUserId };
  const mockContextNoAuth = {};

  const mockAccount = {
    id: mockAccountId,
    userId: mockUserId,
    name: 'Test Account',
    type: 'checking',
    balance: '1000.00',
    currency: 'USD',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLogger.error = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Query resolvers', () => {
    describe('accounts', () => {
      it('should get user accounts successfully', async () => {
        const mockAccounts = [mockAccount];
        mockAccountsService.getUserAccounts = vi.fn().mockResolvedValue(mockAccounts);

        const result = await accountResolvers.Query.accounts(null, {}, mockContext);

        expect(result).toEqual(mockAccounts);
        expect(mockAccountsService.getUserAccounts).toHaveBeenCalledWith(mockUserId);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          accountResolvers.Query.accounts(null, {}, mockContextNoAuth)
        ).rejects.toThrow();

        expect(mockAccountsService.getUserAccounts).not.toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Service validation error');
        mockAccountsService.getUserAccounts = vi.fn().mockRejectedValue(serviceError);

        await expect(
          accountResolvers.Query.accounts(null, {}, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching accounts:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockAccountsService.getUserAccounts = vi.fn().mockRejectedValue(unknownError);

        await expect(
          accountResolvers.Query.accounts(null, {}, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching accounts:', unknownError);
      });
    });

    describe('account', () => {
      it('should get account by id successfully', async () => {
        mockAccountsService.getAccountById = vi.fn().mockResolvedValue(mockAccount);

        const result = await accountResolvers.Query.account(
          null,
          { id: mockAccountId },
          mockContext
        );

        expect(result).toEqual(mockAccount);
        expect(mockAccountsService.getAccountById).toHaveBeenCalledWith(mockAccountId, mockUserId);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          accountResolvers.Query.account(null, { id: mockAccountId }, mockContextNoAuth)
        ).rejects.toThrow();

        expect(mockAccountsService.getAccountById).not.toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Account not found');
        mockAccountsService.getAccountById = vi.fn().mockRejectedValue(serviceError);

        await expect(
          accountResolvers.Query.account(null, { id: mockAccountId }, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching account:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockAccountsService.getAccountById = vi.fn().mockRejectedValue(unknownError);

        await expect(
          accountResolvers.Query.account(null, { id: mockAccountId }, mockContext)
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error fetching account:', unknownError);
      });
    });
  });

  describe('Mutation resolvers', () => {
    describe('createAccount', () => {
      const createInput = {
        name: 'New Account',
        type: 'savings' as const,
        currency: 'USD',
        initialBalance: 500.00,
        includeInNetWorth: true,
        displayOrder: 0,
      };

      it('should create account successfully', async () => {
        const createdAccount = { ...mockAccount, ...createInput };
        mockAccountsService.createAccount = vi.fn().mockResolvedValue(createdAccount);

        const result = await accountResolvers.Mutation.createAccount(
          null,
          { input: createInput },
          mockContext
        );

        expect(result).toEqual(createdAccount);
        expect(mockAccountsService.createAccount).toHaveBeenCalledWith(mockUserId, createInput);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          accountResolvers.Mutation.createAccount(
            null,
            { input: createInput },
            mockContextNoAuth
          )
        ).rejects.toThrow();

        expect(mockAccountsService.createAccount).not.toHaveBeenCalled();
      });

      it('should handle Zod validation errors', async () => {
        const zodError = new ZodError([]);
        const formattedError = new ValidationError('Validation failed');
        mockFormatZodError.mockReturnValue(formattedError);

        // Mock the schema parse to throw ZodError
        const originalParse = vi.fn().mockImplementation(() => {
          throw zodError;
        });
        
        // We need to mock the schema import, but since it's imported at module level,
        // we'll simulate the error by making the service throw a ZodError
        mockAccountsService.createAccount = vi.fn().mockImplementation(() => {
          throw zodError;
        });

        await expect(
          accountResolvers.Mutation.createAccount(
            null,
            { input: createInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockFormatZodError).toHaveBeenCalledWith(zodError);
        expect(mockLogger.error).toHaveBeenCalledWith('Error creating account:', zodError);
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Service validation error');
        mockAccountsService.createAccount = vi.fn().mockRejectedValue(serviceError);

        await expect(
          accountResolvers.Mutation.createAccount(
            null,
            { input: createInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error creating account:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockAccountsService.createAccount = vi.fn().mockRejectedValue(unknownError);

        await expect(
          accountResolvers.Mutation.createAccount(
            null,
            { input: createInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error creating account:', unknownError);
      });
    });

    describe('updateAccount', () => {
      const updateInput = {
        name: 'Updated Account',
        includeInNetWorth: false,
      };

      it('should update account successfully', async () => {
        const updatedAccount = { ...mockAccount, ...updateInput };
        mockAccountsService.updateAccount = vi.fn().mockResolvedValue(updatedAccount);

        const result = await accountResolvers.Mutation.updateAccount(
          null,
          { id: mockAccountId, input: updateInput },
          mockContext
        );

        expect(result).toEqual(updatedAccount);
        expect(mockAccountsService.updateAccount).toHaveBeenCalledWith(
          mockAccountId,
          mockUserId,
          updateInput
        );
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          accountResolvers.Mutation.updateAccount(
            null,
            { id: mockAccountId, input: updateInput },
            mockContextNoAuth
          )
        ).rejects.toThrow();

        expect(mockAccountsService.updateAccount).not.toHaveBeenCalled();
      });

      it('should handle Zod validation errors', async () => {
        const zodError = new ZodError([]);
        const formattedError = new ValidationError('Validation failed');
        mockFormatZodError.mockReturnValue(formattedError);

        mockAccountsService.updateAccount = vi.fn().mockImplementation(() => {
          throw zodError;
        });

        await expect(
          accountResolvers.Mutation.updateAccount(
            null,
            { id: mockAccountId, input: updateInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockFormatZodError).toHaveBeenCalledWith(zodError);
        expect(mockLogger.error).toHaveBeenCalledWith('Error updating account:', zodError);
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Account not found');
        mockAccountsService.updateAccount = vi.fn().mockRejectedValue(serviceError);

        await expect(
          accountResolvers.Mutation.updateAccount(
            null,
            { id: mockAccountId, input: updateInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error updating account:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockAccountsService.updateAccount = vi.fn().mockRejectedValue(unknownError);

        await expect(
          accountResolvers.Mutation.updateAccount(
            null,
            { id: mockAccountId, input: updateInput },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error updating account:', unknownError);
      });
    });

    describe('deleteAccount', () => {
      it('should delete account successfully', async () => {
        mockAccountsService.deleteAccount = vi.fn().mockResolvedValue(true);

        const result = await accountResolvers.Mutation.deleteAccount(
          null,
          { id: mockAccountId },
          mockContext
        );

        expect(result).toBe(true);
        expect(mockAccountsService.deleteAccount).toHaveBeenCalledWith(mockAccountId, mockUserId);
      });

      it('should throw AuthenticationError when user is not authenticated', async () => {
        await expect(
          accountResolvers.Mutation.deleteAccount(
            null,
            { id: mockAccountId },
            mockContextNoAuth
          )
        ).rejects.toThrow();

        expect(mockAccountsService.deleteAccount).not.toHaveBeenCalled();
      });

      it('should handle service errors and rethrow AppErrors', async () => {
        const serviceError = new ValidationError('Account not found');
        mockAccountsService.deleteAccount = vi.fn().mockRejectedValue(serviceError);

        await expect(
          accountResolvers.Mutation.deleteAccount(
            null,
            { id: mockAccountId },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error deleting account:', serviceError);
      });

      it('should handle unknown errors and throw DatabaseError', async () => {
        const unknownError = new Error('Unknown error');
        mockAccountsService.deleteAccount = vi.fn().mockRejectedValue(unknownError);

        await expect(
          accountResolvers.Mutation.deleteAccount(
            null,
            { id: mockAccountId },
            mockContext
          )
        ).rejects.toThrow();

        expect(mockLogger.error).toHaveBeenCalledWith('Error deleting account:', unknownError);
      });
    });
  });
}); 