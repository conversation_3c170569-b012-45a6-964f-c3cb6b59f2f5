import { describe, expect, it } from 'vitest';
import {
  accounts,
  budgets,
  categories,
  goals,
  journalEntries,
  journalLines,
  userSettings,
  users,
} from '../../../src/database/schema.js';
import type {
  Account,
  Budget,
  Category,
  Goal,
  JournalEntry,
  JournalLine,
  User,
  UserSettings,
} from '../../../src/database/types.js';

describe('Database Types', () => {
  describe('Type Inference', () => {
    it('should infer User type correctly from users schema', () => {
      // Create a mock user object that should match the User type
      const mockUser: User = {
        id: 'test-id',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user' as const,
        emailVerified: false,
        phoneNumber: '+**********',
        phoneVerified: false,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      };

      // Verify the type structure matches expected fields
      expect(typeof mockUser.id).toBe('string');
      expect(typeof mockUser.email).toBe('string');
      expect(typeof mockUser.role).toBe('string');
      expect(typeof mockUser.emailVerified).toBe('boolean');
      expect(typeof mockUser.phoneVerified).toBe('boolean');
      expect(typeof mockUser.isDeleted).toBe('boolean');
      expect(mockUser.createdAt).toBeInstanceOf(Date);
      expect(mockUser.updatedAt).toBeInstanceOf(Date);
    });

    it('should infer Account type correctly from accounts schema', () => {
      const mockAccount: Account = {
        id: 'test-id',
        userId: 'user-id',
        name: 'Test Account',
        type: 'checking' as const,
        currency: 'USD',
        initialBalance: '1000.00',
        currentBalance: '1500.00',
        isArchived: false,
        notes: 'Test notes',
        icon: 'bank',
        color: '#FF0000',
        includeInNetWorth: true,
        displayOrder: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockAccount.id).toBe('string');
      expect(typeof mockAccount.userId).toBe('string');
      expect(typeof mockAccount.name).toBe('string');
      expect(typeof mockAccount.type).toBe('string');
      expect(typeof mockAccount.currency).toBe('string');
      expect(typeof mockAccount.initialBalance).toBe('string');
      expect(typeof mockAccount.currentBalance).toBe('string');
      expect(typeof mockAccount.isArchived).toBe('boolean');
      expect(typeof mockAccount.includeInNetWorth).toBe('boolean');
      expect(typeof mockAccount.displayOrder).toBe('number');
    });

    it('should infer Category type correctly from categories schema', () => {
      const mockCategory: Category = {
        id: 'test-id',
        userId: 'user-id',
        parentId: null,
        name: 'Test Category',
        type: 'expense' as const,
        icon: 'shopping',
        color: '#00FF00',
        isDefault: false,
        isSystem: false,
        isArchived: false,
        displayOrder: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockCategory.id).toBe('string');
      expect(typeof mockCategory.userId).toBe('string');
      expect(typeof mockCategory.name).toBe('string');
      expect(typeof mockCategory.type).toBe('string');
      expect(typeof mockCategory.isDefault).toBe('boolean');
      expect(typeof mockCategory.isSystem).toBe('boolean');
      expect(typeof mockCategory.isArchived).toBe('boolean');
      expect(typeof mockCategory.displayOrder).toBe('number');
    });

    it('should infer JournalEntry type correctly from journalEntries schema', () => {
      const mockJournalEntry: JournalEntry = {
        id: 'test-id',
        userId: 'user-id',
        description: 'Test transaction',
        date: new Date(),
        notes: 'Test notes',
        isRecurring: false,
        recurringPattern: null,
        status: 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockJournalEntry.id).toBe('string');
      expect(typeof mockJournalEntry.userId).toBe('string');
      expect(typeof mockJournalEntry.description).toBe('string');
      expect(mockJournalEntry.date).toBeInstanceOf(Date);
      expect(typeof mockJournalEntry.isRecurring).toBe('boolean');
      expect(typeof mockJournalEntry.status).toBe('string');
    });

    it('should infer JournalLine type correctly from journalLines schema', () => {
      const mockJournalLine: JournalLine = {
        id: 'test-id',
        journalEntryId: 'journal-entry-id',
        accountId: 'account-id',
        categoryId: 'category-id',
        amount: '100.00',
        type: 'debit' as const,
        notes: 'Test notes',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockJournalLine.id).toBe('string');
      expect(typeof mockJournalLine.journalEntryId).toBe('string');
      expect(typeof mockJournalLine.accountId).toBe('string');
      expect(typeof mockJournalLine.amount).toBe('string');
      expect(typeof mockJournalLine.type).toBe('string');
    });

    it('should infer Budget type correctly from budgets schema', () => {
      const mockBudget: Budget = {
        id: 'test-id',
        userId: 'user-id',
        name: 'Test Budget',
        amount: '500.00',
        period: 'monthly' as const,
        categoryId: 'category-id',
        startDate: new Date(),
        endDate: new Date(),
        isRecurring: true,
        isArchived: false,
        notes: 'Test notes',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockBudget.id).toBe('string');
      expect(typeof mockBudget.userId).toBe('string');
      expect(typeof mockBudget.name).toBe('string');
      expect(typeof mockBudget.amount).toBe('string');
      expect(typeof mockBudget.period).toBe('string');
      expect(typeof mockBudget.isRecurring).toBe('boolean');
      expect(typeof mockBudget.isArchived).toBe('boolean');
    });

    it('should infer Goal type correctly from goals schema', () => {
      const mockGoal: Goal = {
        id: 'test-id',
        userId: 'user-id',
        name: 'Test Goal',
        type: 'savings' as const,
        targetAmount: '5000.00',
        currentAmount: '1000.00',
        currency: 'USD',
        deadline: new Date(),
        accountId: 'account-id',
        isArchived: false,
        isCompleted: false,
        notes: 'Test notes',
        icon: 'target',
        color: '#0000FF',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockGoal.id).toBe('string');
      expect(typeof mockGoal.userId).toBe('string');
      expect(typeof mockGoal.name).toBe('string');
      expect(typeof mockGoal.type).toBe('string');
      expect(typeof mockGoal.targetAmount).toBe('string');
      expect(typeof mockGoal.currentAmount).toBe('string');
      expect(typeof mockGoal.currency).toBe('string');
      expect(typeof mockGoal.isArchived).toBe('boolean');
      expect(typeof mockGoal.isCompleted).toBe('boolean');
    });

    it('should infer UserSettings type correctly from userSettings schema', () => {
      const mockUserSettings: UserSettings = {
        id: 'test-id',
        userId: 'user-id',
        profilePictureUrl: 'https://example.com/avatar.jpg',
        defaultCurrency: 'USD',
        theme: 'light',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        notificationPreferences: {
          budgetAlerts: true,
          lowBalanceAlerts: true,
          goalAchievedAlerts: true,
          weeklyReports: true,
          marketingEmails: false,
        },
        privacySettings: {
          hideBalances: false,
          requireAuthForSensitiveOperations: true,
        },
        appSettings: {
          defaultView: 'dashboard',
          defaultAccountView: 'all',
          defaultTransactionPeriod: 'month',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(typeof mockUserSettings.id).toBe('string');
      expect(typeof mockUserSettings.userId).toBe('string');
      expect(typeof mockUserSettings.defaultCurrency).toBe('string');
      expect(typeof mockUserSettings.theme).toBe('string');
      expect(typeof mockUserSettings.dateFormat).toBe('string');
      expect(typeof mockUserSettings.timeFormat).toBe('string');
      expect(typeof mockUserSettings.notificationPreferences).toBe('object');
      expect(typeof mockUserSettings.privacySettings).toBe('object');
      expect(typeof mockUserSettings.appSettings).toBe('object');
    });
  });

  describe('Type Compatibility', () => {
    it('should ensure User type is compatible with users table schema', () => {
      // This test ensures that the inferred type matches the actual schema structure
      // If there's a mismatch, TypeScript compilation will fail
      const userFromSchema = users;

      // These assignments should not cause TypeScript errors
      expect(typeof userFromSchema).toBe('object');
      expect(userFromSchema).toBeDefined();
    });

    it('should ensure types can be created without runtime errors', () => {
      // This test ensures that the types are properly defined
      // We can't test runtime properties of empty type objects
      // but we can ensure the types compile correctly

      // These type assertions should not cause TypeScript errors
      const user: User = {} as User;
      const account: Account = {} as Account;
      const category: Category = {} as Category;
      const journalEntry: JournalEntry = {} as JournalEntry;
      const journalLine: JournalLine = {} as JournalLine;
      const budget: Budget = {} as Budget;
      const goal: Goal = {} as Goal;
      const userSettings: UserSettings = {} as UserSettings;

      // If we get here, the types are properly defined
      expect(true).toBe(true);
    });
  });
});
