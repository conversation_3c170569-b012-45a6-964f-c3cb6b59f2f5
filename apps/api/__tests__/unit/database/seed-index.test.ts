import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { existsSync } from 'node:fs';
import { resolve } from 'node:path';

// Mock dependencies
vi.mock('node:fs');
vi.mock('dotenv');
vi.mock('drizzle-orm/postgres-js');
vi.mock('postgres');
vi.mock('../../../src/database/schema.js');
vi.mock('../../../src/database/seed/data/index.js');
vi.mock('../../../src/database/seed/seeds/test/index.js');

const mockExistsSync = vi.mocked(existsSync);
const mockConfig = vi.fn();
const mockPostgres = vi.fn();
const mockDrizzle = vi.fn();
const mockDb = {
  delete: vi.fn().mockResolvedValue(undefined),
  insert: vi.fn().mockReturnValue({
    values: vi.fn().mockResolvedValue(undefined),
  }),
};

// Mock data functions
const mockGetUsersInsertData = vi.fn().mockReturnValue([]);
const mockGetUserSettingsInsertData = vi.fn().mockReturnValue([]);
const mockGetAccountsInsertData = vi.fn().mockReturnValue([]);
const mockGetCategoriesInsertData = vi.fn().mockReturnValue([]);
const mockGetJournalEntriesInsertData = vi.fn().mockReturnValue([]);
const mockGetJournalLinesInsertData = vi.fn().mockReturnValue([]);
const mockGetBudgetsInsertData = vi.fn().mockReturnValue([]);
const mockGetGoalsInsertData = vi.fn().mockReturnValue([]);

const mockTestSeedData = {
  users: [],
  userSettings: [],
  accounts: [],
  categories: [],
  journalEntries: [],
  journalLines: [],
};

// Mock schema
const mockSchema = {
  goals: 'goals',
  budgets: 'budgets',
  journalLines: 'journalLines',
  journalEntries: 'journalEntries',
  categories: 'categories',
  accounts: 'accounts',
  userSettings: 'userSettings',
  users: 'users',
};

vi.doMock('dotenv', () => ({
  config: mockConfig,
}));

vi.doMock('drizzle-orm/postgres-js', () => ({
  drizzle: mockDrizzle,
}));

vi.doMock('postgres', () => ({
  default: mockPostgres,
}));

vi.doMock('../../../src/database/schema.js', () => mockSchema);

vi.doMock('../../../src/database/seed/data/index.js', () => ({
  getUsersInsertData: mockGetUsersInsertData,
  getUserSettingsInsertData: mockGetUserSettingsInsertData,
  getAccountsInsertData: mockGetAccountsInsertData,
  getCategoriesInsertData: mockGetCategoriesInsertData,
  getJournalEntriesInsertData: mockGetJournalEntriesInsertData,
  getJournalLinesInsertData: mockGetJournalLinesInsertData,
  getBudgetsInsertData: mockGetBudgetsInsertData,
  getGoalsInsertData: mockGetGoalsInsertData,
}));

vi.doMock('../../../src/database/seed/seeds/test/index.js', () => ({
  testSeedData: mockTestSeedData,
}));

describe('Database Seed Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env = { ...originalEnv };
    mockExistsSync.mockReturnValue(true);
  });

  afterEach(() => {
    process.env = originalEnv;
    vi.restoreAllMocks();
  });

  describe('Environment File Detection', () => {
    it('should check for .env file in development', () => {
      process.env.NODE_ENV = 'development';
      
      // Test the logic that would be in the seed file
      const nodeEnv = process.env.NODE_ENV;
      const envFile = nodeEnv === 'test' ? '.env.test' : '.env';
      const envPath = resolve(process.cwd(), envFile);
      
      expect(nodeEnv).toBe('development');
      expect(envFile).toBe('.env');
      expect(envPath).toContain('.env');
    });

    it('should check for .env.test file in test environment', () => {
      process.env.NODE_ENV = 'test';
      
      // Test the logic that would be in the seed file
      const nodeEnv = process.env.NODE_ENV;
      const envFile = nodeEnv === 'test' ? '.env.test' : '.env';
      const envPath = resolve(process.cwd(), envFile);
      
      expect(nodeEnv).toBe('test');
      expect(envFile).toBe('.env.test');
      expect(envPath).toContain('.env.test');
    });

    it('should handle missing NODE_ENV gracefully', () => {
      delete process.env.NODE_ENV;
      
      // Test the logic that would be in the seed file
      const nodeEnv = process.env.NODE_ENV;
      const envFile = nodeEnv === 'test' ? '.env.test' : '.env';
      
      expect(nodeEnv).toBeUndefined();
      expect(envFile).toBe('.env');
    });
  });

  describe('Database URL Validation', () => {
    it('should require DATABASE_URL to be set', () => {
      delete process.env.DATABASE_URL;
      
      const databaseUrl = process.env.DATABASE_URL;
      expect(databaseUrl).toBeUndefined();
      
      // This would trigger an error in the actual seed file
      if (!databaseUrl) {
        expect(true).toBe(true); // Simulating the error condition
      }
    });

    it('should accept valid DATABASE_URL', () => {
      const testUrl = 'postgresql://test:password@localhost:5432/testdb';
      process.env.DATABASE_URL = testUrl;
      
      const databaseUrl = process.env.DATABASE_URL;
      expect(databaseUrl).toBe(testUrl);
      expect(databaseUrl).toMatch(/^postgresql:\/\//);
    });

    it('should handle different database URL formats', () => {
      const urls = [
        'postgresql://user:pass@localhost:5432/db',
        'postgres://user:pass@localhost:5432/db',
        'postgresql://localhost/db',
      ];

      urls.forEach(url => {
        process.env.DATABASE_URL = url;
        const databaseUrl = process.env.DATABASE_URL;
        expect(databaseUrl).toBe(url);
        expect(databaseUrl).toMatch(/^postgres(ql)?:\/\//);
      });
    });
  });

  describe('Environment-based Configuration', () => {
    it('should use different seed data for test environment', () => {
      process.env.NODE_ENV = 'test';
      
      const isTestEnv = process.env.NODE_ENV === 'test';
      expect(isTestEnv).toBe(true);
      
      // In test environment, we would use test seed data
      const seedDataSource = isTestEnv ? 'test-data' : 'dev-data';
      expect(seedDataSource).toBe('test-data');
    });

    it('should use development seed data for non-test environments', () => {
      process.env.NODE_ENV = 'development';
      
      const isTestEnv = process.env.NODE_ENV === 'test';
      expect(isTestEnv).toBe(false);
      
      // In non-test environment, we would use development seed data
      const seedDataSource = isTestEnv ? 'test-data' : 'dev-data';
      expect(seedDataSource).toBe('dev-data');
    });

    it('should handle production environment', () => {
      process.env.NODE_ENV = 'production';
      
      const nodeEnv = process.env.NODE_ENV;
      expect(nodeEnv).toBe('production');
      
      // Production should not run seeding typically
      const shouldSeed = nodeEnv !== 'production';
      expect(shouldSeed).toBe(false);
    });
  });

  describe('File Path Resolution', () => {
    it('should resolve correct paths for environment files', () => {
      const envFile = '.env';
      const envPath = resolve(process.cwd(), envFile);
      
      expect(envPath).toContain('.env');
      expect(envPath).toMatch(/.*\.env$/);
    });

    it('should resolve correct paths for test environment files', () => {
      const envFile = '.env.test';
      const envPath = resolve(process.cwd(), envFile);
      
      expect(envPath).toContain('.env.test');
      expect(envPath).toMatch(/.*\.env\.test$/);
    });
  });

  describe('Seed Data Structure Validation', () => {
    it('should validate expected seed data structure', () => {
      // Test the expected structure of seed data
      const expectedTables = [
        'users',
        'userSettings', 
        'accounts',
        'categories',
        'journalEntries',
        'journalLines',
        'budgets',
        'goals'
      ];

      expectedTables.forEach(table => {
        expect(typeof table).toBe('string');
        expect(table.length).toBeGreaterThan(0);
      });

      expect(expectedTables).toHaveLength(8);
    });

    it('should validate table deletion order', () => {
      // Test the correct order for table deletion (reverse dependency order)
      const deletionOrder = [
        'goals',
        'budgets', 
        'journalLines',
        'journalEntries',
        'categories',
        'accounts',
        'userSettings',
        'users'
      ];

      expect(deletionOrder).toHaveLength(8);
      expect(deletionOrder[0]).toBe('goals'); // Should delete dependent tables first
      expect(deletionOrder[deletionOrder.length - 1]).toBe('users'); // Users should be deleted last
    });
  });
}); 