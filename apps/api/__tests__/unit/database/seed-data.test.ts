import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

describe('Seed Data Utilities', () => {
  beforeEach(() => {
    // Mock Date.now to ensure consistent timestamps in tests
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-01-01T00:00:00.000Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('getUsersInsertData', () => {
    it('should return an array of user objects', async () => {
      const { getUsersInsertData } = await import(
        '../../../src/database/seed/data/index.js'
      );
      const users = getUsersInsertData();

      expect(Array.isArray(users)).toBe(true);
      expect(users.length).toBeGreaterThan(0);
    });

    it('should include required user fields', async () => {
      const { getUsersInsertData } = await import(
        '../../../src/database/seed/data/index.js'
      );
      const users = getUsersInsertData();
      const user = users[0];

      expect(user).toHaveProperty('id');
      expect(user).toHaveProperty('email');
      expect(user).toHaveProperty('passwordHash');
      expect(user).toHaveProperty('firstName');
      expect(user).toHaveProperty('lastName');
      expect(user).toHaveProperty('role');
      expect(user).toHaveProperty('emailVerified');
      expect(user).toHaveProperty('phoneVerified');
      expect(user).toHaveProperty('isDeleted');
      expect(user).toHaveProperty('createdAt');
      expect(user).toHaveProperty('updatedAt');
    });

    it('should have valid user role values', async () => {
      const { getUsersInsertData } = await import(
        '../../../src/database/seed/data/index.js'
      );
      const users = getUsersInsertData();

      for (const user of users) {
        expect(['user', 'admin']).toContain(user.role);
      }
    });

    it('should have valid email formats', async () => {
      const { getUsersInsertData } = await import(
        '../../../src/database/seed/data/index.js'
      );
      const users = getUsersInsertData();
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      for (const user of users) {
        expect(emailRegex.test(user.email)).toBe(true);
      }
    });

    it('should have consistent timestamp fields', async () => {
      const { getUsersInsertData } = await import(
        '../../../src/database/seed/data/index.js'
      );
      const users = getUsersInsertData();

      for (const user of users) {
        expect(user.createdAt).toBeInstanceOf(Date);
        expect(user.updatedAt).toBeInstanceOf(Date);
        expect(user.isDeleted).toBe(false);
      }
    });
  });

  describe('Seed Data Module', () => {
    it('should export seed data functions', async () => {
      const seedModule = await import(
        '../../../src/database/seed/data/index.js'
      );

      // Check that the module exports exist
      expect(seedModule.getUsersInsertData).toBeDefined();
      expect(typeof seedModule.getUsersInsertData).toBe('function');
    });

    it('should generate valid seed data', async () => {
      const { getUsersInsertData } = await import(
        '../../../src/database/seed/data/index.js'
      );
      const users = getUsersInsertData();

      // Basic validation
      expect(Array.isArray(users)).toBe(true);
      expect(users.length).toBeGreaterThan(0);

      // Check first user has required fields
      const firstUser = users[0];
      expect(firstUser.id).toBeDefined();
      expect(firstUser.email).toBeDefined();
      expect(firstUser.role).toBeDefined();
      expect(firstUser.createdAt).toBeInstanceOf(Date);
      expect(firstUser.updatedAt).toBeInstanceOf(Date);
    });
  });
});
