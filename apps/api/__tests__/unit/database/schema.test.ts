import { describe, expect, it } from 'vitest';
import {
  accountTypeEnum,
  accounts,
  budgetPeriodEnum,
  budgets,
  categories,
  categoryTypeEnum,
  goalTypeEnum,
  goals,
  journalEntries,
  journalLines,
  recurringPatternTypeEnum,
  transactionEntryTypeEnum,
  transactionTypeEnum,
  userRoleEnum,
  userSettings,
  users,
} from '../../../src/database/schema.js';

describe('Database Schema', () => {
  describe('Enums', () => {
    it('should define userRoleEnum with correct values', () => {
      expect(userRoleEnum.enumName).toBe('user_role');
      expect(userRoleEnum.enumValues).toEqual(['user', 'admin']);
    });

    it('should define accountTypeEnum with correct values', () => {
      expect(accountTypeEnum.enumName).toBe('account_type');
      expect(accountTypeEnum.enumValues).toEqual([
        'checking',
        'savings',
        'credit_card',
        'cash',
        'investment',
        'loan',
        'asset',
        'liability',
      ]);
    });

    it('should define transactionTypeEnum with correct values', () => {
      expect(transactionTypeEnum.enumName).toBe('transaction_type');
      expect(transactionTypeEnum.enumValues).toEqual([
        'income',
        'expense',
        'transfer',
      ]);
    });

    it('should define transactionEntryTypeEnum with correct values', () => {
      expect(transactionEntryTypeEnum.enumName).toBe('transaction_entry_type');
      expect(transactionEntryTypeEnum.enumValues).toEqual(['debit', 'credit']);
    });

    it('should define categoryTypeEnum with correct values', () => {
      expect(categoryTypeEnum.enumName).toBe('category_type');
      expect(categoryTypeEnum.enumValues).toEqual(['income', 'expense']);
    });

    it('should define budgetPeriodEnum with correct values', () => {
      expect(budgetPeriodEnum.enumName).toBe('budget_period');
      expect(budgetPeriodEnum.enumValues).toEqual([
        'daily',
        'weekly',
        'monthly',
        'quarterly',
        'yearly',
      ]);
    });

    it('should define goalTypeEnum with correct values', () => {
      expect(goalTypeEnum.enumName).toBe('goal_type');
      expect(goalTypeEnum.enumValues).toEqual([
        'savings',
        'debt_payoff',
        'purchase',
        'emergency_fund',
        'other',
      ]);
    });

    it('should define recurringPatternTypeEnum with correct values', () => {
      expect(recurringPatternTypeEnum.enumName).toBe('recurring_pattern_type');
      expect(recurringPatternTypeEnum.enumValues).toEqual([
        'daily',
        'weekly',
        'monthly',
        'yearly',
      ]);
    });
  });

  describe('Table Definitions', () => {
    it('should define users table with correct structure', () => {
      // Check that the table object exists and has expected properties
      expect(users).toBeDefined();
      expect(typeof users).toBe('object');

      // Check that key fields exist on the table
      expect(users.id).toBeDefined();
      expect(users.email).toBeDefined();
      expect(users.passwordHash).toBeDefined();
      expect(users.firstName).toBeDefined();
      expect(users.lastName).toBeDefined();
      expect(users.role).toBeDefined();
      expect(users.emailVerified).toBeDefined();
      expect(users.phoneNumber).toBeDefined();
      expect(users.phoneVerified).toBeDefined();
      expect(users.lastLoginAt).toBeDefined();
      expect(users.createdAt).toBeDefined();
      expect(users.updatedAt).toBeDefined();
      expect(users.isDeleted).toBeDefined();
    });

    it('should define accounts table with correct structure', () => {
      // Check that the table object exists and has expected properties
      expect(accounts).toBeDefined();
      expect(typeof accounts).toBe('object');

      // Check that key fields exist on the table
      expect(accounts.id).toBeDefined();
      expect(accounts.userId).toBeDefined();
      expect(accounts.name).toBeDefined();
      expect(accounts.type).toBeDefined();
      expect(accounts.currency).toBeDefined();
      expect(accounts.initialBalance).toBeDefined();
      expect(accounts.currentBalance).toBeDefined();
      expect(accounts.isArchived).toBeDefined();
      expect(accounts.notes).toBeDefined();
      expect(accounts.icon).toBeDefined();
      expect(accounts.color).toBeDefined();
      expect(accounts.includeInNetWorth).toBeDefined();
      expect(accounts.displayOrder).toBeDefined();
      expect(accounts.createdAt).toBeDefined();
      expect(accounts.updatedAt).toBeDefined();
    });

    it('should define all required tables', () => {
      // Check that all table objects exist
      expect(categories).toBeDefined();
      expect(journalEntries).toBeDefined();
      expect(journalLines).toBeDefined();
      expect(budgets).toBeDefined();
      expect(goals).toBeDefined();
      expect(userSettings).toBeDefined();

      // Check that they are objects
      expect(typeof categories).toBe('object');
      expect(typeof journalEntries).toBe('object');
      expect(typeof journalLines).toBe('object');
      expect(typeof budgets).toBe('object');
      expect(typeof goals).toBe('object');
      expect(typeof userSettings).toBe('object');
    });

    it('should have all tables with id fields', () => {
      // All tables should have id fields
      expect(users.id).toBeDefined();
      expect(accounts.id).toBeDefined();
      expect(categories.id).toBeDefined();
      expect(journalEntries.id).toBeDefined();
      expect(journalLines.id).toBeDefined();
      expect(budgets.id).toBeDefined();
      expect(goals.id).toBeDefined();
      expect(userSettings.id).toBeDefined();
    });

    it('should have all tables with timestamp fields', () => {
      // All tables should have createdAt and updatedAt
      expect(users.createdAt).toBeDefined();
      expect(users.updatedAt).toBeDefined();
      expect(accounts.createdAt).toBeDefined();
      expect(accounts.updatedAt).toBeDefined();
      expect(categories.createdAt).toBeDefined();
      expect(categories.updatedAt).toBeDefined();
      expect(journalEntries.createdAt).toBeDefined();
      expect(journalEntries.updatedAt).toBeDefined();
      expect(journalLines.createdAt).toBeDefined();
      expect(journalLines.updatedAt).toBeDefined();
      expect(budgets.createdAt).toBeDefined();
      expect(budgets.updatedAt).toBeDefined();
      expect(goals.createdAt).toBeDefined();
      expect(goals.updatedAt).toBeDefined();
      expect(userSettings.createdAt).toBeDefined();
      expect(userSettings.updatedAt).toBeDefined();
    });
  });

  describe('Schema Exports', () => {
    it('should export all required schema elements', () => {
      // Check that all exports are defined
      expect(users).toBeDefined();
      expect(accounts).toBeDefined();
      expect(categories).toBeDefined();
      expect(journalEntries).toBeDefined();
      expect(journalLines).toBeDefined();
      expect(budgets).toBeDefined();
      expect(goals).toBeDefined();
      expect(userSettings).toBeDefined();

      // Check that all enums are defined
      expect(userRoleEnum).toBeDefined();
      expect(accountTypeEnum).toBeDefined();
      expect(transactionTypeEnum).toBeDefined();
      expect(transactionEntryTypeEnum).toBeDefined();
      expect(categoryTypeEnum).toBeDefined();
      expect(budgetPeriodEnum).toBeDefined();
      expect(goalTypeEnum).toBeDefined();
      expect(recurringPatternTypeEnum).toBeDefined();
    });
  });
});
