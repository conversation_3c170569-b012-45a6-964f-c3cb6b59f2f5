import { describe, expect, it } from 'vitest';

describe('Database Index Exports', () => {
  describe('Client exports', () => {
    it('should export createClient function', async () => {
      const { createClient } = await import('../../../src/database/index.js');
      expect(typeof createClient).toBe('function');
    });

    it('should export db instance', async () => {
      const { db } = await import('../../../src/database/index.js');
      // db might be null if DATABASE_URL is not set, which is fine for testing
      expect(db === null || typeof db === 'object').toBe(true);
    });
  });

  describe('Schema enum exports', () => {
    it('should export userRoleEnum', async () => {
      const { userRoleEnum } = await import('../../../src/database/index.js');
      expect(userRoleEnum).toBeDefined();
      expect(userRoleEnum.enumName).toBe('user_role');
    });

    it('should export accountTypeEnum', async () => {
      const { accountTypeEnum } = await import(
        '../../../src/database/index.js'
      );
      expect(accountTypeEnum).toBeDefined();
      expect(accountTypeEnum.enumName).toBe('account_type');
    });

    it('should export transactionTypeEnum', async () => {
      const { transactionTypeEnum } = await import(
        '../../../src/database/index.js'
      );
      expect(transactionTypeEnum).toBeDefined();
      expect(transactionTypeEnum.enumName).toBe('transaction_type');
    });

    it('should export transactionEntryTypeEnum', async () => {
      const { transactionEntryTypeEnum } = await import(
        '../../../src/database/index.js'
      );
      expect(transactionEntryTypeEnum).toBeDefined();
      expect(transactionEntryTypeEnum.enumName).toBe('transaction_entry_type');
    });

    it('should export categoryTypeEnum', async () => {
      const { categoryTypeEnum } = await import(
        '../../../src/database/index.js'
      );
      expect(categoryTypeEnum).toBeDefined();
      expect(categoryTypeEnum.enumName).toBe('category_type');
    });

    it('should export budgetPeriodEnum', async () => {
      const { budgetPeriodEnum } = await import(
        '../../../src/database/index.js'
      );
      expect(budgetPeriodEnum).toBeDefined();
      expect(budgetPeriodEnum.enumName).toBe('budget_period');
    });

    it('should export goalTypeEnum', async () => {
      const { goalTypeEnum } = await import('../../../src/database/index.js');
      expect(goalTypeEnum).toBeDefined();
      expect(goalTypeEnum.enumName).toBe('goal_type');
    });

    it('should export recurringPatternTypeEnum', async () => {
      const { recurringPatternTypeEnum } = await import(
        '../../../src/database/index.js'
      );
      expect(recurringPatternTypeEnum).toBeDefined();
      expect(recurringPatternTypeEnum.enumName).toBe('recurring_pattern_type');
    });
  });

  describe('Schema table exports', () => {
    it('should export users table', async () => {
      const { users } = await import('../../../src/database/index.js');
      expect(users).toBeDefined();
      expect(typeof users).toBe('object');
    });

    it('should export accounts table', async () => {
      const { accounts } = await import('../../../src/database/index.js');
      expect(accounts).toBeDefined();
      expect(typeof accounts).toBe('object');
    });

    it('should export categories table', async () => {
      const { categories } = await import('../../../src/database/index.js');
      expect(categories).toBeDefined();
      expect(typeof categories).toBe('object');
    });

    it('should export journalEntries table', async () => {
      const { journalEntries } = await import('../../../src/database/index.js');
      expect(journalEntries).toBeDefined();
      expect(typeof journalEntries).toBe('object');
    });

    it('should export journalLines table', async () => {
      const { journalLines } = await import('../../../src/database/index.js');
      expect(journalLines).toBeDefined();
      expect(typeof journalLines).toBe('object');
    });

    it('should export budgets table', async () => {
      const { budgets } = await import('../../../src/database/index.js');
      expect(budgets).toBeDefined();
      expect(typeof budgets).toBe('object');
    });

    it('should export goals table', async () => {
      const { goals } = await import('../../../src/database/index.js');
      expect(goals).toBeDefined();
      expect(typeof goals).toBe('object');
    });

    it('should export userSettings table', async () => {
      const { userSettings } = await import('../../../src/database/index.js');
      expect(userSettings).toBeDefined();
      expect(typeof userSettings).toBe('object');
    });
  });

  describe('Type exports', () => {
    it('should export all database types', async () => {
      // Import types to ensure they exist and are properly exported
      const module = await import('../../../src/database/index.js');

      // These are type-only exports, so we can't test them directly at runtime
      // But we can ensure the module imports without errors
      expect(module).toBeDefined();
    });

    it('should have consistent type exports', async () => {
      // Test that we can import the module without TypeScript errors
      const {
        User,
        Account,
        Category,
        JournalEntry,
        JournalLine,
        Budget,
        Goal,
        UserSettings,
        CurrencyCode,
        Money,
        RecurringPattern,
      } = await import('../../../src/database/types.js');

      // These are type imports, so we can't test them at runtime
      // But the import should succeed without errors
      expect(true).toBe(true);
    });
  });

  describe('Module structure', () => {
    it('should export all expected items', async () => {
      const module = await import('../../../src/database/index.js');

      // Check that all expected exports are present
      const expectedExports = [
        // Client exports
        'createClient',
        'db',

        // Enum exports
        'userRoleEnum',
        'accountTypeEnum',
        'transactionTypeEnum',
        'transactionEntryTypeEnum',
        'categoryTypeEnum',
        'budgetPeriodEnum',
        'goalTypeEnum',
        'recurringPatternTypeEnum',

        // Table exports
        'users',
        'accounts',
        'categories',
        'journalEntries',
        'journalLines',
        'budgets',
        'goals',
        'userSettings',
      ];

      for (const exportName of expectedExports) {
        expect(module).toHaveProperty(exportName);
      }
    });

    it('should not export unexpected items', async () => {
      const module = await import('../../../src/database/index.js');

      // Get all exported keys
      const exportedKeys = Object.keys(module);

      // Define expected exports
      const expectedExports = [
        'createClient',
        'db',
        'userRoleEnum',
        'accountTypeEnum',
        'transactionTypeEnum',
        'transactionEntryTypeEnum',
        'categoryTypeEnum',
        'budgetPeriodEnum',
        'goalTypeEnum',
        'recurringPatternTypeEnum',
        'users',
        'accounts',
        'categories',
        'journalEntries',
        'journalLines',
        'budgets',
        'goals',
        'userSettings',
      ];

      // Check that we don't have unexpected exports
      for (const key of exportedKeys) {
        expect(expectedExports).toContain(key);
      }
    });

    it('should maintain consistent export structure', async () => {
      const module = await import('../../../src/database/index.js');

      // Verify that enums have the expected structure
      expect(module.userRoleEnum.enumName).toBe('user_role');
      expect(module.accountTypeEnum.enumName).toBe('account_type');

      // Verify that tables are objects
      expect(typeof module.users).toBe('object');
      expect(typeof module.accounts).toBe('object');

      // Verify that client exports are functions/objects
      expect(typeof module.createClient).toBe('function');
    });
  });

  describe('Import compatibility', () => {
    it('should support named imports', async () => {
      // Test that named imports work correctly
      const { createClient, users, userRoleEnum } = await import(
        '../../../src/database/index.js'
      );

      expect(typeof createClient).toBe('function');
      expect(typeof users).toBe('object');
      expect(userRoleEnum.enumName).toBe('user_role');
    });

    it('should support namespace imports', async () => {
      // Test that namespace imports work correctly
      const db = await import('../../../src/database/index.js');

      expect(typeof db.createClient).toBe('function');
      expect(typeof db.users).toBe('object');
      expect(db.userRoleEnum.enumName).toBe('user_role');
    });

    it('should support mixed import styles', async () => {
      // Test that we can mix default and named imports
      const { createClient } = await import('../../../src/database/index.js');
      const db = await import('../../../src/database/index.js');

      expect(createClient).toBe(db.createClient);
    });
  });

  describe('Re-export integrity', () => {
    it('should re-export items from correct source modules', async () => {
      // Import from index
      const indexModule = await import('../../../src/database/index.js');

      // Import from source modules
      const clientModule = await import('../../../src/database/client.js');
      const schemaModule = await import('../../../src/database/schema.js');
      const typesModule = await import('../../../src/database/types.js');

      // Verify re-exports match source exports
      expect(indexModule.createClient).toBe(clientModule.createClient);
      expect(indexModule.users).toBe(schemaModule.users);
      expect(indexModule.userRoleEnum).toBe(schemaModule.userRoleEnum);
    });

    it('should maintain reference equality for re-exported items', async () => {
      // Import the same item multiple times
      const { users: users1 } = await import('../../../src/database/index.js');
      const { users: users2 } = await import('../../../src/database/index.js');

      // Should be the same reference
      expect(users1).toBe(users2);
    });
  });
});
