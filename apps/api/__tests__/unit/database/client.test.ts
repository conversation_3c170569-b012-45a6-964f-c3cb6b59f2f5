import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock postgres
const mockSql = {
  end: vi.fn().mockResolvedValue(undefined),
};

const mockPostgres = vi.fn().mockReturnValue(mockSql);

vi.mock('postgres', () => ({
  default: mockPostgres,
}));

// Mock drizzle
const mockDb = {
  query: vi.fn(),
  select: vi.fn(),
  insert: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
};

const mockDrizzle = vi.fn().mockReturnValue(mockDb);

vi.mock('drizzle-orm/postgres-js', () => ({
  drizzle: mockDrizzle,
}));

describe('Database Client', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Ensure mocks return expected values
    mockPostgres.mockReturnValue(mockSql);
    mockDrizzle.mockReturnValue(mockDb);

    // Set a consistent DATABASE_URL for unit tests
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('createClient function', () => {
    it('should create a database client with environment URL', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
      expect(mockDrizzle).toHaveBeenCalledWith(mockSql, {
        schema: expect.any(Object),
      });
      expect(client).toBeDefined();
    });

    it('should create a database client with custom URL', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const customUrl = 'postgresql://custom:custom@localhost:5432/custom';
      const client = createClient(customUrl);

      expect(mockPostgres).toHaveBeenCalledWith(
        customUrl,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
      expect(mockDrizzle).toHaveBeenCalledWith(mockSql, {
        schema: expect.any(Object),
      });
      expect(client).toBeDefined();
    });

    it('should use environment URL when empty string is provided', async () => {
      // When empty string is provided, it should fall back to environment DATABASE_URL
      const { createClient } = await import('../../../src/database/client.js');

      // Empty string should fall back to environment URL
      expect(() => createClient('')).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should prefer custom URL over environment URL', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const customUrl = 'postgresql://custom:custom@localhost:5432/custom';
      process.env.DATABASE_URL = 'postgresql://env:env@localhost:5432/env';

      createClient(customUrl);

      expect(mockPostgres).toHaveBeenCalledWith(
        customUrl,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
      expect(mockPostgres).not.toHaveBeenCalledWith(process.env.DATABASE_URL);
    });

    it('should include end method in returned client', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      expect(client.end).toBeDefined();
      expect(typeof client.end).toBe('function');
    });

    it('should call postgres end method when client end is called', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      await client.end();

      expect(mockSql.end).toHaveBeenCalledTimes(1);
    });

    it('should include all drizzle methods in returned client', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      expect(client.query).toBeDefined();
      expect(client.select).toBeDefined();
      expect(client.insert).toBeDefined();
      expect(client.update).toBeDefined();
      expect(client.delete).toBeDefined();
    });
  });

  describe('URL validation', () => {
    it('should accept valid PostgreSQL URLs', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const validUrls = [
        'postgresql://user:pass@localhost:5432/db',
        'postgres://user:pass@localhost:5432/db',
        'postgresql://user@localhost/db',
        'postgresql://localhost/db',
        'postgresql://user:<EMAIL>:5432/db?ssl=true',
      ];

      for (const url of validUrls) {
        expect(() => createClient(url)).not.toThrow();
      }
    });

    it('should handle empty string URL by falling back to environment', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      expect(() => createClient('')).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle null URL by falling back to environment', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      expect(() => createClient(null as unknown as string)).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle undefined URL when environment is set', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      // When undefined is passed, it should fall back to the environment DATABASE_URL
      expect(() => createClient(undefined)).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });
  });

  describe('Connection handling', () => {
    it('should pass connection URL to postgres client', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const testUrl = 'postgresql://test:test@localhost:5432/testdb';
      createClient(testUrl);

      expect(mockPostgres).toHaveBeenCalledWith(
        testUrl,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should pass schema to drizzle', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      createClient();

      expect(mockDrizzle).toHaveBeenCalledWith(mockSql, {
        schema: expect.any(Object),
      });
    });

    it('should handle connection errors gracefully', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      mockPostgres.mockImplementationOnce(() => {
        throw new Error('Connection failed');
      });

      expect(() => createClient()).toThrow('Connection failed');
    });

    it('should handle drizzle initialization errors', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      mockDrizzle.mockImplementationOnce(() => {
        throw new Error('Drizzle initialization failed');
      });

      expect(() => createClient()).toThrow('Drizzle initialization failed');
    });
  });

  describe('Client methods', () => {
    it('should preserve all drizzle methods', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      // Check that all mocked drizzle methods are available
      expect(client.query).toBe(mockDb.query);
      expect(client.select).toBe(mockDb.select);
      expect(client.insert).toBe(mockDb.insert);
      expect(client.update).toBe(mockDb.update);
      expect(client.delete).toBe(mockDb.delete);
    });

    it('should add end method that calls postgres end', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      await client.end();

      expect(mockSql.end).toHaveBeenCalledTimes(1);
    });

    it('should handle end method errors', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      mockSql.end.mockRejectedValueOnce(new Error('End failed'));
      const client = createClient();

      await expect(client.end()).rejects.toThrow('End failed');
    });

    it('should allow multiple calls to end method', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      await client.end();
      await client.end();

      expect(mockSql.end).toHaveBeenCalledTimes(2);
    });
  });

  describe('Environment variable handling', () => {
    it('should use DATABASE_URL when no custom URL provided', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      createClient();

      // Should use the DATABASE_URL from environment
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle missing DATABASE_URL gracefully', async () => {
      // In unit tests, empty string falls back to environment URL
      const { createClient } = await import('../../../src/database/client.js');

      expect(() => createClient('')).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle empty DATABASE_URL', async () => {
      // Test the same logic - empty string falls back to environment URL
      const { createClient } = await import('../../../src/database/client.js');

      expect(() => createClient('')).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        process.env.DATABASE_URL,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle whitespace-only DATABASE_URL', async () => {
      const { createClient } = await import('../../../src/database/client.js');

      // The function checks for falsy values, so whitespace should pass through to postgres
      // postgres will handle the invalid URL
      expect(() => createClient('   ')).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        '   ',
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });
  });

  describe('Type safety', () => {
    it('should return client with correct type signature', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const client = createClient();

      // TypeScript should ensure these methods exist
      expect(typeof client.query).toBe('function');
      expect(typeof client.select).toBe('function');
      expect(typeof client.insert).toBe('function');
      expect(typeof client.update).toBe('function');
      expect(typeof client.delete).toBe('function');
      expect(typeof client.end).toBe('function');
    });

    it('should maintain schema typing', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      createClient();

      // The client should be typed with the schema
      expect(mockDrizzle).toHaveBeenCalledWith(
        mockSql,
        expect.objectContaining({
          schema: expect.any(Object),
        })
      );
    });
  });

  describe('Edge cases', () => {
    it('should handle very long URLs', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const longUrl = `postgresql://user:pass@${'a'.repeat(1000)}.com:5432/db`;

      expect(() => createClient(longUrl)).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        longUrl,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle URLs with special characters', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const specialUrl =
        'postgresql://user%40domain:p%40ssw0rd@localhost:5432/db-name_test';

      expect(() => createClient(specialUrl)).not.toThrow();
      expect(mockPostgres).toHaveBeenCalledWith(
        specialUrl,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
    });

    it('should handle concurrent client creation', async () => {
      const { createClient } = await import('../../../src/database/client.js');
      const url1 = 'postgresql://user1:pass1@localhost:5432/db1';
      const url2 = 'postgresql://user2:pass2@localhost:5432/db2';

      // Create different mock objects for each call
      const mockDb1 = { ...mockDb };
      const mockDb2 = { ...mockDb };
      mockDrizzle.mockReturnValueOnce(mockDb1).mockReturnValueOnce(mockDb2);

      const client1 = createClient(url1);
      const client2 = createClient(url2);

      expect(mockPostgres).toHaveBeenCalledTimes(2);
      expect(mockPostgres).toHaveBeenNthCalledWith(
        1,
        url1,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
      expect(mockPostgres).toHaveBeenNthCalledWith(
        2,
        url2,
        expect.objectContaining({
          max: 1,
          idle_timeout: 20,
          connect_timeout: 10,
          max_lifetime: 1800,
          onnotice: expect.any(Function),
          transform: { undefined: null },
        })
      );
      expect(client1).not.toBe(client2);
    });
  });
});
