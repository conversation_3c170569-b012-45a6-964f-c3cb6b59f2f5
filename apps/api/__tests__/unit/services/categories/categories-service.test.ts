import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { and, eq, isNull } from 'drizzle-orm';
import {
  createDefaultCategories,
  getUserCategories,
  getCategoryTree,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
} from '../../../../src/services/categories/categories.service.js';
import { db } from '../../../../src/database/client.js';
import { categories } from '../../../../src/database/schema.js';
import { logger } from '../../../../src/utils/logger.js';
import {
  DatabaseError,
  RecordNotFoundError,
  ValidationError,
} from '../../../../src/utils/error-handler.js';

// Mock dependencies
vi.mock('../../../../src/database/client.js');
vi.mock('../../../../src/utils/logger.js');

const mockDb = db as any;
const mockLogger = logger as any;

describe('Categories Service', () => {
  const mockUserId = 'user-123';
  const mockCategoryId = 'cat-123';
  const mockParentId = 'parent-123';

  const mockCategory = {
    id: mockCategoryId,
    userId: mockUserId,
    parentId: null,
    name: 'Test Category',
    type: 'expense',
    icon: 'test-icon',
    color: '#FF0000',
    isDefault: false,
    isSystem: false,
    isArchived: false,
    displayOrder: 1,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLogger.info = vi.fn();
    mockLogger.error = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('createDefaultCategories', () => {
    it('should create default categories successfully', async () => {
      const mockCreatedCategories = [
        { ...mockCategory, name: 'Salary', type: 'income', isDefault: true, isSystem: true },
        { ...mockCategory, name: 'Investments', type: 'income', isDefault: true, isSystem: true },
        { ...mockCategory, name: 'Housing', type: 'expense', isDefault: true, isSystem: true },
        { ...mockCategory, name: 'Food', type: 'expense', isDefault: true, isSystem: true },
      ];

      mockDb.insert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue(mockCreatedCategories),
        }),
      });

      const result = await createDefaultCategories(mockUserId);

      expect(result).toHaveLength(4);
      expect(result[0]).toMatchObject({
        name: 'Salary',
        type: 'income',
        isDefault: true,
        isSystem: true,
      });
      expect(mockLogger.info).toHaveBeenCalledWith(
        `Created 4 default categories for user: ${mockUserId}`
      );
    });

    it('should throw DatabaseError when db is not available', async () => {
      const originalDb = mockDb;
      (db as any) = null;

      await expect(createDefaultCategories(mockUserId)).rejects.toThrow(
        new DatabaseError('Database connection not available')
      );

      (db as any) = originalDb;
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      mockDb.insert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockRejectedValue(dbError),
        }),
      });

      await expect(createDefaultCategories(mockUserId)).rejects.toThrow(
        'Error creating default categories'
      );
      expect(mockLogger.error).toHaveBeenCalledWith('Error creating default categories:', dbError);
    });
  });

  describe('getUserCategories', () => {
    it('should get user categories successfully', async () => {
      const mockCategories = [mockCategory];
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue(mockCategories),
        },
      };

      const result = await getUserCategories(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: mockCategoryId,
        name: 'Test Category',
        type: 'expense',
      });
    });

    it('should filter by type', async () => {
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([mockCategory]),
        },
      };

      await getUserCategories(mockUserId, { type: 'expense' } as any);

      expect(mockDb.query.categories.findMany).toHaveBeenCalledWith({
        where: and(eq(categories.userId, mockUserId), eq(categories.type, 'expense'), eq(categories.isArchived, false)),
        orderBy: [categories.displayOrder, categories.createdAt],
      });
    });

    it('should filter by parentId', async () => {
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([mockCategory]),
        },
      };

      await getUserCategories(mockUserId, { parentId: mockParentId } as any);

      expect(mockDb.query.categories.findMany).toHaveBeenCalledWith({
        where: and(eq(categories.userId, mockUserId), eq(categories.parentId, mockParentId), eq(categories.isArchived, false)),
        orderBy: [categories.displayOrder, categories.createdAt],
      });
    });

    it('should filter by null parentId', async () => {
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([mockCategory]),
        },
      };

      await getUserCategories(mockUserId, { parentId: null } as any);

      expect(mockDb.query.categories.findMany).toHaveBeenCalledWith({
        where: and(eq(categories.userId, mockUserId), isNull(categories.parentId), eq(categories.isArchived, false)),
        orderBy: [categories.displayOrder, categories.createdAt],
      });
    });

    it('should include archived categories when requested', async () => {
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([mockCategory]),
        },
      };

      await getUserCategories(mockUserId, { includeArchived: true } as any);

      expect(mockDb.query.categories.findMany).toHaveBeenCalledWith({
        where: and(eq(categories.userId, mockUserId)),
        orderBy: [categories.displayOrder, categories.createdAt],
      });
    });

    it('should exclude system categories when requested', async () => {
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([mockCategory]),
        },
      };

      await getUserCategories(mockUserId, { includeSystem: false } as any);

      expect(mockDb.query.categories.findMany).toHaveBeenCalledWith({
        where: and(eq(categories.userId, mockUserId), eq(categories.isArchived, false), eq(categories.isSystem, false)),
        orderBy: [categories.displayOrder, categories.createdAt],
      });
    });

    it('should throw DatabaseError when db is not available', async () => {
      const originalDb = mockDb;
      (db as any) = null;

      await expect(getUserCategories(mockUserId)).rejects.toThrow(
        new DatabaseError('Database connection not available')
      );

      (db as any) = originalDb;
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockRejectedValue(dbError),
        },
      };

      await expect(getUserCategories(mockUserId)).rejects.toThrow(
        'Error fetching categories'
      );
      expect(mockLogger.error).toHaveBeenCalledWith('Error fetching user categories:', dbError);
    });
  });

  describe('getCategoryTree', () => {
    it('should build hierarchical category tree', async () => {
      const parentCategory = { ...mockCategory, id: 'parent-1', name: 'Parent', displayOrder: 1 };
      const childCategory = { ...mockCategory, id: 'child-1', name: 'Child', parentId: 'parent-1', displayOrder: 2 };
      
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([parentCategory, childCategory]),
        },
      };

      const result = await getCategoryTree(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'parent-1',
        name: 'Parent',
        children: [
          {
            id: 'child-1',
            name: 'Child',
            children: [],
          },
        ],
      });
    });

    it('should sort categories by display order', async () => {
      const category1 = { ...mockCategory, id: 'cat-1', name: 'Category 1', displayOrder: 2 };
      const category2 = { ...mockCategory, id: 'cat-2', name: 'Category 2', displayOrder: 1 };
      
      mockDb.query = {
        categories: {
          findMany: vi.fn().mockResolvedValue([category1, category2]),
        },
      };

      const result = await getCategoryTree(mockUserId);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Category 2'); // Lower display order first
      expect(result[1].name).toBe('Category 1');
    });
  });

  describe('getCategoryById', () => {
    it('should get category by id successfully', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
        },
      };

      const result = await getCategoryById(mockCategoryId, mockUserId);

      expect(result).toMatchObject({
        id: mockCategoryId,
        name: 'Test Category',
        type: 'expense',
      });
      expect(mockDb.query.categories.findFirst).toHaveBeenCalledWith({
        where: and(eq(categories.id, mockCategoryId), eq(categories.userId, mockUserId)),
      });
    });

    it('should return null when category not found', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(null),
        },
      };

      const result = await getCategoryById(mockCategoryId, mockUserId);

      expect(result).toBeNull();
    });

    it('should throw DatabaseError when db is not available', async () => {
      const originalDb = mockDb;
      (db as any) = null;

      await expect(getCategoryById(mockCategoryId, mockUserId)).rejects.toThrow(
        new DatabaseError('Database connection not available')
      );

      (db as any) = originalDb;
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockRejectedValue(dbError),
        },
      };

      await expect(getCategoryById(mockCategoryId, mockUserId)).rejects.toThrow(
        'Error fetching category'
      );
      expect(mockLogger.error).toHaveBeenCalledWith('Error fetching category:', dbError);
    });
  });

  describe('createCategory', () => {
    const createInput = {
      name: 'New Category',
      type: 'expense' as const,
      icon: 'new-icon',
      color: '#00FF00',
      displayOrder: 5,
    };

    it('should create category successfully', async () => {
      mockDb.insert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockCategory]),
        }),
      });

      const result = await createCategory(mockUserId, createInput);

      expect(result).toMatchObject({
        id: mockCategoryId,
        name: 'Test Category',
        type: 'expense',
      });
      expect(mockLogger.info).toHaveBeenCalledWith(
        `Category created: ${mockCategoryId} for user: ${mockUserId}`
      );
    });

    it('should create category with parent validation', async () => {
      const parentCategory = { ...mockCategory, id: mockParentId, type: 'expense' };
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(parentCategory),
        },
      };
      mockDb.insert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockCategory]),
        }),
      });

      const inputWithParent = { ...createInput, parentId: mockParentId };
      const result = await createCategory(mockUserId, inputWithParent);

      expect(result).toMatchObject({
        id: mockCategoryId,
        name: 'Test Category',
      });
    });

    it('should throw ValidationError when parent category not found', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(null),
        },
      };

      const inputWithParent = { ...createInput, parentId: mockParentId };
      await expect(createCategory(mockUserId, inputWithParent)).rejects.toThrow(
        new ValidationError('Parent category not found')
      );
    });

    it('should throw ValidationError when parent and child types differ', async () => {
      const parentCategory = { ...mockCategory, id: mockParentId, type: 'income' };
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(parentCategory),
        },
      };

      const inputWithParent = { ...createInput, parentId: mockParentId, type: 'expense' as const };
      await expect(createCategory(mockUserId, inputWithParent)).rejects.toThrow(
        new ValidationError('Parent and child categories must have the same type')
      );
    });

    it('should throw DatabaseError when db is not available', async () => {
      const originalDb = mockDb;
      (db as any) = null;

      await expect(createCategory(mockUserId, createInput)).rejects.toThrow(
        new DatabaseError('Database connection not available')
      );

      (db as any) = originalDb;
    });

    it('should throw DatabaseError when creation fails', async () => {
      mockDb.insert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([]),
        }),
      });

      await expect(createCategory(mockUserId, createInput)).rejects.toThrow(
        'Error creating category'
      );
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      mockDb.insert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockRejectedValue(dbError),
        }),
      });

      await expect(createCategory(mockUserId, createInput)).rejects.toThrow(
        'Error creating category'
      );
      expect(mockLogger.error).toHaveBeenCalledWith('Error creating category:', dbError);
    });
  });

  describe('updateCategory', () => {
    const updateInput = {
      name: 'Updated Category',
      color: '#0000FF',
      displayOrder: 10,
    };

    it('should update category successfully', async () => {
      const updatedCategory = { ...mockCategory, ...updateInput };
      
      // Mock getCategoryById call
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
        },
      };
      
      // Mock update call
      mockDb.update = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([updatedCategory]),
          }),
        }),
      });

      const result = await updateCategory(mockCategoryId, mockUserId, updateInput);

      expect(result).toMatchObject({
        name: 'Updated Category',
        color: '#0000FF',
        displayOrder: 10,
      });
      expect(mockLogger.info).toHaveBeenCalledWith(
        `Category updated: ${mockCategoryId} for user: ${mockUserId}`
      );
    });

    it('should throw RecordNotFoundError when category not found', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(null),
        },
      };

      await expect(updateCategory(mockCategoryId, mockUserId, updateInput)).rejects.toThrow(
        new RecordNotFoundError('Category not found')
      );
    });

    it('should throw ValidationError when trying to update system category', async () => {
      const systemCategory = { ...mockCategory, isSystem: true };
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(systemCategory),
        },
      };

      await expect(updateCategory(mockCategoryId, mockUserId, updateInput)).rejects.toThrow(
        new ValidationError('System categories cannot be modified')
      );
    });

    it('should throw ValidationError when setting category as its own parent', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
        },
      };

      const inputWithSelfParent = { ...updateInput, parentId: mockCategoryId };
      await expect(updateCategory(mockCategoryId, mockUserId, inputWithSelfParent)).rejects.toThrow(
        new ValidationError('Category cannot be its own parent')
      );
    });

    it('should throw ValidationError when parent category not found', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn()
            .mockResolvedValueOnce(mockCategory) // First call for existing category
            .mockResolvedValueOnce(null), // Second call for parent category
        },
      };

      const inputWithParent = { ...updateInput, parentId: mockParentId };
      await expect(updateCategory(mockCategoryId, mockUserId, inputWithParent)).rejects.toThrow(
        new ValidationError('Parent category not found')
      );
    });

    it('should throw ValidationError when parent and child types differ', async () => {
      const parentCategory = { ...mockCategory, id: mockParentId, type: 'income' };
      mockDb.query = {
        categories: {
          findFirst: vi.fn()
            .mockResolvedValueOnce(mockCategory) // Existing category (expense)
            .mockResolvedValueOnce(parentCategory), // Parent category (income)
        },
      };

      const inputWithParent = { ...updateInput, parentId: mockParentId, type: 'expense' as const };
      await expect(updateCategory(mockCategoryId, mockUserId, inputWithParent)).rejects.toThrow(
        new ValidationError('Parent and child categories must have the same type')
      );
    });

    it('should throw DatabaseError when db is not available', async () => {
      const originalDb = mockDb;
      (db as any) = null;

      await expect(updateCategory(mockCategoryId, mockUserId, updateInput)).rejects.toThrow(
        new DatabaseError('Database connection not available')
      );

      (db as any) = originalDb;
    });

    it('should throw DatabaseError when update fails', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
        },
      };
      mockDb.update = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([]),
          }),
        }),
      });

      await expect(updateCategory(mockCategoryId, mockUserId, updateInput)).rejects.toThrow(
        'Error updating category'
      );
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
        },
      };
      mockDb.update = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockRejectedValue(dbError),
          }),
        }),
      });

      await expect(updateCategory(mockCategoryId, mockUserId, updateInput)).rejects.toThrow(
        'Error updating category'
      );
      expect(mockLogger.error).toHaveBeenCalledWith('Error updating category:', dbError);
    });
  });

  describe('deleteCategory', () => {
    it('should delete category successfully (soft delete)', async () => {
      const archivedCategory = { ...mockCategory, isArchived: true };
      
      // Mock getCategoryById call
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
          findMany: vi.fn().mockResolvedValue([]), // No child categories
        },
      };
      
      // Mock update call for archiving
      mockDb.update = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([archivedCategory]),
          }),
        }),
      });

      const result = await deleteCategory(mockCategoryId, mockUserId);

      expect(result).toBe(true);
      expect(mockLogger.info).toHaveBeenCalledWith(
        `Category archived: ${mockCategoryId} for user: ${mockUserId}`
      );
    });

    it('should throw RecordNotFoundError when category not found', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(null),
        },
      };

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        new RecordNotFoundError('Category not found')
      );
    });

    it('should throw ValidationError when trying to delete system category', async () => {
      const systemCategory = { ...mockCategory, isSystem: true };
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(systemCategory),
        },
      };

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        new ValidationError('System categories cannot be deleted')
      );
    });

    it('should throw ValidationError when trying to delete default category', async () => {
      const defaultCategory = { ...mockCategory, isDefault: true };
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(defaultCategory),
        },
      };

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        new ValidationError('Default categories cannot be deleted')
      );
    });

    it('should throw ValidationError when category has children', async () => {
      const childCategory = { ...mockCategory, id: 'child-1', parentId: mockCategoryId };
      
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
          findMany: vi.fn().mockResolvedValue([childCategory]), // Has child categories
        },
      };

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        new ValidationError('Cannot delete category with subcategories. Archive or move subcategories first.')
      );
    });

    it('should throw DatabaseError when db is not available', async () => {
      const originalDb = mockDb;
      (db as any) = null;

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        new DatabaseError('Database connection not available')
      );

      (db as any) = originalDb;
    });

    it('should throw DatabaseError when archiving fails', async () => {
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
          findMany: vi.fn().mockResolvedValue([]), // No child categories
        },
      };
      mockDb.update = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([]),
          }),
        }),
      });

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        'Error deleting category'
      );
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      mockDb.query = {
        categories: {
          findFirst: vi.fn().mockResolvedValue(mockCategory),
          findMany: vi.fn().mockResolvedValue([]), // No child categories
        },
      };
      mockDb.update = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockRejectedValue(dbError),
          }),
        }),
      });

      await expect(deleteCategory(mockCategoryId, mockUserId)).rejects.toThrow(
        'Error deleting category'
      );
      expect(mockLogger.error).toHaveBeenCalledWith('Error deleting category:', dbError);
    });
  });
}); 