// Unit tests for accounts service
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock all dependencies before importing
vi.mock('../../../../src/database/client.js', () => {
  const mockDb = {
    query: {
      accounts: {
        findMany: vi.fn(),
        findFirst: vi.fn(),
      },
    },
    insert: vi.fn(),
    update: vi.fn(),
    values: vi.fn(),
    set: vi.fn(),
    where: vi.fn(),
    returning: vi.fn(),
  };

  // Setup database mock chain
  mockDb.insert.mockReturnValue({ values: mockDb.values });
  mockDb.values.mockReturnValue({ returning: mockDb.returning });
  mockDb.update.mockReturnValue({ set: mockDb.set });
  mockDb.set.mockReturnValue({ where: mockDb.where });
  mockDb.where.mockReturnValue({ returning: mockDb.returning });

  return {
    db: mockDb,
  };
});

vi.mock('drizzle-orm', () => ({
  and: vi.fn(),
  eq: vi.fn(),
}));

vi.mock('../../../../src/utils/logger.js', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

import { and, eq } from 'drizzle-orm';
import { db } from '../../../../src/database/client.js';
import { logger } from '../../../../src/utils/logger.js';

// Import the service under test
import {
  createAccount,
  deleteAccount,
  getAccountById,
  getUserAccounts,
  updateAccount,
} from '../../../../src/services/accounts/accounts.service.js';

import {
  DatabaseError,
  RecordNotFoundError,
} from '../../../../src/utils/error-handler.js';

import type { Account } from '../../../../src/database/types.js';
import type {
  CreateAccountInput,
  UpdateAccountInput,
} from '../../../../src/services/accounts/accounts.types.js';

describe('Accounts Service', () => {
  // Get reference to the mocked database
  const mockDb = db as any;

  const mockUserId = '********-0000-0000-0000-********0001';
  const mockAccountId = '********-0000-0000-0000-********0002';

  const mockAccount: Account = {
    id: mockAccountId,
    userId: mockUserId,
    name: 'Test Account',
    type: 'checking',
    currency: 'USD',
    initialBalance: '1000.00',
    currentBalance: '1000.00',
    notes: 'Test account notes',
    icon: 'bank',
    color: '#4285F4',
    includeInNetWorth: true,
    displayOrder: 1,
    isArchived: false,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock implementations
    vi.mocked(and).mockImplementation((...conditions) => ({ conditions }) as any);
    vi.mocked(eq).mockImplementation((field, value) => ({ field, value }) as any);

    // Reset database mock chain
    mockDb.insert.mockReturnValue({ values: mockDb.values });
    mockDb.values.mockReturnValue({ returning: mockDb.returning });
    mockDb.update.mockReturnValue({ set: mockDb.set });
    mockDb.set.mockReturnValue({ where: mockDb.where });
    mockDb.where.mockReturnValue({ returning: mockDb.returning });
  });

  describe('getUserAccounts', () => {
    it('should return user accounts successfully', async () => {
      const mockAccounts = [mockAccount];
      mockDb.query.accounts.findMany.mockResolvedValue(mockAccounts);

      const result = await getUserAccounts(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: mockAccountId,
        name: 'Test Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: '1000.00',
        currentBalance: '1000.00',
      });

      expect(mockDb.query.accounts.findMany).toHaveBeenCalledWith({
        where: { conditions: expect.any(Array) },
        orderBy: expect.any(Array),
      });
    });

    it('should return empty array when user has no accounts', async () => {
      mockDb.query.accounts.findMany.mockResolvedValue([]);

      const result = await getUserAccounts(mockUserId);

      expect(result).toEqual([]);
      expect(mockDb.query.accounts.findMany).toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      mockDb.query.accounts.findMany.mockRejectedValue(new Error('Database error'));

      await expect(getUserAccounts(mockUserId)).rejects.toThrow(DatabaseError);
      await expect(getUserAccounts(mockUserId)).rejects.toThrow('Error fetching accounts');

      expect(logger.error).toHaveBeenCalledWith('Error fetching user accounts:', expect.any(Error));
    });
  });

  describe('getAccountById', () => {
    it('should return account when found', async () => {
      mockDb.query.accounts.findFirst.mockResolvedValue(mockAccount);

      const result = await getAccountById(mockAccountId, mockUserId);

      expect(result).toMatchObject({
        id: mockAccountId,
        name: 'Test Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: '1000.00',
        currentBalance: '1000.00',
      });

      expect(mockDb.query.accounts.findFirst).toHaveBeenCalledWith({
        where: { conditions: expect.any(Array) },
      });
    });

    it('should return null when account not found', async () => {
      mockDb.query.accounts.findFirst.mockResolvedValue(null);

      const result = await getAccountById(mockAccountId, mockUserId);

      expect(result).toBeNull();
      expect(mockDb.query.accounts.findFirst).toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      mockDb.query.accounts.findFirst.mockRejectedValue(new Error('Database error'));

      await expect(getAccountById(mockAccountId, mockUserId)).rejects.toThrow(DatabaseError);
      await expect(getAccountById(mockAccountId, mockUserId)).rejects.toThrow('Error fetching account');

      expect(logger.error).toHaveBeenCalledWith('Error fetching account:', expect.any(Error));
    });
  });

  describe('createAccount', () => {
    const createInput: CreateAccountInput = {
      name: 'New Account',
      type: 'savings',
      currency: 'USD',
      initialBalance: 500,
      notes: 'New account notes',
      icon: 'piggy-bank',
      color: '#34A853',
      includeInNetWorth: true,
      displayOrder: 2,
    };

    it('should create account successfully', async () => {
      const newAccount = {
        ...mockAccount,
        name: 'New Account',
        type: 'savings',
        initialBalance: '500.00',
        currentBalance: '500.00',
      };

      mockDb.returning.mockResolvedValue([newAccount]);

      const result = await createAccount(mockUserId, createInput);

      expect(result).toMatchObject({
        id: mockAccountId,
        name: 'New Account',
        type: 'savings',
        currency: 'USD',
        initialBalance: '500.00',
        currentBalance: '500.00',
      });

      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith({
        userId: mockUserId,
        name: 'New Account',
        type: 'savings',
        currency: 'USD',
        initialBalance: '500',
        currentBalance: '500',
        notes: 'New account notes',
        icon: 'piggy-bank',
        color: '#34A853',
        includeInNetWorth: true,
        displayOrder: 2,
        isArchived: false,
      });

      expect(logger.info).toHaveBeenCalledWith(
        `Account created: ${mockAccountId} for user: ${mockUserId}`
      );
    });

    it('should create account with default values', async () => {
      const minimalInput: CreateAccountInput = {
        name: 'Minimal Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: 0,
        includeInNetWorth: true,
        displayOrder: 0,
      };

      const newAccount = {
        ...mockAccount,
        name: 'Minimal Account',
        currency: 'USD',
        initialBalance: '0',
        currentBalance: '0',
        notes: null,
        icon: null,
        color: null,
        displayOrder: 0,
      };

      mockDb.returning.mockResolvedValue([newAccount]);

      const result = await createAccount(mockUserId, minimalInput);

      expect(result).toMatchObject({
        name: 'Minimal Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: '0',
        currentBalance: '0',
      });

      expect(mockDb.values).toHaveBeenCalledWith({
        userId: mockUserId,
        name: 'Minimal Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: '0',
        currentBalance: '0',
        notes: null,
        icon: null,
        color: null,
        includeInNetWorth: true,
        displayOrder: 0,
        isArchived: false,
      });
    });

    it('should throw DatabaseError when account creation fails', async () => {
      mockDb.returning.mockResolvedValue([]);

      await expect(createAccount(mockUserId, createInput)).rejects.toThrow(DatabaseError);
      await expect(createAccount(mockUserId, createInput)).rejects.toThrow('Error creating account');
    });

    it('should handle database errors gracefully', async () => {
      mockDb.returning.mockRejectedValue(new Error('Database error'));

      await expect(createAccount(mockUserId, createInput)).rejects.toThrow(DatabaseError);
      await expect(createAccount(mockUserId, createInput)).rejects.toThrow('Error creating account');

      expect(logger.error).toHaveBeenCalledWith('Error creating account:', expect.any(Error));
    });
  });

  describe('updateAccount', () => {
    const updateInput: UpdateAccountInput = {
      name: 'Updated Account',
      notes: 'Updated notes',
      color: '#FF5722',
      displayOrder: 5,
    };

    beforeEach(() => {
      // Mock getAccountById to return existing account
      mockDb.query.accounts.findFirst.mockResolvedValue(mockAccount);
    });

    it('should update account successfully', async () => {
      const updatedAccount = {
        ...mockAccount,
        name: 'Updated Account',
        notes: 'Updated notes',
        color: '#FF5722',
        displayOrder: 5,
        updatedAt: new Date(),
      };

      mockDb.returning.mockResolvedValue([updatedAccount]);

      const result = await updateAccount(mockAccountId, mockUserId, updateInput);

      expect(result).toMatchObject({
        id: mockAccountId,
        name: 'Updated Account',
        notes: 'Updated notes',
        color: '#FF5722',
        displayOrder: 5,
      });

      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.set).toHaveBeenCalledWith({
        name: 'Updated Account',
        notes: 'Updated notes',
        color: '#FF5722',
        displayOrder: 5,
        updatedAt: expect.any(Date),
      });

      expect(logger.info).toHaveBeenCalledWith(
        `Account updated: ${mockAccountId} for user: ${mockUserId}`
      );
    });

    it('should throw RecordNotFoundError when account does not exist', async () => {
      mockDb.query.accounts.findFirst.mockResolvedValue(null);

      await expect(updateAccount(mockAccountId, mockUserId, updateInput)).rejects.toThrow(RecordNotFoundError);
      await expect(updateAccount(mockAccountId, mockUserId, updateInput)).rejects.toThrow('Account not found');

      expect(mockDb.update).not.toHaveBeenCalled();
    });

    it('should throw DatabaseError when update fails', async () => {
      mockDb.returning.mockResolvedValue([]);

      await expect(updateAccount(mockAccountId, mockUserId, updateInput)).rejects.toThrow(DatabaseError);
      await expect(updateAccount(mockAccountId, mockUserId, updateInput)).rejects.toThrow('Error updating account');
    });

    it('should handle database errors gracefully', async () => {
      mockDb.returning.mockRejectedValue(new Error('Database error'));

      await expect(updateAccount(mockAccountId, mockUserId, updateInput)).rejects.toThrow(DatabaseError);
      await expect(updateAccount(mockAccountId, mockUserId, updateInput)).rejects.toThrow('Error updating account');

      expect(logger.error).toHaveBeenCalledWith('Error updating account:', expect.any(Error));
    });
  });

  describe('deleteAccount', () => {
    beforeEach(() => {
      // Mock getAccountById to return existing account
      mockDb.query.accounts.findFirst.mockResolvedValue(mockAccount);
    });

    it('should delete account successfully (soft delete)', async () => {
      const archivedAccount = {
        ...mockAccount,
        isArchived: true,
        updatedAt: new Date(),
      };

      mockDb.returning.mockResolvedValue([archivedAccount]);

      const result = await deleteAccount(mockAccountId, mockUserId);

      expect(result).toBe(true);

      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.set).toHaveBeenCalledWith({
        isArchived: true,
        updatedAt: expect.any(Date),
      });

      expect(logger.info).toHaveBeenCalledWith(
        `Account archived: ${mockAccountId} for user: ${mockUserId}`
      );
    });

    it('should throw RecordNotFoundError when account does not exist', async () => {
      mockDb.query.accounts.findFirst.mockResolvedValue(null);

      await expect(deleteAccount(mockAccountId, mockUserId)).rejects.toThrow(RecordNotFoundError);
      await expect(deleteAccount(mockAccountId, mockUserId)).rejects.toThrow('Account not found');

      expect(mockDb.update).not.toHaveBeenCalled();
    });

    it('should throw DatabaseError when delete fails', async () => {
      mockDb.returning.mockResolvedValue([]);

      await expect(deleteAccount(mockAccountId, mockUserId)).rejects.toThrow(DatabaseError);
      await expect(deleteAccount(mockAccountId, mockUserId)).rejects.toThrow('Error archiving account');
    });

    it('should handle database errors gracefully', async () => {
      mockDb.returning.mockRejectedValue(new Error('Database error'));

      await expect(deleteAccount(mockAccountId, mockUserId)).rejects.toThrow(DatabaseError);
      await expect(deleteAccount(mockAccountId, mockUserId)).rejects.toThrow('Error archiving account');

      expect(logger.error).toHaveBeenCalledWith('Error archiving account:', expect.any(Error));
    });
  });
}); 