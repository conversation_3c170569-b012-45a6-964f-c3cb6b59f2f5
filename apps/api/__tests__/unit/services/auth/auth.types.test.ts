// Unit tests for authentication types and validation schemas
import { describe, expect, it } from 'vitest';
import {
  type AuthResponse,
  type LoginInput,
  type RefreshTokenInput,
  type RegisterInput,
  loginInputSchema,
  refreshTokenInputSchema,
  registerInputSchema,
} from '../../../../src/services/auth/auth.types.js';

describe('Authentication Types', () => {
  describe('registerInputSchema', () => {
    it('should validate valid registration input', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Test',
        lastName: 'User',
      };

      const result = registerInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validInput);
      }
    });

    it('should validate registration input without optional fields', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = registerInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.email).toBe('<EMAIL>');
        expect(result.data.password).toBe('Password123!');
        expect(result.data.firstName).toBeUndefined();
        expect(result.data.lastName).toBeUndefined();
      }
    });

    it('should reject invalid email', () => {
      const invalidInput = {
        email: 'invalid-email',
        password: 'Password123!',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Invalid email address');
      }
    });

    it('should reject password shorter than 8 characters', () => {
      const invalidInput = {
        email: '<EMAIL>',
        password: 'Pass1!',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          'Password must be at least 8 characters'
        );
      }
    });

    it('should reject password without uppercase letter', () => {
      const invalidInput = {
        email: '<EMAIL>',
        password: 'password123!',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          'Password must contain at least one uppercase letter'
        );
      }
    });

    it('should reject password without lowercase letter', () => {
      const invalidInput = {
        email: '<EMAIL>',
        password: 'PASSWORD123!',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          'Password must contain at least one lowercase letter'
        );
      }
    });

    it('should reject password without number', () => {
      const invalidInput = {
        email: '<EMAIL>',
        password: 'Password!',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          'Password must contain at least one number'
        );
      }
    });

    it('should reject missing email', () => {
      const invalidInput = {
        password: 'Password123!',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some(issue => issue.path.includes('email'))
        ).toBe(true);
      }
    });

    it('should reject missing password', () => {
      const invalidInput = {
        email: '<EMAIL>',
      };

      const result = registerInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some(issue => issue.path.includes('password'))
        ).toBe(true);
      }
    });

    it('should accept valid complex password', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'MyVerySecurePassword123!@#',
      };

      const result = registerInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
    });

    it('should handle empty optional fields', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: '',
        lastName: '',
      };

      const result = registerInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.firstName).toBe('');
        expect(result.data.lastName).toBe('');
      }
    });
  });

  describe('loginInputSchema', () => {
    it('should validate valid login input', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = loginInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validInput);
      }
    });

    it('should reject invalid email', () => {
      const invalidInput = {
        email: 'invalid-email',
        password: 'password123',
      };

      const result = loginInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Invalid email address');
      }
    });

    it('should reject empty password', () => {
      const invalidInput = {
        email: '<EMAIL>',
        password: '',
      };

      const result = loginInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Password is required');
      }
    });

    it('should reject missing email', () => {
      const invalidInput = {
        password: 'password123',
      };

      const result = loginInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some(issue => issue.path.includes('email'))
        ).toBe(true);
      }
    });

    it('should reject missing password', () => {
      const invalidInput = {
        email: '<EMAIL>',
      };

      const result = loginInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some(issue => issue.path.includes('password'))
        ).toBe(true);
      }
    });

    it('should accept any non-empty password for login', () => {
      const validInput = {
        email: '<EMAIL>',
        password: 'weak', // Login validation is less strict than registration
      };

      const result = loginInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
    });
  });

  describe('refreshTokenInputSchema', () => {
    it('should validate valid refresh token input', () => {
      const validInput = {
        refreshToken: 'valid-refresh-token-string',
      };

      const result = refreshTokenInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validInput);
      }
    });

    it('should reject empty refresh token', () => {
      const invalidInput = {
        refreshToken: '',
      };

      const result = refreshTokenInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          'Refresh token is required'
        );
      }
    });

    it('should reject missing refresh token', () => {
      const invalidInput = {};

      const result = refreshTokenInputSchema.safeParse(invalidInput);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some(issue => issue.path.includes('refreshToken'))
        ).toBe(true);
      }
    });

    it('should accept long refresh token', () => {
      const validInput = {
        refreshToken: 'a'.repeat(1000), // Very long token
      };

      const result = refreshTokenInputSchema.safeParse(validInput);

      expect(result.success).toBe(true);
    });
  });

  describe('Type inference', () => {
    it('should infer RegisterInput type correctly', () => {
      const registerInput: RegisterInput = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Test',
        lastName: 'User',
      };

      expect(registerInput.email).toBe('<EMAIL>');
      expect(registerInput.password).toBe('Password123!');
      expect(registerInput.firstName).toBe('Test');
      expect(registerInput.lastName).toBe('User');
    });

    it('should infer LoginInput type correctly', () => {
      const loginInput: LoginInput = {
        email: '<EMAIL>',
        password: 'password123',
      };

      expect(loginInput.email).toBe('<EMAIL>');
      expect(loginInput.password).toBe('password123');
    });

    it('should infer RefreshTokenInput type correctly', () => {
      const refreshTokenInput: RefreshTokenInput = {
        refreshToken: 'refresh-token-string',
      };

      expect(refreshTokenInput.refreshToken).toBe('refresh-token-string');
    });

    it('should define AuthResponse interface correctly', () => {
      const authResponse: AuthResponse = {
        token: 'jwt-token-string',
        user: {
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: 'user',
          emailVerified: true,
        },
      };

      expect(authResponse.token).toBe('jwt-token-string');
      expect(authResponse.user.id).toBe('00000000-0000-0000-0000-000000000001');
      expect(authResponse.user.email).toBe('<EMAIL>');
      expect(authResponse.user.firstName).toBe('Test');
      expect(authResponse.user.lastName).toBe('User');
      expect(authResponse.user.role).toBe('user');
      expect(authResponse.user.emailVerified).toBe(true);
    });

    it('should handle optional fields in AuthResponse', () => {
      const authResponse: AuthResponse = {
        token: 'jwt-token-string',
        user: {
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          firstName: null,
          lastName: null,
          role: 'user',
          emailVerified: false,
        },
      };

      expect(authResponse.user.firstName).toBeNull();
      expect(authResponse.user.lastName).toBeNull();
      expect(authResponse.user.emailVerified).toBe(false);
    });
  });
});
