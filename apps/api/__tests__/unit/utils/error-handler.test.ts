import { GraphQLError } from 'graphql';
// Unit tests for error handling utilities
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ZodError, type ZodIssue } from 'zod';

// Mock the logger
vi.mock('../../../src/utils/logger.js', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

import { logger } from '../../../src/utils/logger.js';

// Import the error handling utilities
import {
  AppError,
  AuthenticationError,
  DatabaseError,
  EmailAlreadyExistsError,
  ErrorCode,
  InvalidCredentialsError,
  InvalidTokenError,
  OAuthError,
  RecordNotFoundError,
  TokenExpiredError,
  UserNotFoundError,
  ValidationError,
  WeakPasswordError,
  errorFormatter,
  formatZodError,
  handleErrors,
} from '../../../src/utils/error-handler.js';

describe('Error Handler Utilities', () => {
  const originalEnv = process.env.NODE_ENV;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  describe('ErrorCode enum', () => {
    it('should have all expected error codes', () => {
      expect(ErrorCode.INVALID_CREDENTIALS).toBe('INVALID_CREDENTIALS');
      expect(ErrorCode.UNAUTHORIZED).toBe('UNAUTHORIZED');
      expect(ErrorCode.TOKEN_EXPIRED).toBe('TOKEN_EXPIRED');
      expect(ErrorCode.INVALID_TOKEN).toBe('INVALID_TOKEN');
      expect(ErrorCode.USER_NOT_FOUND).toBe('USER_NOT_FOUND');
      expect(ErrorCode.EMAIL_ALREADY_EXISTS).toBe('EMAIL_ALREADY_EXISTS');
      expect(ErrorCode.WEAK_PASSWORD).toBe('WEAK_PASSWORD');
      expect(ErrorCode.OAUTH_ERROR).toBe('OAUTH_ERROR');
      expect(ErrorCode.DATABASE_ERROR).toBe('DATABASE_ERROR');
      expect(ErrorCode.RECORD_NOT_FOUND).toBe('RECORD_NOT_FOUND');
      expect(ErrorCode.VALIDATION_ERROR).toBe('VALIDATION_ERROR');
      expect(ErrorCode.INTERNAL_SERVER_ERROR).toBe('INTERNAL_SERVER_ERROR');
      expect(ErrorCode.NETWORK_ERROR).toBe('NETWORK_ERROR');
      expect(ErrorCode.UNKNOWN_ERROR).toBe('UNKNOWN_ERROR');
    });
  });

  describe('AppError', () => {
    it('should create an AppError with all properties', () => {
      const details = { field: 'email', value: '<EMAIL>' };
      const error = new AppError(
        'Test error',
        ErrorCode.VALIDATION_ERROR,
        400,
        details
      );

      expect(error.message).toBe('Test error');
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual(details);
      expect(error.name).toBe('AppError');
      expect(error.stack).toBeDefined();
    });

    it('should create an AppError with default status code', () => {
      const error = new AppError('Test error', ErrorCode.INTERNAL_SERVER_ERROR);

      expect(error.statusCode).toBe(500);
      expect(error.details).toBeUndefined();
    });

    it('should convert to GraphQL error', () => {
      const details = { field: 'email' };
      const error = new AppError(
        'Test error',
        ErrorCode.VALIDATION_ERROR,
        400,
        details
      );
      const graphqlError = error.toGraphQLError();

      expect(graphqlError).toBeInstanceOf(GraphQLError);
      expect(graphqlError.message).toBe('Test error');
      expect(graphqlError.extensions).toEqual({
        code: ErrorCode.VALIDATION_ERROR,
        statusCode: 400,
        details,
      });
    });
  });

  describe('AuthenticationError', () => {
    it('should create an AuthenticationError with default values', () => {
      const error = new AuthenticationError('Auth failed');

      expect(error.message).toBe('Auth failed');
      expect(error.code).toBe(ErrorCode.UNAUTHORIZED);
      expect(error.statusCode).toBe(401);
      expect(error.name).toBe('AuthenticationError');
    });

    it('should create an AuthenticationError with custom code', () => {
      const error = new AuthenticationError(
        'Token expired',
        ErrorCode.TOKEN_EXPIRED
      );

      expect(error.code).toBe(ErrorCode.TOKEN_EXPIRED);
      expect(error.statusCode).toBe(401);
    });
  });

  describe('InvalidCredentialsError', () => {
    it('should create with default message', () => {
      const error = new InvalidCredentialsError();

      expect(error.message).toBe('Invalid email or password');
      expect(error.code).toBe(ErrorCode.INVALID_CREDENTIALS);
      expect(error.statusCode).toBe(401);
    });

    it('should create with custom message', () => {
      const error = new InvalidCredentialsError('Custom message');

      expect(error.message).toBe('Custom message');
      expect(error.code).toBe(ErrorCode.INVALID_CREDENTIALS);
    });
  });

  describe('TokenExpiredError', () => {
    it('should create with default message', () => {
      const error = new TokenExpiredError();

      expect(error.message).toBe('Authentication token has expired');
      expect(error.code).toBe(ErrorCode.TOKEN_EXPIRED);
      expect(error.statusCode).toBe(401);
    });

    it('should create with custom message and details', () => {
      const details = { expiredAt: '2023-01-01' };
      const error = new TokenExpiredError('Token expired at midnight', details);

      expect(error.message).toBe('Token expired at midnight');
      expect(error.details).toEqual(details);
    });
  });

  describe('InvalidTokenError', () => {
    it('should create with default message', () => {
      const error = new InvalidTokenError();

      expect(error.message).toBe('Invalid authentication token');
      expect(error.code).toBe(ErrorCode.INVALID_TOKEN);
      expect(error.statusCode).toBe(401);
    });
  });

  describe('UserNotFoundError', () => {
    it('should create with default message', () => {
      const error = new UserNotFoundError();

      expect(error.message).toBe('User not found');
      expect(error.code).toBe(ErrorCode.USER_NOT_FOUND);
      expect(error.statusCode).toBe(401);
    });
  });

  describe('EmailAlreadyExistsError', () => {
    it('should create with default message', () => {
      const error = new EmailAlreadyExistsError();

      expect(error.message).toBe('Email already exists');
      expect(error.code).toBe(ErrorCode.EMAIL_ALREADY_EXISTS);
      expect(error.statusCode).toBe(409);
    });
  });

  describe('WeakPasswordError', () => {
    it('should create with default message', () => {
      const error = new WeakPasswordError();

      expect(error.message).toBe(
        'Password does not meet security requirements'
      );
      expect(error.code).toBe(ErrorCode.WEAK_PASSWORD);
      expect(error.statusCode).toBe(400);
    });
  });

  describe('OAuthError', () => {
    it('should create with default message', () => {
      const error = new OAuthError();

      expect(error.message).toBe('OAuth authentication failed');
      expect(error.code).toBe(ErrorCode.OAUTH_ERROR);
      expect(error.statusCode).toBe(401);
    });
  });

  describe('DatabaseError', () => {
    it('should create with default message', () => {
      const error = new DatabaseError();

      expect(error.message).toBe('Database operation failed');
      expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
      expect(error.statusCode).toBe(500);
    });
  });

  describe('RecordNotFoundError', () => {
    it('should create with default message', () => {
      const error = new RecordNotFoundError();

      expect(error.message).toBe('Record not found');
      expect(error.code).toBe(ErrorCode.RECORD_NOT_FOUND);
      expect(error.statusCode).toBe(404);
    });
  });

  describe('ValidationError', () => {
    it('should create with default message', () => {
      const error = new ValidationError();

      expect(error.message).toBe('Validation failed');
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.statusCode).toBe(400);
    });
  });

  describe('formatZodError', () => {
    it('should format Zod validation errors', () => {
      const zodIssues: ZodIssue[] = [
        {
          code: 'invalid_type',
          expected: 'string',
          received: 'number',
          path: ['email'],
          message: 'Expected string, received number',
        },
        {
          code: 'too_small',
          minimum: 8,
          type: 'string',
          inclusive: true,
          exact: false,
          path: ['password'],
          message: 'String must contain at least 8 character(s)',
        },
      ];

      const zodError = new ZodError(zodIssues);
      const validationError = formatZodError(zodError);

      expect(validationError).toBeInstanceOf(ValidationError);
      expect(validationError.message).toBe('Validation failed');
      expect(validationError.details).toEqual({
        errors: [
          {
            path: 'email',
            message: 'Expected string, received number',
          },
          {
            path: 'password',
            message: 'String must contain at least 8 character(s)',
          },
        ],
      });
    });

    it('should handle nested path in Zod errors', () => {
      const zodIssues: ZodIssue[] = [
        {
          code: 'invalid_type',
          expected: 'string',
          received: 'undefined',
          path: ['user', 'profile', 'name'],
          message: 'Required',
        },
      ];

      const zodError = new ZodError(zodIssues);
      const validationError = formatZodError(zodError);

      expect(validationError.details).toEqual({
        errors: [
          {
            path: 'user.profile.name',
            message: 'Required',
          },
        ],
      });
    });
  });

  describe('errorFormatter', () => {
    it('should format AppError correctly', () => {
      const appError = new ValidationError('Invalid input', { field: 'email' });
      const execution = {
        data: null,
        errors: [
          {
            originalError: appError,
            message: 'Invalid input',
          },
        ],
      };

      const result = errorFormatter(execution, {});

      expect(result).toEqual({
        statusCode: 400,
        response: {
          data: null,
          errors: [
            {
              message: 'Invalid input',
              extensions: {
                code: ErrorCode.VALIDATION_ERROR,
                statusCode: 400,
                details: { field: 'email' },
              },
            },
          ],
        },
      });

      expect(logger.error).toHaveBeenCalledWith('GraphQL Error:', appError);
    });

    it('should format ZodError correctly', () => {
      const zodIssues: ZodIssue[] = [
        {
          code: 'invalid_type',
          expected: 'string',
          received: 'number',
          path: ['email'],
          message: 'Expected string, received number',
        },
      ];
      const zodError = new ZodError(zodIssues);
      const execution = {
        data: null,
        errors: [
          {
            originalError: zodError,
            message: 'Validation failed',
          },
        ],
      };

      const result = errorFormatter(execution, {});

      expect(result.statusCode).toBe(400);
      expect(result.response.errors[0].message).toBe('Validation failed');
      expect(result.response.errors[0].extensions.code).toBe(
        ErrorCode.VALIDATION_ERROR
      );
    });

    it('should handle unknown errors in development', () => {
      process.env.NODE_ENV = 'development';
      const execution = {
        data: null,
        errors: [
          {
            message: 'Unknown error occurred',
            extensions: { code: 'UNKNOWN_CODE' },
          },
        ],
      };

      const result = errorFormatter(execution, {});

      expect(result).toEqual({
        statusCode: 500,
        response: {
          data: null,
          errors: [
            {
              message: 'Unknown error occurred',
              extensions: {
                code: 'UNKNOWN_CODE',
                statusCode: 500,
              },
            },
          ],
        },
      });
    });

    it('should mask unknown errors in production', () => {
      process.env.NODE_ENV = 'production';
      const execution = {
        data: null,
        errors: [
          {
            message: 'Sensitive error information',
            extensions: { code: 'UNKNOWN_CODE' },
          },
        ],
      };

      const result = errorFormatter(execution, {});

      expect(result.response.errors[0].message).toBe(
        'An unexpected error occurred'
      );
    });

    it('should not mask known errors in production', () => {
      process.env.NODE_ENV = 'production';
      const execution = {
        data: null,
        errors: [
          {
            message: 'Invalid credentials',
            extensions: { code: ErrorCode.INVALID_CREDENTIALS },
          },
        ],
      };

      const result = errorFormatter(execution, {});

      expect(result.response.errors[0].message).toBe('Invalid credentials');
    });

    it('should handle errors without extensions', () => {
      const execution = {
        data: { user: null },
        errors: [
          {
            message: 'Generic error',
          },
        ],
      };

      const result = errorFormatter(execution, {});

      expect(result).toEqual({
        statusCode: 500,
        response: {
          data: { user: null },
          errors: [
            {
              message: 'Generic error',
              extensions: {
                code: ErrorCode.UNKNOWN_ERROR,
                statusCode: 500,
              },
            },
          ],
        },
      });
    });
  });

  describe('handleErrors', () => {
    it('should handle empty errors array', () => {
      const result = handleErrors([], {}, {});

      expect(result).toEqual({
        statusCode: 500,
        response: {
          errors: [
            {
              message: 'Unknown error',
              extensions: {
                code: ErrorCode.UNKNOWN_ERROR,
                statusCode: 500,
              },
            },
          ],
        },
      });
    });

    it('should handle AppError in originalError', () => {
      const appError = new InvalidCredentialsError('Wrong password');
      const errors = [
        {
          message: 'Wrong password',
          originalError: appError,
        },
      ];

      const result = handleErrors(errors, {}, {});

      expect(result).toEqual({
        statusCode: 401,
        response: {
          errors: [
            {
              message: 'Wrong password',
              extensions: {
                code: ErrorCode.INVALID_CREDENTIALS,
                statusCode: 401,
                details: undefined,
              },
            },
          ],
        },
      });

      expect(logger.error).toHaveBeenCalledWith('GraphQL Error:', errors[0]);
    });

    it('should handle generic errors in development', () => {
      process.env.NODE_ENV = 'development';
      const errors = [
        {
          message: 'Some error',
          extensions: { code: 'CUSTOM_ERROR' },
        },
      ];

      const result = handleErrors(errors, {}, {});

      expect(result.response.errors[0].message).toBe('Some error');
      expect(result.response.errors[0].extensions.code).toBe('CUSTOM_ERROR');
    });

    it('should mask generic errors in production', () => {
      process.env.NODE_ENV = 'production';
      const errors = [
        {
          message: 'Sensitive information',
          extensions: { code: 'CUSTOM_ERROR' },
        },
      ];

      const result = handleErrors(errors, {}, {});

      expect(result.response.errors[0].message).toBe(
        'An unexpected error occurred'
      );
    });

    it('should handle errors without extensions', () => {
      const errors = [
        {
          message: 'Basic error',
        },
      ];

      const result = handleErrors(errors, {}, {});

      expect(result.statusCode).toBe(500);
      expect(result.response.errors[0].extensions.code).toBe(
        ErrorCode.UNKNOWN_ERROR
      );
    });
  });
});
