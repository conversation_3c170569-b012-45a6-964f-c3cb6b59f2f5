import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';

// <PERSON><PERSON> winston before importing logger
vi.mock('winston', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  })),
  format: {
    combine: vi.fn(),
    timestamp: vi.fn(),
    errors: vi.fn(),
    json: vi.fn(),
    colorize: vi.fn(),
    simple: vi.fn(),
  },
  transports: {
    Console: vi.fn(),
    File: vi.fn(),
  },
}));

describe('Logger Edge Cases', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    vi.clearAllMocks();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Environment-based configuration', () => {
    it('should handle production environment', async () => {
      process.env.NODE_ENV = 'production';
      process.env.LOG_LEVEL = 'warn';

      // Re-import to get fresh instance with new env
      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.warn).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });

    it('should handle development environment', async () => {
      process.env.NODE_ENV = 'development';
      process.env.LOG_LEVEL = 'debug';

      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(logger).toBeDefined();
    });

    it('should handle test environment', async () => {
      process.env.NODE_ENV = 'test';
      process.env.LOG_LEVEL = 'error';

      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(logger).toBeDefined();
    });

    it('should handle missing LOG_LEVEL', async () => {
      process.env.NODE_ENV = 'production';
      delete process.env.LOG_LEVEL;

      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(logger).toBeDefined();
    });

    it('should handle invalid LOG_LEVEL', async () => {
      process.env.NODE_ENV = 'production';
      process.env.LOG_LEVEL = 'invalid-level';

      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(logger).toBeDefined();
    });
  });

  describe('Logger methods', () => {
    it('should expose all required logging methods', async () => {
      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(logger.info).toBeDefined();
      expect(logger.warn).toBeDefined();
      expect(logger.error).toBeDefined();
      expect(logger.debug).toBeDefined();
      
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.warn).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });

    it('should handle logging with different data types', async () => {
      const { logger } = await import('../../../src/utils/logger.js');
      
      // These should not throw
      expect(() => logger.info('string message')).not.toThrow();
      expect(() => logger.info('message', { key: 'value' })).not.toThrow();
      expect(() => logger.error('error', new Error('test error'))).not.toThrow();
      expect(() => logger.debug('debug', { complex: { nested: 'object' } })).not.toThrow();
    });

    it('should handle undefined and null values', async () => {
      const { logger } = await import('../../../src/utils/logger.js');
      
      expect(() => logger.info(undefined as any)).not.toThrow();
      expect(() => logger.info(null as any)).not.toThrow();
      expect(() => logger.warn('message', undefined)).not.toThrow();
      expect(() => logger.error('message', null)).not.toThrow();
    });
  });

  describe('Winston configuration', () => {
    it('should import logger without errors', async () => {
      // Import logger to trigger winston setup
      const loggerModule = await import('../../../src/utils/logger.js');
      
      expect(loggerModule.logger).toBeDefined();
    });

    it('should create logger with proper configuration', async () => {
      await import('../../../src/utils/logger.js');
      
      // Verify that winston mocks were called (mocked at top of file)
      expect(vi.isMockFunction).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('should handle logger import gracefully', async () => {
      // Should not throw when importing
      await expect(import('../../../src/utils/logger.js')).resolves.toBeDefined();
    });

    it('should provide fallback logger functionality', async () => {
      const { logger } = await import('../../../src/utils/logger.js');
      
      // Should have basic logging methods even if winston fails
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
    });
  });

  describe('Module exports', () => {
    it('should export logger as named export', async () => {
      const loggerModule = await import('../../../src/utils/logger.js');
      
      expect(loggerModule.logger).toBeDefined();
      expect(typeof loggerModule.logger).toBe('object');
    });

    it('should have consistent logger interface', async () => {
      const { logger } = await import('../../../src/utils/logger.js');
      
      const requiredMethods = ['info', 'warn', 'error', 'debug'] as const;
      
      requiredMethods.forEach(method => {
        expect(logger).toHaveProperty(method);
        expect(typeof (logger as any)[method]).toBe('function');
      });
    });
  });
}); 