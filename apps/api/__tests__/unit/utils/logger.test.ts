// Unit tests for logger utility
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock console methods
const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => {});
const mockConsoleError = vi
  .spyOn(console, 'error')
  .mockImplementation(() => {});
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {});
const mockConsoleDebug = vi
  .spyOn(console, 'debug')
  .mockImplementation(() => {});

// Import the logger
import { logger } from '../../../src/utils/logger.js';

describe('Logger Utility', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('info', () => {
    it('should log info messages with prefix', () => {
      logger.info('Test info message');

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] Test info message');
    });

    it('should log info messages with additional arguments', () => {
      const data = { user: 'test', id: 123 };
      logger.info('User data:', data, 'extra info');

      expect(mockConsoleLog).toHaveBeenCalledWith(
        '[INFO] User data:',
        data,
        'extra info'
      );
    });

    it('should handle empty message', () => {
      logger.info('');

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] ');
    });

    it('should handle multiple arguments', () => {
      logger.info('Message', 1, 2, 3, { test: true });

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] Message', 1, 2, 3, {
        test: true,
      });
    });
  });

  describe('error', () => {
    it('should log error messages with prefix', () => {
      logger.error('Test error message');

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[ERROR] Test error message'
      );
    });

    it('should log Error objects', () => {
      const error = new Error('Something went wrong');
      logger.error(error);

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[ERROR] Error: Something went wrong'
      );
    });

    it('should log error messages with additional arguments', () => {
      const errorData = { code: 500, details: 'Server error' };
      logger.error('Server failed:', errorData);

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[ERROR] Server failed:',
        errorData
      );
    });

    it('should handle Error object with additional arguments', () => {
      const error = new Error('Database connection failed');
      const context = { database: 'users', operation: 'select' };
      logger.error(error, context);

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[ERROR] Error: Database connection failed',
        context
      );
    });

    it('should handle custom error objects', () => {
      const customError = {
        name: 'CustomError',
        message: 'Custom error occurred',
        code: 'CUSTOM_001',
      };
      logger.error(customError);

      expect(mockConsoleError).toHaveBeenCalledWith('[ERROR] [object Object]');
    });
  });

  describe('warn', () => {
    it('should log warning messages with prefix', () => {
      logger.warn('Test warning message');

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARN] Test warning message'
      );
    });

    it('should log warning messages with additional arguments', () => {
      const warningData = { deprecated: true, version: '1.0.0' };
      logger.warn('Deprecated feature used:', warningData);

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARN] Deprecated feature used:',
        warningData
      );
    });

    it('should handle multiple warning arguments', () => {
      logger.warn('Warning:', 'feature', 'will be removed in', 'v2.0');

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARN] Warning:',
        'feature',
        'will be removed in',
        'v2.0'
      );
    });
  });

  describe('debug', () => {
    it('should log debug messages with prefix', () => {
      logger.debug('Test debug message');

      expect(mockConsoleDebug).toHaveBeenCalledWith(
        '[DEBUG] Test debug message'
      );
    });

    it('should log debug messages with additional arguments', () => {
      const debugData = {
        function: 'processUser',
        input: { id: 123 },
        timestamp: '2023-01-01T00:00:00Z',
      };
      logger.debug('Processing user:', debugData);

      expect(mockConsoleDebug).toHaveBeenCalledWith(
        '[DEBUG] Processing user:',
        debugData
      );
    });

    it('should handle complex debug data', () => {
      const complexData = {
        request: { method: 'POST', url: '/api/users' },
        response: { status: 200, data: { success: true } },
        timing: { start: 1000, end: 1050, duration: 50 },
      };
      logger.debug('Request completed:', complexData);

      expect(mockConsoleDebug).toHaveBeenCalledWith(
        '[DEBUG] Request completed:',
        complexData
      );
    });
  });

  describe('logger interface', () => {
    it('should have all required methods', () => {
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.warn).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });

    it('should be callable without arguments', () => {
      expect(() => logger.info()).not.toThrow();
      expect(() => logger.error()).not.toThrow();
      expect(() => logger.warn()).not.toThrow();
      expect(() => logger.debug()).not.toThrow();

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] undefined');
      expect(mockConsoleError).toHaveBeenCalledWith('[ERROR] undefined');
      expect(mockConsoleWarn).toHaveBeenCalledWith('[WARN] undefined');
      expect(mockConsoleDebug).toHaveBeenCalledWith('[DEBUG] undefined');
    });

    it('should handle null and undefined values', () => {
      logger.info(null);
      logger.error(undefined);
      logger.warn(null, undefined);
      logger.debug(undefined, null);

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] null');
      expect(mockConsoleError).toHaveBeenCalledWith('[ERROR] undefined');
      expect(mockConsoleWarn).toHaveBeenCalledWith('[WARN] null', undefined);
      expect(mockConsoleDebug).toHaveBeenCalledWith('[DEBUG] undefined', null);
    });

    it('should handle boolean values', () => {
      logger.info(true);
      logger.error(false);
      logger.warn('Status:', true);
      logger.debug('Flag:', false);

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] true');
      expect(mockConsoleError).toHaveBeenCalledWith('[ERROR] false');
      expect(mockConsoleWarn).toHaveBeenCalledWith('[WARN] Status:', true);
      expect(mockConsoleDebug).toHaveBeenCalledWith('[DEBUG] Flag:', false);
    });

    it('should handle numeric values', () => {
      logger.info(42);
      logger.error(0);
      logger.warn(-1);
      logger.debug(3.5);

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] 42');
      expect(mockConsoleError).toHaveBeenCalledWith('[ERROR] 0');
      expect(mockConsoleWarn).toHaveBeenCalledWith('[WARN] -1');
      expect(mockConsoleDebug).toHaveBeenCalledWith('[DEBUG] 3.5');
    });

    it('should handle array values', () => {
      const array = [1, 2, 3, 'test'];
      logger.info('Array:', array);
      logger.error(['error', 'items']);

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] Array:', array);
      expect(mockConsoleError).toHaveBeenCalledWith('[ERROR] error,items');
    });

    it('should handle function values', () => {
      const testFunction = () => 'test';
      logger.info('Function:', testFunction);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        '[INFO] Function:',
        testFunction
      );
    });
  });

  describe('performance and edge cases', () => {
    it('should handle very long messages', () => {
      const longMessage = 'A'.repeat(10000);
      logger.info(longMessage);

      expect(mockConsoleLog).toHaveBeenCalledWith(`[INFO] ${longMessage}`);
    });

    it('should handle circular references in objects', () => {
      const circularObj: { name: string; self?: unknown } = { name: 'test' };
      circularObj.self = circularObj;

      // This should not throw an error
      expect(() => logger.info('Circular:', circularObj)).not.toThrow();
      expect(mockConsoleLog).toHaveBeenCalled();
    });

    it('should handle special characters and unicode', () => {
      const specialMessage = 'Special chars: 🚀 ñ ü ß 中文 العربية';
      logger.info(specialMessage);

      expect(mockConsoleLog).toHaveBeenCalledWith(`[INFO] ${specialMessage}`);
    });

    it('should handle Date objects', () => {
      const date = new Date('2023-01-01T00:00:00Z');
      logger.info('Date:', date);

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] Date:', date);
    });

    it('should handle RegExp objects', () => {
      const regex = /test\d+/gi;
      logger.info('Regex:', regex);

      expect(mockConsoleLog).toHaveBeenCalledWith('[INFO] Regex:', regex);
    });
  });
});
