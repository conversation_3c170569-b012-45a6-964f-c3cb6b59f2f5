import { describe, expect, it } from 'vitest';
import {
  AuthenticationError,
  DatabaseError,
  EmailAlreadyExistsError,
  InvalidCredentialsError,
  OAuthError,
  ValidationError,
  ErrorCode,
} from '../../../src/utils/error-handler.js';

describe('Error Handler Edge Cases', () => {
  describe('AuthenticationError', () => {
    it('should create error with custom message', () => {
      const error = new AuthenticationError('Custom auth error');
      expect(error.message).toBe('Custom auth error');
      expect(error.code).toBe(ErrorCode.UNAUTHORIZED);
      expect(error.statusCode).toBe(401);
    });

    it('should create error with custom code', () => {
      const error = new AuthenticationError('Token expired', ErrorCode.TOKEN_EXPIRED);
      expect(error.message).toBe('Token expired');
      expect(error.code).toBe(ErrorCode.TOKEN_EXPIRED);
      expect(error.statusCode).toBe(401);
    });

    it('should create error with details', () => {
      const details = { reason: 'token_expired' };
      const error = new AuthenticationError('Token expired', ErrorCode.TOKEN_EXPIRED, details);
      expect(error.message).toBe('Token expired');
      expect(error.details).toEqual(details);
    });
  });

  describe('DatabaseError', () => {
    it('should create error with default message', () => {
      const error = new DatabaseError();
      expect(error.message).toBe('Database operation failed');
      expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
      expect(error.statusCode).toBe(500);
    });

    it('should create error with custom message', () => {
      const error = new DatabaseError('Connection timeout');
      expect(error.message).toBe('Connection timeout');
      expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
      expect(error.statusCode).toBe(500);
    });
  });

  describe('EmailAlreadyExistsError', () => {
    it('should create error with default message', () => {
      const error = new EmailAlreadyExistsError();
      expect(error.message).toBe('Email already exists');
      expect(error.code).toBe(ErrorCode.EMAIL_ALREADY_EXISTS);
      expect(error.statusCode).toBe(409);
    });

    it('should create error with custom message', () => {
      const error = new EmailAlreadyExistsError('Email <EMAIL> is already registered');
      expect(error.message).toBe('Email <EMAIL> is already registered');
    });
  });

  describe('InvalidCredentialsError', () => {
    it('should create error with default message', () => {
      const error = new InvalidCredentialsError();
      expect(error.message).toBe('Invalid email or password');
      expect(error.code).toBe(ErrorCode.INVALID_CREDENTIALS);
      expect(error.statusCode).toBe(401);
    });

    it('should create error with custom message', () => {
      const error = new InvalidCredentialsError('Password is incorrect');
      expect(error.message).toBe('Password is incorrect');
    });
  });

  describe('OAuthError', () => {
    it('should create error with default message', () => {
      const error = new OAuthError();
      expect(error.message).toBe('OAuth authentication failed');
      expect(error.code).toBe(ErrorCode.OAUTH_ERROR);
      expect(error.statusCode).toBe(401);
    });

    it('should create error with provider', () => {
      const error = new OAuthError('OAuth authentication failed: google');
      expect(error.message).toBe('OAuth authentication failed: google');
    });

    it('should create error with details', () => {
      const details = { provider: 'google', error: 'invalid_grant' };
      const error = new OAuthError('OAuth failed', details);
      expect(error.details).toEqual(details);
    });
  });

  describe('ValidationError', () => {
    it('should create error with default message', () => {
      const error = new ValidationError();
      expect(error.message).toBe('Validation failed');
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.statusCode).toBe(400);
    });

    it('should create error with field details', () => {
      const details = { field: 'email', reason: 'invalid_format' };
      const error = new ValidationError('Invalid email format', details);
      expect(error.message).toBe('Invalid email format');
      expect(error.details).toEqual(details);
    });
  });

  describe('Error inheritance', () => {
    it('should be instances of Error', () => {
      const errors = [
        new AuthenticationError('Test'),
        new DatabaseError(),
        new EmailAlreadyExistsError(),
        new InvalidCredentialsError(),
        new OAuthError(),
        new ValidationError(),
      ];

      errors.forEach(error => {
        expect(error).toBeInstanceOf(Error);
        expect(error.name).toBeDefined();
        expect(error.message).toBeDefined();
        expect(error.stack).toBeDefined();
      });
    });

    it('should have correct error names', () => {
      expect(new AuthenticationError('Test').name).toBe('AuthenticationError');
      expect(new DatabaseError().name).toBe('DatabaseError');
      expect(new EmailAlreadyExistsError().name).toBe('EmailAlreadyExistsError');
      expect(new InvalidCredentialsError().name).toBe('InvalidCredentialsError');
      expect(new OAuthError().name).toBe('OAuthError');
      expect(new ValidationError().name).toBe('ValidationError');
    });
  });

  describe('Error serialization', () => {
    it('should serialize to JSON correctly', () => {
      const error = new ValidationError('Test error', { field: 'email' });
      const serialized = JSON.stringify(error);
      const parsed = JSON.parse(serialized);

      // Note: Error properties are not enumerable by default
      // This test verifies the structure exists
      expect(error.message).toBe('Test error');
      expect(error.details).toEqual({ field: 'email' });
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.statusCode).toBe(400);
    });

    it('should handle errors without details', () => {
      const error = new DatabaseError('Connection failed');
      expect(error.details).toBeUndefined();
      expect(error.message).toBe('Connection failed');
    });
  });

  describe('Error comparison', () => {
    it('should distinguish between different error types', () => {
      const authError = new AuthenticationError('Test');
      const dbError = new DatabaseError();
      const validationError = new ValidationError();

      expect(authError.code).not.toBe(dbError.code);
      expect(authError.statusCode).not.toBe(dbError.statusCode);
      expect(dbError.code).not.toBe(validationError.code);
    });

    it('should have consistent codes and status codes', () => {
      const errorMappings = [
        { error: new AuthenticationError('Test'), code: ErrorCode.UNAUTHORIZED, status: 401 },
        { error: new DatabaseError(), code: ErrorCode.DATABASE_ERROR, status: 500 },
        { error: new EmailAlreadyExistsError(), code: ErrorCode.EMAIL_ALREADY_EXISTS, status: 409 },
        { error: new InvalidCredentialsError(), code: ErrorCode.INVALID_CREDENTIALS, status: 401 },
        { error: new OAuthError(), code: ErrorCode.OAUTH_ERROR, status: 401 },
        { error: new ValidationError(), code: ErrorCode.VALIDATION_ERROR, status: 400 },
      ];

      errorMappings.forEach(({ error, code, status }) => {
        expect(error.code).toBe(code);
        expect(error.statusCode).toBe(status);
      });
    });
  });
}); 