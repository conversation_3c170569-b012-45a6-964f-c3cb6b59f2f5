import type { FastifyInstance } from 'fastify';
import supertest from 'supertest';
// E2E tests for server health and basic functionality
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { createServer } from '../../../src/server.js';
import {
  type TestGraphQLClient,
  createTestClientWithApp,
} from '../../../src/test-utils/gql-client.js';

describe('E2E Server Health Tests', () => {
  let app: FastifyInstance;
  let client: TestGraphQLClient;
  let request: supertest.SuperTest<supertest.Test>;

  beforeAll(async () => {
    // Create a real server instance for E2E testing
    app = await createServer();
    await app.ready();

    // Create GraphQL client and HTTP client
    client = createTestClientWithApp(app);
    request = supertest(app.server);
  });

  afterAll(async () => {
    // Clean up server
    if (app) {
      await app.close();
    }
  });

  describe('Server Health', () => {
    it('should respond to health check endpoint', async () => {
      const response = await request.get('/health').expect(200);

      expect(response.body).toEqual({ status: 'ok' });
    });

    it('should serve GraphQL endpoint', async () => {
      // Test basic GraphQL introspection query
      const introspectionQuery = `
        query IntrospectionQuery {
          __schema {
            queryType {
              name
            }
            mutationType {
              name
            }
          }
        }
      `;

      const response = await client.query(introspectionQuery);

      expect(response.errors).toBeUndefined();
      expect(response.data).toBeDefined();
      expect(response.data.__schema).toBeDefined();
      expect(response.data.__schema.queryType.name).toBe('Query');
      expect(response.data.__schema.mutationType.name).toBe('Mutation');
    });

    it.skip('should serve GraphiQL interface', async () => {
      const response = await request
        .get('/graphql')
        .set('Accept', 'text/html')
        .expect(200);

      // GraphiQL should return HTML content
      expect(response.headers['content-type']).toContain('text/html');
      expect(response.text).toContain('GraphiQL');
    });

    it('should serve Altair GraphQL client', async () => {
      const response = await request.get('/altair').expect(200);

      // Altair should return HTML content
      expect(response.headers['content-type']).toContain('text/html');
    });

    it('should handle CORS properly', async () => {
      const response = await request
        .options('/graphql')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type, Authorization')
        .expect(204);

      expect(response.headers['access-control-allow-origin']).toBe(
        'http://localhost:3000'
      );
      expect(response.headers['access-control-allow-methods']).toContain(
        'POST'
      );
      expect(response.headers['access-control-allow-headers']).toContain(
        'Content-Type'
      );
      expect(response.headers['access-control-allow-headers']).toContain(
        'Authorization'
      );
    });
  });

  describe('GraphQL Schema Validation', () => {
    it('should have authentication types available', async () => {
      const typeQuery = `
        query GetAuthTypes {
          __type(name: "User") {
            name
            fields {
              name
              type {
                name
              }
            }
          }
        }
      `;

      const response = await client.query(typeQuery);

      expect(response.errors).toBeUndefined();
      expect(response.data).toBeDefined();
      expect(response.data.__type).toBeDefined();
      expect(response.data.__type.name).toBe('User');

      // Check for essential User fields
      const fieldNames = response.data.__type.fields.map(
        (field: { name: string }) => field.name
      );
      expect(fieldNames).toContain('id');
      expect(fieldNames).toContain('email');
      expect(fieldNames).toContain('firstName');
      expect(fieldNames).toContain('lastName');
      expect(fieldNames).toContain('role');
    });

    it('should have authentication mutations available', async () => {
      const mutationQuery = `
        query GetMutationType {
          __type(name: "Mutation") {
            name
            fields {
              name
              args {
                name
                type {
                  name
                }
              }
            }
          }
        }
      `;

      const response = await client.query(mutationQuery);

      expect(response.errors).toBeUndefined();
      expect(response.data).toBeDefined();
      expect(response.data.__type).toBeDefined();
      expect(response.data.__type.name).toBe('Mutation');

      // Check for essential authentication mutations
      const fieldNames = response.data.__type.fields.map(
        (field: { name: string }) => field.name
      );
      expect(fieldNames).toContain('register');
      expect(fieldNames).toContain('login');
      expect(fieldNames).toContain('logout');
      expect(fieldNames).toContain('refreshToken');
    });

    it('should have authentication queries available', async () => {
      const queryQuery = `
        query GetQueryType {
          __type(name: "Query") {
            name
            fields {
              name
              type {
                name
              }
            }
          }
        }
      `;

      const response = await client.query(queryQuery);

      expect(response.errors).toBeUndefined();
      expect(response.data).toBeDefined();
      expect(response.data.__type).toBeDefined();
      expect(response.data.__type.name).toBe('Query');

      // Check for essential authentication queries
      const fieldNames = response.data.__type.fields.map(
        (field: { name: string }) => field.name
      );
      expect(fieldNames).toContain('me');
      expect(fieldNames).toContain('hello'); // Basic test query
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid GraphQL queries gracefully', async () => {
      const invalidQuery = `
        query InvalidQuery {
          nonExistentField {
            id
          }
        }
      `;

      const response = await client.query(invalidQuery);

      expect(response.errors).toBeDefined();
      expect(response.errors).toHaveLength(1);
      expect(response.errors![0].message).toContain(
        'Cannot query field "nonExistentField"'
      );
    });

    it('should handle malformed GraphQL requests', async () => {
      const response = await request
        .post('/graphql')
        .send({ query: 'invalid graphql syntax {{{' })
        .expect(400);

      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].message).toContain('Syntax Error');
    });

    it('should handle non-existent routes', async () => {
      const response = await request.get('/non-existent-route').expect(404);

      expect(response.body.statusCode).toBe(404);
    });
  });

  describe('Security Headers', () => {
    it('should not expose sensitive server information', async () => {
      const response = await request.get('/health').expect(200);

      // Should not expose server version or other sensitive info
      expect(response.headers['x-powered-by']).toBeUndefined();

      // Server header should either be undefined or not contain 'Express'
      const serverHeader = response.headers.server;
      if (serverHeader) {
        expect(serverHeader).not.toContain('Express'); // Should be Fastify
      }
    });

    it('should handle authentication headers properly', async () => {
      const response = await request
        .post('/graphql')
        .set('Authorization', 'Bearer invalid-token')
        .send({
          query: `
            query Me {
              me {
                id
                email
              }
            }
          `,
        })
        .expect(200);

      // Should not error on invalid token, just return null
      expect(response.body.errors).toBeUndefined();
      expect(response.body.data.me).toBeNull();
    });
  });
});
