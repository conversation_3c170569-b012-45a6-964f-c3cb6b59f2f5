import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
import type { FastifyInstance } from 'fastify';
import supertest from 'supertest';
// E2E tests for complete authentication lifecycle
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { userSettings, users } from '../../../src/database/schema.js';
import { verifyToken } from '../../../src/lib/auth/jwt.js';
import { createServer } from '../../../src/server.js';
import { withTransaction } from '../../../src/test-utils/db.js';
import {
  type TestGraphQLClient,
  createTestClientWithApp,
  gqlQueries,
} from '../../../src/test-utils/gql-client.js';

describe('E2E Authentication Lifecycle Tests', () => {
  let app: FastifyInstance;
  let client: TestGraphQLClient;
  let request: supertest.SuperTest<supertest.Test>;

  beforeAll(async () => {
    // Create a real server instance for E2E testing
    app = await createServer();
    await app.ready();

    // Create GraphQL client and HTTP client
    client = createTestClientWithApp(app);
    request = supertest(app.server);
  });

  afterAll(async () => {
    // Clean up server
    if (app) {
      await app.close();
    }
  });

  beforeEach(async () => {
    // Clear any authentication state before each test
    client.logout();
  });

  describe('Complete Authentication Lifecycle', () => {
    it('should complete full authentication lifecycle: register → login → access protected resource → logout', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();
        const userPassword = 'SecurePass123!';
        const userData = {
          email: userEmail,
          password: userPassword,
          firstName: 'E2E',
          lastName: 'User',
        };

        // Step 1: User Registration
        console.log('Step 1: User Registration');
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });

        expect(registerResponse.errors).toBeUndefined();
        expect(registerResponse.data).toBeDefined();
        expect(registerResponse.data.register).toBeDefined();

        const { token: registerToken, user: registeredUser } =
          registerResponse.data.register;

        // Verify registration response
        expect(typeof registerToken).toBe('string');
        expect(registeredUser).toMatchObject({
          email: userEmail,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
          emailVerified: false,
        });

        // Verify JWT token from registration
        const registerTokenPayload = verifyToken(registerToken);
        expect(registerTokenPayload).toBeDefined();
        expect(registerTokenPayload!.userId).toBe(registeredUser.id);
        expect(registerTokenPayload!.email).toBe(registeredUser.email);

        // Verify user was created in database
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, userEmail),
        });
        expect(dbUser).toBeDefined();
        expect(dbUser!.email).toBe(userEmail);
        expect(dbUser!.passwordHash).toBeDefined();

        // Verify user settings were created
        const dbUserSettings = await db.query.userSettings.findFirst({
          where: eq(userSettings.userId, dbUser!.id),
        });
        expect(dbUserSettings).toBeDefined();

        // Step 2: Access Protected Resource with Registration Token
        console.log(
          'Step 2: Access Protected Resource with Registration Token'
        );
        client.setToken(registerToken);

        const meResponseAfterRegister = await client.query(gqlQueries.me);
        expect(meResponseAfterRegister.errors).toBeUndefined();
        expect(meResponseAfterRegister.data.me).toMatchObject({
          id: registeredUser.id,
          email: userEmail,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
        });

        // Step 3: Logout (clear token)
        console.log('Step 3: Logout (clear token)');
        client.logout();

        // Verify access is denied after logout
        const meResponseAfterLogout = await client.query(gqlQueries.me);
        expect(meResponseAfterLogout.errors).toBeUndefined();
        expect(meResponseAfterLogout.data.me).toBeNull();

        // Step 4: Login with Credentials
        console.log('Step 4: Login with Credentials');
        const loginInput = {
          email: userEmail,
          password: userPassword,
        };

        const loginResponse = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        expect(loginResponse.errors).toBeUndefined();
        expect(loginResponse.data).toBeDefined();
        expect(loginResponse.data.login).toBeDefined();

        const { token: loginToken, user: loggedInUser } =
          loginResponse.data.login;

        // Verify login response
        expect(typeof loginToken).toBe('string');
        expect(loggedInUser).toMatchObject({
          id: registeredUser.id,
          email: userEmail,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
        });

        // Verify JWT token from login
        const loginTokenPayload = verifyToken(loginToken);
        expect(loginTokenPayload).toBeDefined();
        expect(loginTokenPayload!.userId).toBe(loggedInUser.id);
        expect(loginTokenPayload!.email).toBe(loggedInUser.email);

        // Step 5: Access Protected Resource with Login Token
        console.log('Step 5: Access Protected Resource with Login Token');
        client.setToken(loginToken);

        const meResponseAfterLogin = await client.query(gqlQueries.me);
        expect(meResponseAfterLogin.errors).toBeUndefined();
        expect(meResponseAfterLogin.data.me).toMatchObject({
          id: registeredUser.id,
          email: userEmail,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
        });

        // Step 6: Token Refresh
        console.log('Step 6: Token Refresh');
        const refreshResponse = await client.mutate(gqlQueries.refreshToken);
        expect(refreshResponse.errors).toBeUndefined();
        expect(refreshResponse.data.refreshToken).toBeDefined();

        const { token: refreshedToken, user: refreshedUser } =
          refreshResponse.data.refreshToken;
        expect(refreshedUser).toMatchObject({
          id: registeredUser.id,
          email: userEmail,
        });

        // Step 7: GraphQL Logout
        console.log('Step 7: GraphQL Logout');
        const logoutResponse = await client.mutate(gqlQueries.logout);
        expect(logoutResponse.errors).toBeUndefined();
        expect(logoutResponse.data.logout).toBe(true);

        // Step 8: Verify Access Denied After Logout
        console.log('Step 8: Verify Access Denied After Logout');
        // Note: In the current implementation, logout doesn't invalidate the token server-side
        // So we manually clear the token to simulate the client-side logout
        client.logout();

        const meResponseAfterGraphQLLogout = await client.query(gqlQueries.me);
        expect(meResponseAfterGraphQLLogout.errors).toBeUndefined();
        expect(meResponseAfterGraphQLLogout.data.me).toBeNull();

        console.log('✅ Complete authentication lifecycle test passed!');
      });
    }, 60000); // 60 second timeout
  });
});
