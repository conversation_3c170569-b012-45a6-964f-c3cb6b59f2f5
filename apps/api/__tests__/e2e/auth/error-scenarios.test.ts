import { faker } from '@faker-js/faker';
import type { FastifyInstance } from 'fastify';
import supertest from 'supertest';
// E2E tests for comprehensive error scenarios
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { createServer } from '../../../src/server.js';
import { withTransaction } from '../../../src/test-utils/db.js';
import {
  type TestGraphQLClient,
  createTestClientWithApp,
  gqlQueries,
} from '../../../src/test-utils/gql-client.js';

describe('E2E Error Scenarios Tests', () => {
  let app: FastifyInstance;
  let client: TestGraphQLClient;
  let request: supertest.SuperTest<supertest.Test>;

  beforeAll(async () => {
    // Create a real server instance for E2E testing
    app = await createServer();
    await app.ready();

    // Create GraphQL client and HTTP client
    client = createTestClientWithApp(app);
    request = supertest(app.server);
  });

  afterAll(async () => {
    // Clean up server
    if (app) {
      await app.close();
    }
  });

  beforeEach(async () => {
    // Clear any authentication state before each test
    client.logout();
  });

  describe('GraphQL Error Handling', () => {
    it.skip('should handle malformed GraphQL queries', async () => {
      const malformedQueries = [
        '{ invalidSyntax {',
        'mutation { register( }',
        'query { me { id name } }', // name field doesn't exist
        '{ }', // Empty query
        'invalid graphql',
        '{ me { id } } { me { email } }', // Multiple operations without names
      ];

      for (const query of malformedQueries) {
        console.log(`Testing malformed query: "${query}"`);

        const response = await request
          .post('/graphql')
          .send({ query })
          .expect(200); // GraphQL always returns 200, errors are in the response body

        expect(response.body.errors).toBeDefined();
        expect(response.body.errors.length).toBeGreaterThan(0);
      }
    });

    it('should handle queries with non-existent fields', async () => {
      const invalidFieldQueries = [
        'query { me { nonExistentField } }',
        'query { me { id, invalidField, email } }',
        'mutation { register(input: { email: "<EMAIL>", password: "Test123!", firstName: "Test", lastName: "User" }) { invalidField } }',
      ];

      for (const query of invalidFieldQueries) {
        console.log(`Testing invalid field query: "${query}"`);

        const response = await client.query(query);

        expect(response.errors).toBeDefined();
        expect(response.errors!.length).toBeGreaterThan(0);
        expect(response.errors![0].message).toContain('Cannot query field');
      }
    });

    it('should handle queries with invalid argument types', async () => {
      const invalidArgQueries = [
        {
          query: gqlQueries.register,
          variables: { input: 'invalid_string_instead_of_object' },
        },
        {
          query: gqlQueries.register,
          variables: {
            input: {
              email: 123,
              password: 'Test123!',
              firstName: 'Test',
              lastName: 'User',
            },
          },
        },
        {
          query: gqlQueries.login,
          variables: { input: { email: '<EMAIL>', password: 123 } },
        },
      ];

      for (const testCase of invalidArgQueries) {
        console.log('Testing invalid argument types');

        const response = await client.query(testCase.query, testCase.variables);

        expect(response.errors).toBeDefined();
        expect(response.errors!.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Authentication State Errors', () => {
    it('should handle expired tokens gracefully', async () => {
      await withTransaction(async db => {
        // Create a user first
        const userEmail = faker.internet.email().toLowerCase();
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: {
            email: userEmail,
            password: 'ValidPass123!',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        expect(registerResponse.errors).toBeUndefined();
        const { token } = registerResponse.data.register;

        // Set an obviously invalid/expired token
        const expiredToken =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.invalid';
        client.setToken(expiredToken);

        const response = await client.query(gqlQueries.me);

        // Should not error, but return null for unauthenticated user
        expect(response.errors).toBeUndefined();
        expect(response.data.me).toBeNull();
      });
    });

    it('should handle corrupted tokens', async () => {
      const corruptedTokens = [
        'corrupted.token.here',
        'Bearer corrupted.token.here',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.corrupted.signature',
        'not.a.jwt.token.at.all',
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.corrupted',
      ];

      for (const token of corruptedTokens) {
        console.log(`Testing corrupted token: ${token.substring(0, 20)}...`);

        client.setToken(token);
        const response = await client.query(gqlQueries.me);

        // Should not error, but return null for invalid token
        expect(response.errors).toBeUndefined();
        expect(response.data.me).toBeNull();
      }
    });

    it('should handle token with invalid user ID', async () => {
      await withTransaction(async db => {
        // Create a valid token structure but with non-existent user ID
        const fakeToken =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.fake_signature';

        client.setToken(fakeToken);
        const response = await client.query(gqlQueries.me);

        // Should not error, but return null for non-existent user
        expect(response.errors).toBeUndefined();
        expect(response.data.me).toBeNull();
      });
    });
  });

  describe('Rate Limiting and Abuse Prevention', () => {
    it('should handle rapid successive requests', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();

        // Make multiple rapid requests
        const promises = Array.from({ length: 10 }, (_, i) =>
          client.mutate(gqlQueries.register, {
            input: {
              email: `${i}_${userEmail}`,
              password: 'ValidPass123!',
              firstName: 'Test',
              lastName: 'User',
            },
          })
        );

        const responses = await Promise.all(promises);

        // All should succeed (no rate limiting implemented yet, but testing for stability)
        responses.forEach((response, index) => {
          if (response.errors) {
            console.log(`Request ${index} failed:`, response.errors[0].message);
          }
          // Most should succeed, but we're mainly testing that the server doesn't crash
          expect(response.data || response.errors).toBeDefined();
        });
      });
    });

    it('should handle concurrent login attempts', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();

        // Register a user first
        await client.mutate(gqlQueries.register, {
          input: {
            email: userEmail,
            password: 'ValidPass123!',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        // Make multiple concurrent login attempts
        const promises = Array.from({ length: 5 }, () =>
          client.mutate(gqlQueries.login, {
            input: {
              email: userEmail,
              password: 'ValidPass123!',
            },
          })
        );

        const responses = await Promise.all(promises);

        // All should succeed (same user, valid credentials)
        responses.forEach((response, index) => {
          expect(response.errors).toBeUndefined();
          expect(response.data.login).toBeDefined();
          expect(response.data.login.token).toBeDefined();
        });
      });
    });
  });

  describe('Database Error Scenarios', () => {
    it('should handle duplicate email registration attempts', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();
        const userData = {
          email: userEmail,
          password: 'ValidPass123!',
          firstName: 'Test',
          lastName: 'User',
        };

        // Register first user
        const firstResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        expect(firstResponse.errors).toBeUndefined();

        // Try to register with same email multiple times
        const duplicatePromises = Array.from({ length: 3 }, () =>
          client.mutate(gqlQueries.register, {
            input: {
              ...userData,
              firstName: 'Different',
              lastName: 'Name',
            },
          })
        );

        const duplicateResponses = await Promise.all(duplicatePromises);

        // All should fail with duplicate email error
        for (const response of duplicateResponses) {
          expect(response.errors).toBeDefined();
          expect(response.errors![0].message).toContain(
            'User with this email already exists'
          );
        }
      });
    });
  });

  describe('Network and Protocol Errors', () => {
    it('should handle requests with invalid Content-Type', async () => {
      const response = await request
        .post('/graphql')
        .set('Content-Type', 'text/plain')
        .send('{ me { id } }')
        .expect(400);

      expect(response.body.statusCode).toBe(400);
    });

    it('should handle requests with no body', async () => {
      const response = await request.post('/graphql').expect(400);

      expect(response.body.statusCode).toBe(400);
    });

    it.skip('should handle GET requests to GraphQL endpoint', async () => {
      const response = await request.get('/graphql').expect(200);

      // Should serve GraphiQL or return schema information
      expect(response.status).toBe(200);
    });
  });
});
