import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
import type { FastifyInstance } from 'fastify';
import supertest from 'supertest';
// E2E tests for OAuth authentication lifecycle
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { userSettings, users } from '../../../src/database/schema.js';
import { createServer } from '../../../src/server.js';
import { withTransaction } from '../../../src/test-utils/db.js';
import {
  type TestGraphQLClient,
  createTestClientWithApp,
  gqlQueries,
} from '../../../src/test-utils/gql-client.js';

describe.skip('E2E OAuth Authentication Lifecycle Tests', () => {
  let app: FastifyInstance;
  let client: TestGraphQLClient;
  let request: supertest.SuperTest<supertest.Test>;

  beforeAll(async () => {
    // Create a real server instance for E2E testing
    app = await createServer();
    await app.ready();

    // Create GraphQL client and HTTP client
    client = createTestClientWithApp(app);
    request = supertest(app.server);
  });

  afterAll(async () => {
    // Clean up server
    if (app) {
      await app.close();
    }
  });

  beforeEach(async () => {
    // Clear any authentication state before each test
    client.logout();
  });

  describe('Google OAuth Flow', () => {
    it('should complete Google OAuth authentication lifecycle', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();

        // Step 1: Initiate Google OAuth
        console.log('Step 1: Initiate Google OAuth');
        const initiateResponse = await request.get('/auth/google').expect(302); // Should redirect to Google

        expect(initiateResponse.headers.location).toBeDefined();
        expect(initiateResponse.headers.location).toContain(
          'accounts.google.com'
        );

        // Step 2: Simulate OAuth callback with authorization code
        console.log('Step 2: Simulate OAuth callback');
        const mockAuthCode = `mock_google_auth_code_${Date.now()}`;
        const mockState = `mock_state_${Date.now()}`;

        // Mock the OAuth callback (in real implementation, this would come from Google)
        const callbackResponse = await request
          .get(`/auth/google/callback?code=${mockAuthCode}&state=${mockState}`)
          .expect(302); // Should redirect after successful auth

        // Verify redirect contains token (for web) or success indicator
        expect(callbackResponse.headers.location).toBeDefined();
        const redirectUrl = new URL(
          callbackResponse.headers.location,
          'http://localhost'
        );

        // For web clients, token should be in URL fragment or query params
        const hasToken =
          redirectUrl.searchParams.has('token') ||
          redirectUrl.hash.includes('token=') ||
          redirectUrl.pathname.includes('success');
        expect(hasToken).toBe(true);

        // Step 3: Verify user was created in database
        console.log('Step 3: Verify user creation');
        // Note: In the current mock implementation, we need to check if the OAuth flow
        // would create a user. Since it's mocked, we'll verify the endpoint behavior.

        // The OAuth callback should have processed the authorization code
        // and either created a new user or found an existing one

        console.log('✅ Google OAuth lifecycle test passed!');
      });
    });

    it('should handle Google OAuth errors gracefully', async () => {
      await withTransaction(async db => {
        // Step 1: Simulate OAuth error callback
        console.log('Step 1: Simulate OAuth error');
        const errorResponse = await request
          .get(
            '/auth/google/callback?error=access_denied&error_description=User%20denied%20access'
          )
          .expect(302); // Should redirect with error

        expect(errorResponse.headers.location).toBeDefined();
        const redirectUrl = new URL(
          errorResponse.headers.location,
          'http://localhost'
        );

        // Should redirect to error page or include error in URL
        const hasError =
          redirectUrl.searchParams.has('error') ||
          redirectUrl.pathname.includes('error') ||
          redirectUrl.hash.includes('error=');
        expect(hasError).toBe(true);

        console.log('✅ Google OAuth error handling test passed!');
      });
    });
  });

  describe('Apple OAuth Flow', () => {
    it('should complete Apple OAuth authentication lifecycle', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();

        // Step 1: Initiate Apple OAuth
        console.log('Step 1: Initiate Apple OAuth');
        const initiateResponse = await request.get('/auth/apple').expect(302); // Should redirect to Apple

        expect(initiateResponse.headers.location).toBeDefined();
        expect(initiateResponse.headers.location).toContain(
          'appleid.apple.com'
        );

        // Step 2: Simulate OAuth callback with authorization code
        console.log('Step 2: Simulate OAuth callback');
        const mockAuthCode = `mock_apple_auth_code_${Date.now()}`;
        const mockState = `mock_state_${Date.now()}`;

        // Mock the OAuth callback (in real implementation, this would come from Apple)
        const callbackResponse = await request
          .get(`/auth/apple/callback?code=${mockAuthCode}&state=${mockState}`)
          .expect(302); // Should redirect after successful auth

        // Verify redirect contains token (for web) or success indicator
        expect(callbackResponse.headers.location).toBeDefined();
        const redirectUrl = new URL(
          callbackResponse.headers.location,
          'http://localhost'
        );

        // For web clients, token should be in URL fragment or query params
        const hasToken =
          redirectUrl.searchParams.has('token') ||
          redirectUrl.hash.includes('token=') ||
          redirectUrl.pathname.includes('success');
        expect(hasToken).toBe(true);

        console.log('✅ Apple OAuth lifecycle test passed!');
      });
    });

    it('should handle Apple OAuth errors gracefully', async () => {
      await withTransaction(async db => {
        // Step 1: Simulate OAuth error callback
        console.log('Step 1: Simulate OAuth error');
        const errorResponse = await request
          .get(
            '/auth/apple/callback?error=user_cancelled&error_description=User%20cancelled%20authentication'
          )
          .expect(302); // Should redirect with error

        expect(errorResponse.headers.location).toBeDefined();
        const redirectUrl = new URL(
          errorResponse.headers.location,
          'http://localhost'
        );

        // Should redirect to error page or include error in URL
        const hasError =
          redirectUrl.searchParams.has('error') ||
          redirectUrl.pathname.includes('error') ||
          redirectUrl.hash.includes('error=');
        expect(hasError).toBe(true);

        console.log('✅ Apple OAuth error handling test passed!');
      });
    });
  });

  describe('OAuth Security Tests', () => {
    it('should reject OAuth callback without authorization code', async () => {
      await withTransaction(async db => {
        // Attempt callback without code parameter
        const response = await request.get('/auth/google/callback').expect(302); // Should redirect with error

        expect(response.headers.location).toBeDefined();
        const redirectUrl = new URL(
          response.headers.location,
          'http://localhost'
        );

        // Should indicate error due to missing authorization code
        const hasError =
          redirectUrl.searchParams.has('error') ||
          redirectUrl.pathname.includes('error');
        expect(hasError).toBe(true);
      });
    });

    it('should handle invalid authorization codes', async () => {
      await withTransaction(async db => {
        // Attempt callback with invalid code
        const response = await request
          .get('/auth/google/callback?code=invalid_code_123')
          .expect(302); // Should redirect with error

        expect(response.headers.location).toBeDefined();
        const redirectUrl = new URL(
          response.headers.location,
          'http://localhost'
        );

        // Should indicate error due to invalid authorization code
        const hasError =
          redirectUrl.searchParams.has('error') ||
          redirectUrl.pathname.includes('error');
        expect(hasError).toBe(true);
      });
    });
  });
});
