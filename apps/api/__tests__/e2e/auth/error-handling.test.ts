import { faker } from '@faker-js/faker';
import type { FastifyInstance } from 'fastify';
import supertest from 'supertest';
// E2E tests for authentication error handling and validation
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { createServer } from '../../../src/server.js';
import { withTransaction } from '../../../src/test-utils/db.js';
import {
  type TestGraphQLClient,
  createTestClientWithApp,
  gqlQueries,
} from '../../../src/test-utils/gql-client.js';

describe('E2E Authentication Error Handling Tests', () => {
  let app: FastifyInstance;
  let client: TestGraphQLClient;
  let request: supertest.SuperTest<supertest.Test>;

  beforeAll(async () => {
    // Create a real server instance for E2E testing
    app = await createServer();
    await app.ready();

    // Create GraphQL client and HTTP client
    client = createTestClientWithApp(app);
    request = supertest(app.server);
  });

  afterAll(async () => {
    // Clean up server
    if (app) {
      await app.close();
    }
  });

  beforeEach(async () => {
    // Clear any authentication state before each test
    client.logout();
  });

  describe('Registration Error Handling', () => {
    it('should handle invalid email formats', async () => {
      await withTransaction(async db => {
        const invalidEmails = [
          'invalid-email',
          'missing@domain',
          '@missing-local.com',
          'spaces <EMAIL>',
          'special!<EMAIL>',
          '',
        ];

        for (const email of invalidEmails) {
          console.log(`Testing invalid email: ${email}`);

          const registerInput = {
            email,
            password: 'ValidPass123!',
            firstName: 'Test',
            lastName: 'User',
          };

          const response = await client.mutate(gqlQueries.register, {
            input: registerInput,
          });

          expect(response.errors).toBeDefined();
          expect(response.errors).toHaveLength(1);
          expect(response.errors![0].message).toContain('Validation failed');
        }
      });
    });

    it('should handle weak passwords', async () => {
      await withTransaction(async db => {
        const weakPasswords = [
          'short',
          '12345678',
          'password',
          'PASSWORD',
          'Password',
          'Pass123',
          '',
        ];

        for (const password of weakPasswords) {
          console.log(`Testing weak password: ${password}`);

          const registerInput = {
            email: faker.internet.email().toLowerCase(),
            password,
            firstName: 'Test',
            lastName: 'User',
          };

          const response = await client.mutate(gqlQueries.register, {
            input: registerInput,
          });

          expect(response.errors).toBeDefined();
          expect(response.errors).toHaveLength(1);
          expect(response.errors![0].message).toContain('Validation failed');
        }
      });
    });

    it('should handle duplicate email registration', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();
        const userData = {
          email: userEmail,
          password: 'SecurePass123!',
          firstName: 'First',
          lastName: 'User',
        };

        // Register first user
        const firstResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        expect(firstResponse.errors).toBeUndefined();

        // Try to register with same email
        const duplicateData = {
          ...userData,
          firstName: 'Second',
          lastName: 'User',
        };

        const secondResponse = await client.mutate(gqlQueries.register, {
          input: duplicateData,
        });

        expect(secondResponse.errors).toBeDefined();
        expect(secondResponse.errors).toHaveLength(1);
        expect(secondResponse.errors![0].message).toContain(
          'User with this email already exists'
        );
      });
    });

    it('should handle missing required fields', async () => {
      await withTransaction(async db => {
        // Test with empty strings instead of missing fields (which GraphQL schema prevents)
        const emptyEmailResponse = await client.mutate(gqlQueries.register, {
          input: {
            email: '',
            password: 'SecurePass123!',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        expect(emptyEmailResponse.errors).toBeDefined();
        expect(emptyEmailResponse.errors!.length).toBeGreaterThan(0);
        expect(emptyEmailResponse.errors![0].message).toContain(
          'Validation failed'
        );

        // Test with empty password
        const emptyPasswordResponse = await client.mutate(gqlQueries.register, {
          input: {
            email: faker.internet.email().toLowerCase(),
            password: '',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        expect(emptyPasswordResponse.errors).toBeDefined();
        expect(emptyPasswordResponse.errors!.length).toBeGreaterThan(0);
        expect(emptyPasswordResponse.errors![0].message).toContain(
          'Validation failed'
        );

        // Test with empty names (should be allowed as names are optional)
        const emptyNamesResponse = await client.mutate(gqlQueries.register, {
          input: {
            email: faker.internet.email().toLowerCase(),
            password: 'SecurePass123!',
            firstName: '',
            lastName: '',
          },
        });

        // Empty names should be allowed since firstName and lastName are optional
        expect(emptyNamesResponse.errors).toBeUndefined();
        expect(emptyNamesResponse.data.register).toBeDefined();
        expect(emptyNamesResponse.data.register.user.firstName).toBe('');
        expect(emptyNamesResponse.data.register.user.lastName).toBe('');
      });
    });
  });

  describe('Login Error Handling', () => {
    it('should handle non-existent user login', async () => {
      await withTransaction(async db => {
        const loginInput = {
          email: faker.internet.email().toLowerCase(),
          password: 'AnyPassword123!',
        };

        const response = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain(
          'Invalid email or password'
        );
      });
    });

    it('should handle incorrect password', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();
        const correctPassword = 'CorrectPass123!';

        // Register user
        await client.mutate(gqlQueries.register, {
          input: {
            email: userEmail,
            password: correctPassword,
            firstName: 'Test',
            lastName: 'User',
          },
        });

        // Try to login with wrong password
        const loginInput = {
          email: userEmail,
          password: 'WrongPass123!',
        };

        const response = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain(
          'Invalid email or password'
        );
      });
    });

    it('should handle malformed login requests', async () => {
      await withTransaction(async db => {
        // Test with invalid email format
        const invalidEmailResponse = await client.mutate(gqlQueries.login, {
          input: {
            email: 'invalid-email',
            password: 'ValidPass123!',
          },
        });

        expect(invalidEmailResponse.errors).toBeDefined();
        expect(invalidEmailResponse.errors![0].message).toContain(
          'Validation failed'
        );

        // Test with empty password
        const emptyPasswordResponse = await client.mutate(gqlQueries.login, {
          input: {
            email: faker.internet.email().toLowerCase(),
            password: '',
          },
        });

        expect(emptyPasswordResponse.errors).toBeDefined();
        expect(emptyPasswordResponse.errors![0].message).toContain(
          'Validation failed'
        );
      });
    });
  });

  describe('Protected Route Error Handling', () => {
    it('should handle requests without authentication token', async () => {
      await withTransaction(async db => {
        // Ensure no token is set
        client.logout();

        const response = await client.query(gqlQueries.me);

        // Should not error, but return null
        expect(response.errors).toBeUndefined();
        expect(response.data.me).toBeNull();
      });
    });

    it('should handle invalid authentication tokens', async () => {
      await withTransaction(async db => {
        const invalidTokens = [
          'invalid-token',
          'Bearer invalid-token',
          'malformed.jwt.token',
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
          '',
        ];

        for (const token of invalidTokens) {
          console.log(`Testing invalid token: ${token}`);

          client.setToken(token);
          const response = await client.query(gqlQueries.me);

          // Should not error, but return null
          expect(response.errors).toBeUndefined();
          expect(response.data.me).toBeNull();
        }
      });
    });

    it('should handle refresh token without authentication', async () => {
      await withTransaction(async db => {
        // Ensure no token is set
        client.logout();

        const response = await client.mutate(gqlQueries.refreshToken);

        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain('Not authenticated');
      });
    });
  });

  describe('Rate Limiting and Security', () => {
    it('should handle multiple failed login attempts gracefully', async () => {
      await withTransaction(async db => {
        const userEmail = faker.internet.email().toLowerCase();

        // Register a user first
        await client.mutate(gqlQueries.register, {
          input: {
            email: userEmail,
            password: 'CorrectPass123!',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        // Attempt multiple failed logins
        const failedAttempts = 5;
        for (let i = 0; i < failedAttempts; i++) {
          const response = await client.mutate(gqlQueries.login, {
            input: {
              email: userEmail,
              password: `WrongPass${i}!`,
            },
          });

          expect(response.errors).toBeDefined();
          expect(response.errors![0].message).toContain(
            'Invalid email or password'
          );
        }

        // Should still be able to login with correct credentials
        const correctResponse = await client.mutate(gqlQueries.login, {
          input: {
            email: userEmail,
            password: 'CorrectPass123!',
          },
        });

        expect(correctResponse.errors).toBeUndefined();
        expect(correctResponse.data.login).toBeDefined();
      });
    });
  });

  describe('GraphQL Error Format Validation', () => {
    it('should return properly formatted GraphQL errors', async () => {
      await withTransaction(async db => {
        const response = await client.mutate(gqlQueries.register, {
          input: {
            email: 'invalid-email',
            password: 'weak',
            firstName: 'Test',
            lastName: 'User',
          },
        });

        expect(response.errors).toBeDefined();
        expect(response.errors!.length).toBeGreaterThan(0);

        const error = response.errors![0];
        expect(error.message).toBeDefined();
        expect(typeof error.message).toBe('string');

        // GraphQL errors should have either locations or path (or both)
        // Some errors may not have locations if they're validation errors
        const hasLocations =
          error.locations &&
          Array.isArray(error.locations) &&
          error.locations.length > 0;
        const hasPath =
          error.path && Array.isArray(error.path) && error.path.length > 0;

        // For validation errors, we mainly care that the error message is present and meaningful
        // The path and locations may not always be present for resolver-level validation errors
        expect(error.message).toContain('Validation failed');

        // Verify it's a proper GraphQL error structure
        expect(error).toHaveProperty('message');
        // Extensions may contain additional error information
        if (error.extensions) {
          expect(typeof error.extensions).toBe('object');
        }
      });
    });
  });
});
