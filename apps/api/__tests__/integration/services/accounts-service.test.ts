import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { eq } from 'drizzle-orm';
import { accounts, users } from '../../../src/database/schema.js';
import { withSeededData } from '../../../src/test-utils/db.js';
import { faker } from '@faker-js/faker';
import {
  getUserAccounts,
  getAccountById,
  createAccount,
  updateAccount,
  deleteAccount,
} from '../../../src/services/accounts/accounts.service.js';

describe('Integration Tests - Accounts Service', () => {
  let testUserId: string;

  beforeEach(async () => {
    // Create a test user
    await withSeededData(async (db) => {
      const [testUser] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUserId = testUser.id;
    });
  });

  afterEach(async () => {
    // Clean up test data
    await withSeededData(async (db) => {
      await db.delete(accounts).where(eq(accounts.userId, testUserId));
      await db.delete(users).where(eq(users.id, testUserId));
    });
  });

  describe('getUserAccounts', () => {
    it('should return empty array when user has no accounts', async () => {
      const result = await getUserAccounts(testUserId);
      expect(result).toEqual([]);
    });

    it('should return user accounts when they exist', async () => {
      await withSeededData(async (db) => {
        // Create test accounts
        const accountsData = [
          {
            userId: testUserId,
            name: 'Checking Account',
            type: 'checking' as const,
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          },
          {
            userId: testUserId,
            name: 'Savings Account',
            type: 'savings' as const,
            initialBalance: '5000.00',
            currentBalance: '5000.00',
            currency: 'USD',
          },
        ];

        await db.insert(accounts).values(accountsData);
      });

      const result = await getUserAccounts(testUserId);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Checking Account');
      expect(result[0].type).toBe('checking');
      expect(result[1].name).toBe('Savings Account');
      expect(result[1].type).toBe('savings');
    });

    it('should not return archived accounts by default', async () => {
      await withSeededData(async (db) => {
        // Create active and archived accounts
        const accountsData = [
          {
            userId: testUserId,
            name: 'Active Account',
            type: 'checking' as const,
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
            isArchived: false,
          },
          {
            userId: testUserId,
            name: 'Archived Account',
            type: 'savings' as const,
            initialBalance: '2000.00',
            currentBalance: '2000.00',
            currency: 'USD',
            isArchived: true,
          },
        ];

        await db.insert(accounts).values(accountsData);
      });

      const result = await getUserAccounts(testUserId);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Active Account');
      expect(result[0].isArchived).toBe(false);
    });

    it('should return accounts in display order', async () => {
      await withSeededData(async (db) => {
        // Create accounts with different display orders
        const accountsData = [
          {
            userId: testUserId,
            name: 'Third Account',
            type: 'checking' as const,
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
            displayOrder: 3,
          },
          {
            userId: testUserId,
            name: 'First Account',
            type: 'savings' as const,
            initialBalance: '2000.00',
            currentBalance: '2000.00',
            currency: 'USD',
            displayOrder: 1,
          },
          {
            userId: testUserId,
            name: 'Second Account',
            type: 'credit_card' as const,
            initialBalance: '0.00',
            currentBalance: '0.00',
            currency: 'USD',
            displayOrder: 2,
          },
        ];

        await db.insert(accounts).values(accountsData);
      });

      const result = await getUserAccounts(testUserId);

      expect(result).toHaveLength(3);
      expect(result[0].name).toBe('First Account');
      expect(result[1].name).toBe('Second Account');
      expect(result[2].name).toBe('Third Account');
    });
  });

  describe('getAccountById', () => {
    it('should return account by ID when it exists and belongs to user', async () => {
      let accountId: string = '';

      await withSeededData(async (db) => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Test Account',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();
        accountId = account.id;
      });

      const result = await getAccountById(accountId, testUserId);

      expect(result).toBeDefined();
      expect(result?.id).toBe(accountId);
      expect(result?.name).toBe('Test Account');
      expect(result?.type).toBe('checking');
    });

    it('should throw error when account ID is invalid UUID', async () => {
      await expect(getAccountById('non-existent-id', testUserId)).rejects.toThrow();
    });

    it('should return null when account belongs to different user', async () => {
      let accountId: string;
      let otherUserId: string;

      await withSeededData(async (db) => {
        // Create another user and their account
        const [otherUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();
        otherUserId = otherUser.id;

        const [otherAccount] = await db
          .insert(accounts)
          .values({
            userId: otherUserId,
            name: 'Other User Account',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();
        accountId = otherAccount.id;
      });

      const result = await getAccountById(accountId, testUserId);
      expect(result).toBeNull();

      // Clean up
      await withSeededData(async (db) => {
        await db.delete(accounts).where(eq(accounts.id, accountId));
        await db.delete(users).where(eq(users.id, otherUserId));
      });
    });
  });

  describe('createAccount', () => {
    it('should create a new account with valid data', async () => {
      const input = {
        name: 'New Checking Account',
        type: 'checking' as const,
        initialBalance: 1500.00,
        currency: 'USD',
        icon: 'bank',
        color: '#3498DB',
      };

      const result = await createAccount(testUserId, input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);
      expect(result.type).toBe(input.type);
      expect(result.currency).toBe(input.currency);
      expect(result.icon).toBe(input.icon);
      expect(result.color).toBe(input.color);
      expect(result.userId).toBe(testUserId);
      expect(result.isArchived).toBe(false);

      // Verify it was actually created in the database
      await withSeededData(async (db) => {
        const dbAccount = await db.select().from(accounts).where(eq(accounts.id, result.id)).limit(1);
        expect(dbAccount).toHaveLength(1);
        expect(dbAccount[0].name).toBe(input.name);
      });
    });

    it('should create account with minimal required fields', async () => {
      const input = {
        name: 'Minimal Account',
        type: 'savings' as const,
      };

      const result = await createAccount(testUserId, input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);
      expect(result.type).toBe(input.type);
      expect(result.currency).toBe('USD'); // Default value
      expect(result.userId).toBe(testUserId);
    });

    it('should handle different account types', async () => {
      const accountTypes = ['checking', 'savings', 'credit_card', 'cash', 'investment'] as const;

      for (const type of accountTypes) {
        const input = {
          name: `${type} Account`,
          type,
        };

        const result = await createAccount(testUserId, input);

        expect(result).toBeDefined();
        expect(result.type).toBe(type);
        expect(result.name).toBe(`${type} Account`);
      }
    });
  });

  describe('updateAccount', () => {
    it('should update account with valid data', async () => {
      let accountId: string;

      await withSeededData(async (db) => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Original Name',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();
        accountId = account.id;
      });

      const updateInput = {
        name: 'Updated Name',
        icon: 'updated-icon',
        color: '#FF5733',
      };

      const result = await updateAccount(accountId, testUserId, updateInput);

      expect(result).toBeDefined();
      expect(result.name).toBe(updateInput.name);
      expect(result.icon).toBe(updateInput.icon);
      expect(result.color).toBe(updateInput.color);
      expect(result.type).toBe('checking'); // Unchanged
    });

    it('should throw error when updating non-existent account', async () => {
      const updateInput = {
        name: 'Updated Name',
      };

      await expect(updateAccount('non-existent-id', testUserId, updateInput)).rejects.toThrow();
    });

    it('should throw error when updating account belonging to different user', async () => {
      let accountId: string;
      let otherUserId: string;

      await withSeededData(async (db) => {
        // Create another user and their account
        const [otherUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();
        otherUserId = otherUser.id;

        const [otherAccount] = await db
          .insert(accounts)
          .values({
            userId: otherUserId,
            name: 'Other User Account',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();
        accountId = otherAccount.id;
      });

      const updateInput = {
        name: 'Hacked Name',
      };

      await expect(updateAccount(accountId, testUserId, updateInput)).rejects.toThrow();

      // Verify the account was not actually updated
      await withSeededData(async (db) => {
        const unchangedAccount = await db.select().from(accounts).where(eq(accounts.id, accountId)).limit(1);
        expect(unchangedAccount[0].name).toBe('Other User Account');

        // Clean up
        await db.delete(accounts).where(eq(accounts.id, accountId));
        await db.delete(users).where(eq(users.id, otherUserId));
      });
    });
  });

  describe('deleteAccount', () => {
    it('should archive account instead of deleting', async () => {
      let accountId: string;

      await withSeededData(async (db) => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Account to Delete',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();
        accountId = account.id;
      });

      const result = await deleteAccount(accountId, testUserId);

      expect(result).toBe(true);

      // Verify account is archived, not deleted
      await withSeededData(async (db) => {
        const archivedAccount = await db.select().from(accounts).where(eq(accounts.id, accountId)).limit(1);
        expect(archivedAccount).toHaveLength(1);
        expect(archivedAccount[0].isArchived).toBe(true);
      });
    });

    it('should throw error when deleting non-existent account', async () => {
      await expect(deleteAccount('non-existent-id', testUserId)).rejects.toThrow();
    });

    it('should throw error when deleting account belonging to different user', async () => {
      let accountId: string;
      let otherUserId: string;

      await withSeededData(async (db) => {
        // Create another user and their account
        const [otherUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();
        otherUserId = otherUser.id;

        const [otherAccount] = await db
          .insert(accounts)
          .values({
            userId: otherUserId,
            name: 'Other User Account',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();
        accountId = otherAccount.id;
      });

      await expect(deleteAccount(accountId, testUserId)).rejects.toThrow();

      // Verify the account was not archived
      await withSeededData(async (db) => {
        const unchangedAccount = await db.select().from(accounts).where(eq(accounts.id, accountId)).limit(1);
        expect(unchangedAccount[0].isArchived).toBe(false);

        // Clean up
        await db.delete(accounts).where(eq(accounts.id, accountId));
        await db.delete(users).where(eq(users.id, otherUserId));
      });
    });
  });
}); 