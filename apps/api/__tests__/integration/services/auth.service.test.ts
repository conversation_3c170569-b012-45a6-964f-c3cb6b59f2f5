import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
// Integration tests for authentication service with real database
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { type OAuthProfile, OAuthProvider } from '../../../src/config/auth.js';
import { userSettings, users } from '../../../src/database/schema.js';
import {
  getUserById,
  handleOAuthLogin,
  loginUser,
  registerUser,
  validateJwtPayload,
} from '../../../src/services/auth/auth.service.js';
import { withSeededData, withTransaction } from '../../../src/test-utils/db.js';
import {
  DatabaseError,
  EmailAlreadyExistsError,
  InvalidCredentialsError,
  OAuthError,
  WeakPasswordError,
} from '../../../src/utils/error-handler.js';

describe('Authentication Service Integration Tests', () => {
  describe('registerUser', () => {
    it('should successfully register a new user with valid data', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'New',
          lastName: 'User',
        };

        const result = await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );

        // Verify the response structure
        expect(result).toHaveProperty('user');
        expect(result).toHaveProperty('token');
        expect(result.user.email).toBe(userData.email.toLowerCase());
        expect(result.user.firstName).toBe(userData.firstName);
        expect(result.user.lastName).toBe(userData.lastName);
        expect(result.user.role).toBe('user');
        expect(result.user.emailVerified).toBe(false);
        expect(typeof result.token).toBe('string');

        // Verify user was created in database
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, userData.email.toLowerCase()),
        });
        expect(dbUser).toBeDefined();
        expect(dbUser!.email).toBe(userData.email.toLowerCase());
        expect(dbUser!.passwordHash).toBeDefined();
        expect(dbUser!.passwordHash).not.toBe(userData.password); // Should be hashed

        // Verify user settings were created
        const dbUserSettings = await db.query.userSettings.findFirst({
          where: eq(userSettings.userId, dbUser!.id),
        });
        expect(dbUserSettings).toBeDefined();
        expect(dbUserSettings!.defaultCurrency).toBe('USD');
        expect(dbUserSettings!.theme).toBe('light');
      });
    });

    it('should throw EmailAlreadyExistsError for duplicate email', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'First',
          lastName: 'User',
        };

        // Register first user
        await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );

        // Try to register with same email
        await expect(
          registerUser(
            userData.email,
            userData.password,
            'Second',
            userData.lastName
          )
        ).rejects.toThrow(EmailAlreadyExistsError);
      });
    });

    it('should throw WeakPasswordError for short password', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'short',
          firstName: 'Weak',
          lastName: 'Password',
        };

        await expect(
          registerUser(
            userData.email,
            userData.password,
            userData.firstName,
            userData.lastName
          )
        ).rejects.toThrow(WeakPasswordError);
      });
    });

    it('should handle email case insensitivity', async () => {
      await withTransaction(async db => {
        const baseEmail = faker.internet.email().toLowerCase();
        const userData = {
          email: baseEmail.toUpperCase(), // Test with uppercase
          password: 'SecurePass123!',
          firstName: 'Case',
          lastName: 'Test',
        };

        const result = await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );

        expect(result.user.email).toBe(baseEmail);

        // Verify in database
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, baseEmail),
        });
        expect(dbUser).toBeDefined();
      });
    });
  });

  describe('loginUser', () => {
    it('should successfully login with valid credentials', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Login',
          lastName: 'Test',
        };

        // Register user first
        await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );

        // Login with same credentials
        const result = await loginUser(userData.email, userData.password);

        expect(result).toHaveProperty('user');
        expect(result).toHaveProperty('token');
        expect(result.user.email).toBe(userData.email.toLowerCase());
        expect(result.user.firstName).toBe(userData.firstName);
        expect(typeof result.token).toBe('string');
      });
    });

    it('should throw InvalidCredentialsError for non-existent user', async () => {
      await withTransaction(async db => {
        await expect(
          loginUser(faker.internet.email().toLowerCase(), 'AnyPassword123!')
        ).rejects.toThrow(InvalidCredentialsError);
      });
    });

    it('should throw InvalidCredentialsError for wrong password', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'CorrectPass123!',
          firstName: 'Wrong',
          lastName: 'Password',
        };

        // Register user
        await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );

        // Try to login with wrong password
        await expect(
          loginUser(userData.email, 'WrongPass123!')
        ).rejects.toThrow(InvalidCredentialsError);
      });
    });

    it('should handle email case insensitivity during login', async () => {
      await withTransaction(async db => {
        const baseEmail = faker.internet.email().toLowerCase();
        const userData = {
          email: baseEmail,
          password: 'SecurePass123!',
          firstName: 'Case',
          lastName: 'Login',
        };

        // Register user
        await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );

        // Login with different case
        const result = await loginUser(
          baseEmail.toUpperCase(),
          userData.password
        );

        expect(result.user.email).toBe(baseEmail);
      });
    });
  });

  describe('handleOAuthLogin', () => {
    it('should create new user for first-time OAuth login', async () => {
      await withTransaction(async db => {
        const oauthProfile: OAuthProfile = {
          id: `google_${faker.string.alphanumeric(10)}`,
          email: faker.internet.email().toLowerCase(),
          firstName: 'OAuth',
          lastName: 'User',
          provider: OAuthProvider.GOOGLE,
        };

        const result = await handleOAuthLogin(oauthProfile);

        expect(result).toHaveProperty('user');
        expect(result).toHaveProperty('token');
        expect(result.user.email).toBe(oauthProfile.email.toLowerCase());
        expect(result.user.firstName).toBe(oauthProfile.firstName);
        expect(result.user.emailVerified).toBe(true); // OAuth users are pre-verified

        // Verify user was created in database
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, oauthProfile.email.toLowerCase()),
        });
        expect(dbUser).toBeDefined();
        expect(dbUser!.passwordHash).toBeNull(); // OAuth users don't have passwords

        // Verify user settings were created
        const dbUserSettings = await db.query.userSettings.findFirst({
          where: eq(userSettings.userId, dbUser!.id),
        });
        expect(dbUserSettings).toBeDefined();
      });
    });

    it('should login existing user for repeat OAuth login', async () => {
      await withTransaction(async db => {
        const oauthProfile: OAuthProfile = {
          id: `google_${faker.string.alphanumeric(10)}`,
          email: faker.internet.email().toLowerCase(),
          firstName: 'Existing',
          lastName: 'OAuth',
          provider: OAuthProvider.GOOGLE,
        };

        // First OAuth login (creates user)
        const firstResult = await handleOAuthLogin(oauthProfile);
        const firstUserId = firstResult.user.id;

        // Second OAuth login (should find existing user)
        const secondResult = await handleOAuthLogin(oauthProfile);

        expect(secondResult.user.id).toBe(firstUserId);
        expect(secondResult.user.email).toBe(oauthProfile.email.toLowerCase());
      });
    });

    it('should handle OAuth profile with minimal data', async () => {
      await withTransaction(async db => {
        const oauthProfile: OAuthProfile = {
          id: `apple_${faker.string.alphanumeric(10)}`,
          email: faker.internet.email().toLowerCase(),
          provider: OAuthProvider.APPLE,
          // No firstName/lastName
        };

        const result = await handleOAuthLogin(oauthProfile);

        expect(result.user.email).toBe(oauthProfile.email.toLowerCase());
        expect(result.user.firstName).toBeNull();
        expect(result.user.lastName).toBeNull();
      });
    });
  });

  describe('getUserById', () => {
    it('should return user for valid ID', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Get',
          lastName: 'User',
        };

        const registerResult = await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );
        const userId = registerResult.user.id;

        const user = await getUserById(userId);

        expect(user).toBeDefined();
        expect(user!.id).toBe(userId);
        expect(user!.email).toBe(userData.email.toLowerCase());
      });
    });

    it('should return null for non-existent ID', async () => {
      await withTransaction(async db => {
        const user = await getUserById('550e8400-e29b-41d4-a716-************'); // Valid UUID format
        expect(user).toBeNull();
      });
    });
  });

  describe('validateJwtPayload', () => {
    it('should return true for valid payload with existing user', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Validate',
          lastName: 'User',
        };

        const registerResult = await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );
        const payload = {
          userId: registerResult.user.id,
          email: registerResult.user.email,
          role: registerResult.user.role,
        };

        const isValid = await validateJwtPayload(payload);
        expect(isValid).toBe(true);
      });
    });

    it('should return false for payload with non-existent user', async () => {
      await withTransaction(async db => {
        const payload = {
          userId: '550e8400-e29b-41d4-a716-************', // Valid UUID format
          email: faker.internet.email().toLowerCase(),
          role: 'user',
        };

        const isValid = await validateJwtPayload(payload);
        expect(isValid).toBe(false);
      });
    });

    it('should return false for payload with mismatched email', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Mismatch',
          lastName: 'User',
        };

        const registerResult = await registerUser(
          userData.email,
          userData.password,
          userData.firstName,
          userData.lastName
        );
        const payload = {
          userId: registerResult.user.id,
          email: faker.internet.email().toLowerCase(), // Wrong email
          role: registerResult.user.role,
        };

        const isValid = await validateJwtPayload(payload);
        expect(isValid).toBe(false);
      });
    });

    it('should return false for invalid payload structure', async () => {
      await withTransaction(async db => {
        const invalidPayload = {
          userId: '',
          email: '',
          role: 'user',
        };

        const isValid = await validateJwtPayload(invalidPayload);
        expect(isValid).toBe(false);
      });
    });
  });
});
