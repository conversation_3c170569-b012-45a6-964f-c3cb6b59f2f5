import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
import { categories, users } from '../../../src/database/schema.js';
import {
  getUserCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
} from '../../../src/services/categories/categories.service.js';
import {
  DatabaseError,
  RecordNotFoundError,
} from '../../../src/utils/error-handler.js';
import { withSeededData } from '../../../src/test-utils/db.js';
import type { CreateCategoryInput, UpdateCategoryInput } from '../../../src/services/categories/categories.types.js';

describe('Integration Tests - Categories Service', () => {
  let testUserId: string;
  let testUser2Id: string;
  let testCategoryId: string;

  beforeEach(async () => {
    // Create test users that persist for the service functions
    await withSeededData(async db => {
      // Create first test user
      const [testUser] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUserId = testUser.id;

      // Create second test user for cross-user tests
      const [testUser2] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUser2Id = testUser2.id;
    });
  });

  afterEach(async () => {
    // Clean up test data
    await withSeededData(async db => {
      if (testCategoryId) {
        await db.delete(categories).where(eq(categories.id, testCategoryId));
      }
      await db.delete(categories).where(eq(categories.userId, testUserId));
      await db.delete(categories).where(eq(categories.userId, testUser2Id));
      await db.delete(users).where(eq(users.id, testUserId));
      await db.delete(users).where(eq(users.id, testUser2Id));
    });
  });

  describe('getUserCategories', () => {
    it('should return empty array for user with no categories', async () => {
      const result = await getUserCategories(testUserId);
      expect(result).toEqual([]);
    });

    it('should return user categories excluding archived ones', async () => {
      // Create test categories
      await withSeededData(async db => {
        // Create active category
        await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Active Category',
            type: 'expense',
            color: '#FF0000',
            isArchived: false,
          });

        // Create archived category
        await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Archived Category',
            type: 'income',
            color: '#00FF00',
            isArchived: true,
          });
      });

      const result = await getUserCategories(testUserId);
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Active Category');
      expect(result[0].isArchived).toBe(false);
    });

    it('should return categories ordered by displayOrder and createdAt', async () => {
      // Create test categories with different display orders
      await withSeededData(async db => {
        await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Second Category',
            type: 'expense',
            color: '#FF0000',
            displayOrder: 2,
            isArchived: false,
          });

        await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'First Category',
            type: 'income',
            color: '#00FF00',
            displayOrder: 1,
            isArchived: false,
          });
      });

      const result = await getUserCategories(testUserId);
      
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('First Category'); // Lower displayOrder comes first
      expect(result[1].name).toBe('Second Category');
    });

    it('should handle database errors gracefully', async () => {
      // Test with invalid user ID format to trigger database error
      const invalidUserId = 'invalid-uuid-format';
      
      await expect(getUserCategories(invalidUserId)).rejects.toThrow(DatabaseError);
    });
  });

  describe('getCategoryById', () => {
    beforeEach(async () => {
      // Create a test category for these tests
      await withSeededData(async db => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Test Category',
            type: 'expense',
            color: '#FF0000',
            icon: 'shopping',
            isArchived: false,
          })
          .returning();
        testCategoryId = category.id;
      });
    });

    it('should return category when found and belongs to user', async () => {
      const result = await getCategoryById(testCategoryId, testUserId);
      
      expect(result).not.toBeNull();
      expect(result!.id).toBe(testCategoryId);
      expect(result!.name).toBe('Test Category');
      expect(result!.type).toBe('expense');
    });

    it('should return null when category not found', async () => {
      const nonExistentId = faker.string.uuid();
      const result = await getCategoryById(nonExistentId, testUserId);
      
      expect(result).toBeNull();
    });

    it('should return null when category belongs to different user', async () => {
      const result = await getCategoryById(testCategoryId, testUser2Id);
      
      expect(result).toBeNull();
    });

    it('should return category even when archived', async () => {
      // Archive the category
      await withSeededData(async db => {
        await db
          .update(categories)
          .set({ isArchived: true })
          .where(eq(categories.id, testCategoryId));
      });

      const result = await getCategoryById(testCategoryId, testUserId);
      
      expect(result).not.toBeNull();
      expect(result!.isArchived).toBe(true);
    });
  });

  describe('createCategory', () => {
    it('should create category with minimal required data', async () => {
      const input: CreateCategoryInput = {
        name: 'New Category',
        type: 'expense',
        color: '#FF0000',
        displayOrder: 0,
      };

      const result = await createCategory(testUserId, input);
      testCategoryId = result.id;
      
      expect(result.id).toBeDefined();
      expect(result.name).toBe('New Category');
      expect(result.type).toBe('expense');
      expect(result.color).toBe('#FF0000');
      expect(result.isArchived).toBe(false);
    });

    it('should create category with all optional fields', async () => {
      const input: CreateCategoryInput = {
        name: 'Complete Category',
        type: 'income',
        color: '#00FF00',
        icon: 'salary',
        displayOrder: 1,
      };

      const result = await createCategory(testUserId, input);
      testCategoryId = result.id;
      
      expect(result.name).toBe('Complete Category');
      expect(result.type).toBe('income');
      expect(result.color).toBe('#00FF00');
      expect(result.icon).toBe('salary');
      expect(result.parentId).toBeNull();
    });

    it('should create subcategory with parent', async () => {
      // First create a parent category
      let parentCategoryId: string = '';
      await withSeededData(async db => {
        const [parentCategory] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Parent Category',
            type: 'expense',
            color: '#FF0000',
            displayOrder: 0,
            isArchived: false,
          })
          .returning();
        parentCategoryId = parentCategory.id;
      });

      const input: CreateCategoryInput = {
        name: 'Sub Category',
        type: 'expense',
        color: '#FF0000',
        displayOrder: 1,
        parentId: parentCategoryId,
      };

      const result = await createCategory(testUserId, input);
      testCategoryId = result.id;
      
      expect(result.name).toBe('Sub Category');
      expect(result.parentId).toBe(parentCategoryId);
    });

    it('should handle database errors gracefully', async () => {
      const input: CreateCategoryInput = {
        name: 'Test Category',
        type: 'expense',
        color: '#FF0000',
        displayOrder: 0,
      };

      // Use invalid user ID to trigger database error
      const invalidUserId = 'invalid-uuid-format';
      
      await expect(createCategory(invalidUserId, input)).rejects.toThrow(DatabaseError);
    });
  });

  describe('updateCategory', () => {
    beforeEach(async () => {
      // Create a test category for these tests
      await withSeededData(async db => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Original Category',
            type: 'expense',
            color: '#FF0000',
            icon: 'shopping',
            isArchived: false,
          })
          .returning();
        testCategoryId = category.id;
      });
    });

    it('should update single field', async () => {
      const input: UpdateCategoryInput = {
        name: 'Updated Name',
      };

      const result = await updateCategory(testCategoryId, testUserId, input);
      
      expect(result.name).toBe('Updated Name');
      expect(result.type).toBe('expense'); // Unchanged
      expect(result.color).toBe('#FF0000'); // Unchanged
    });

    it('should update multiple fields', async () => {
      const input: UpdateCategoryInput = {
        name: 'Updated Category',
        type: 'income',
        color: '#00FF00',
        icon: 'salary',
      };

      const result = await updateCategory(testCategoryId, testUserId, input);
      
      expect(result.name).toBe('Updated Category');
      expect(result.type).toBe('income');
      expect(result.color).toBe('#00FF00');
      expect(result.icon).toBe('salary');
    });

    it('should update updatedAt timestamp', async () => {
      const originalCategory = await getCategoryById(testCategoryId, testUserId);
      const originalUpdatedAt = originalCategory!.updatedAt;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      const input: UpdateCategoryInput = {
        name: 'Updated Name',
      };

      const result = await updateCategory(testCategoryId, testUserId, input);
      
      expect(new Date(result.updatedAt!).getTime()).toBeGreaterThan(new Date(originalUpdatedAt!).getTime());
    });

    it('should throw RecordNotFoundError when category does not exist', async () => {
      const nonExistentId = faker.string.uuid();
      const input: UpdateCategoryInput = {
        name: 'Updated Name',
      };

      await expect(updateCategory(nonExistentId, testUserId, input)).rejects.toThrow(RecordNotFoundError);
      await expect(updateCategory(nonExistentId, testUserId, input)).rejects.toThrow('Category not found');
    });

    it('should throw RecordNotFoundError when category belongs to different user', async () => {
      const input: UpdateCategoryInput = {
        name: 'Updated Name',
      };

      await expect(updateCategory(testCategoryId, testUser2Id, input)).rejects.toThrow(RecordNotFoundError);
    });

    it('should allow archiving category', async () => {
      const input: UpdateCategoryInput = {
        isArchived: true,
      };

      const result = await updateCategory(testCategoryId, testUserId, input);
      
      expect(result.isArchived).toBe(true);
    });
  });

  describe('deleteCategory', () => {
    beforeEach(async () => {
      // Create a test category for these tests
      await withSeededData(async db => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Category to Delete',
            type: 'expense',
            color: '#FF0000',
            isArchived: false,
          })
          .returning();
        testCategoryId = category.id;
      });
    });

    it('should soft delete category by archiving', async () => {
      const result = await deleteCategory(testCategoryId, testUserId);
      
      expect(result).toBe(true);

      // Verify category is archived, not deleted
      await withSeededData(async db => {
        const archivedCategory = await db
          .select()
          .from(categories)
          .where(eq(categories.id, testCategoryId))
          .limit(1);
        
        expect(archivedCategory).toHaveLength(1);
        expect(archivedCategory[0].isArchived).toBe(true);
      });
    });

    it('should throw RecordNotFoundError when category does not exist', async () => {
      const nonExistentId = faker.string.uuid();

      await expect(deleteCategory(nonExistentId, testUserId)).rejects.toThrow(RecordNotFoundError);
      await expect(deleteCategory(nonExistentId, testUserId)).rejects.toThrow('Category not found');
    });

    it('should throw RecordNotFoundError when category belongs to different user', async () => {
      await expect(deleteCategory(testCategoryId, testUser2Id)).rejects.toThrow(RecordNotFoundError);
    });

    it('should successfully archive already archived category', async () => {
      // First archive the category
      await withSeededData(async db => {
        await db
          .update(categories)
          .set({ isArchived: true })
          .where(eq(categories.id, testCategoryId));
      });

      // Should still return true when trying to archive an already archived category
      const result = await deleteCategory(testCategoryId, testUserId);
      expect(result).toBe(true);
    });
  });
}); 