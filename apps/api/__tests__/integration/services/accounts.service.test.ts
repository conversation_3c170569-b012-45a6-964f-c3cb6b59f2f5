import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
import { accounts, users } from '../../../src/database/schema.js';
import {
  getUserAccounts,
  getAccountById,
  createAccount,
  updateAccount,
  deleteAccount,
} from '../../../src/services/accounts/accounts.service.js';
import {
  DatabaseError,
  RecordNotFoundError,
} from '../../../src/utils/error-handler.js';
import { withSeededData } from '../../../src/test-utils/db.js';
import type { CreateAccountInput, UpdateAccountInput } from '../../../src/services/accounts/accounts.types.js';

describe('Integration Tests - Accounts Service', () => {
  let testUserId: string;
  let testUser2Id: string;
  let testAccountId: string;

  beforeEach(async () => {
    // Create test users that persist for the service functions
    await withSeededData(async db => {
      // Create first test user
      const [testUser] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUserId = testUser.id;

      // Create second test user for cross-user tests
      const [testUser2] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUser2Id = testUser2.id;
    });
  });

  afterEach(async () => {
    // Clean up test data
    await withSeededData(async db => {
      if (testAccountId) {
        await db.delete(accounts).where(eq(accounts.id, testAccountId));
      }
      await db.delete(accounts).where(eq(accounts.userId, testUserId));
      await db.delete(accounts).where(eq(accounts.userId, testUser2Id));
      await db.delete(users).where(eq(users.id, testUserId));
      await db.delete(users).where(eq(users.id, testUser2Id));
    });
  });

  describe('getUserAccounts', () => {
    it('should return empty array for user with no accounts', async () => {
      const result = await getUserAccounts(testUserId);
      expect(result).toEqual([]);
    });

    it('should return user accounts excluding archived ones', async () => {
      // Create test accounts
      await withSeededData(async db => {
        // Create active account
        await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Active Account',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000',
            currentBalance: '1000',
            isArchived: false,
          });

        // Create archived account
        await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Archived Account',
            type: 'savings',
            currency: 'USD',
            initialBalance: '500',
            currentBalance: '500',
            isArchived: true,
          });
      });

      const result = await getUserAccounts(testUserId);
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Active Account');
      expect(result[0].isArchived).toBe(false);
    });

    it('should return accounts ordered by displayOrder and createdAt', async () => {
      // Create test accounts with different display orders
      await withSeededData(async db => {
        await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Account 1',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000',
            currentBalance: '1000',
            displayOrder: 2,
            isArchived: false,
          });

        await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Account 2',
            type: 'savings',
            currency: 'USD',
            initialBalance: '500',
            currentBalance: '500',
            displayOrder: 1,
            isArchived: false,
          });
      });

      const result = await getUserAccounts(testUserId);
      
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Account 2'); // Lower displayOrder comes first
      expect(result[1].name).toBe('Account 1');
    });

    it('should handle database errors gracefully', async () => {
      // Test with invalid user ID format to trigger database error
      const invalidUserId = 'invalid-uuid-format';
      
      await expect(getUserAccounts(invalidUserId)).rejects.toThrow(DatabaseError);
    });
  });

  describe('getAccountById', () => {
    beforeEach(async () => {
      // Create a test account for these tests
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Test Account',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000',
            currentBalance: '1000',
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });
    });

    it('should return account when found and belongs to user', async () => {
      const result = await getAccountById(testAccountId, testUserId);
      
      expect(result).not.toBeNull();
      expect(result!.id).toBe(testAccountId);
      expect(result!.name).toBe('Test Account');
      expect(result!.type).toBe('checking');
    });

    it('should return null when account not found', async () => {
      const nonExistentId = faker.string.uuid();
      const result = await getAccountById(nonExistentId, testUserId);
      
      expect(result).toBeNull();
    });

    it('should return null when account belongs to different user', async () => {
      const result = await getAccountById(testAccountId, testUser2Id);
      
      expect(result).toBeNull();
    });

    it('should return null when account is archived', async () => {
      // Archive the account
      await withSeededData(async db => {
        await db
          .update(accounts)
          .set({ isArchived: true })
          .where(eq(accounts.id, testAccountId));
      });

      const result = await getAccountById(testAccountId, testUserId);
      
      expect(result).toBeNull();
    });
  });

  describe('createAccount', () => {
    it('should create account with minimal required data', async () => {
      const input: CreateAccountInput = {
        name: 'New Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: 0,
        includeInNetWorth: true,
        displayOrder: 0,
      };

      const result = await createAccount(testUserId, input);
      testAccountId = result.id;
      
      expect(result.id).toBeDefined();
      expect(result.name).toBe('New Account');
      expect(result.type).toBe('checking');
      expect(result.currency).toBe('USD');
      expect(result.initialBalance).toBe('0.0000');
      expect(result.currentBalance).toBe('0.0000');
      expect(result.includeInNetWorth).toBe(true);
      expect(result.isArchived).toBe(false);
    });

    it('should create account with all optional fields', async () => {
      const input: CreateAccountInput = {
        name: 'Complete Account',
        type: 'savings',
        currency: 'EUR',
        initialBalance: 1500.50,
        notes: 'Test notes',
        icon: 'bank',
        color: '#FF0000',
        includeInNetWorth: false,
        displayOrder: 5,
      };

      const result = await createAccount(testUserId, input);
      testAccountId = result.id;
      
      expect(result.name).toBe('Complete Account');
      expect(result.type).toBe('savings');
      expect(result.currency).toBe('EUR');
      expect(result.initialBalance).toBe('1500.5000');
      expect(result.currentBalance).toBe('1500.5000');
      expect(result.notes).toBe('Test notes');
      expect(result.icon).toBe('bank');
      expect(result.color).toBe('#FF0000');
      expect(result.includeInNetWorth).toBe(false);
      expect(result.displayOrder).toBe(5);
    });

    it('should handle zero initial balance', async () => {
      const input: CreateAccountInput = {
        name: 'Zero Balance Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: 0,
        includeInNetWorth: true,
        displayOrder: 0,
      };

      const result = await createAccount(testUserId, input);
      testAccountId = result.id;
      
      expect(result.initialBalance).toBe('0.0000');
      expect(result.currentBalance).toBe('0.0000');
    });

    it('should handle negative initial balance', async () => {
      const input: CreateAccountInput = {
        name: 'Credit Card',
        type: 'credit_card',
        currency: 'USD',
        initialBalance: -500.25,
        includeInNetWorth: true,
        displayOrder: 0,
      };

      const result = await createAccount(testUserId, input);
      testAccountId = result.id;
      
      expect(result.initialBalance).toBe('-500.2500');
      expect(result.currentBalance).toBe('-500.2500');
    });

    it('should handle database errors gracefully', async () => {
      const input: CreateAccountInput = {
        name: 'Test Account',
        type: 'checking',
        currency: 'USD',
        initialBalance: 0,
        includeInNetWorth: true,
        displayOrder: 0,
      };

      // Use invalid user ID to trigger database error
      const invalidUserId = 'invalid-uuid-format';
      
      await expect(createAccount(invalidUserId, input)).rejects.toThrow(DatabaseError);
    });
  });

  describe('updateAccount', () => {
    beforeEach(async () => {
      // Create a test account for these tests
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Original Account',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000',
            currentBalance: '1000',
            notes: 'Original notes',
            icon: 'bank',
            color: '#0000FF',
            includeInNetWorth: true,
            displayOrder: 1,
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });
    });

    it('should update single field', async () => {
      const input: UpdateAccountInput = {
        name: 'Updated Name',
      };

      const result = await updateAccount(testAccountId, testUserId, input);
      
      expect(result.name).toBe('Updated Name');
      expect(result.type).toBe('checking'); // Unchanged
      expect(result.currency).toBe('USD'); // Unchanged
    });

    it('should update multiple fields', async () => {
      const input: UpdateAccountInput = {
        name: 'Updated Account',
        type: 'savings',
        currency: 'EUR',
        notes: 'Updated notes',
        icon: 'wallet',
        color: '#FF0000',
        includeInNetWorth: false,
        displayOrder: 10,
      };

      const result = await updateAccount(testAccountId, testUserId, input);
      
      expect(result.name).toBe('Updated Account');
      expect(result.type).toBe('savings');
      expect(result.currency).toBe('EUR');
      expect(result.notes).toBe('Updated notes');
      expect(result.icon).toBe('wallet');
      expect(result.color).toBe('#FF0000');
      expect(result.includeInNetWorth).toBe(false);
      expect(result.displayOrder).toBe(10);
    });

    it('should update updatedAt timestamp', async () => {
      const originalAccount = await getAccountById(testAccountId, testUserId);
      const originalUpdatedAt = originalAccount!.updatedAt;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      const input: UpdateAccountInput = {
        name: 'Updated Name',
      };

      const result = await updateAccount(testAccountId, testUserId, input);
      
      expect(new Date(result.updatedAt!).getTime()).toBeGreaterThan(new Date(originalUpdatedAt!).getTime());
    });

    it('should throw RecordNotFoundError when account does not exist', async () => {
      const nonExistentId = faker.string.uuid();
      const input: UpdateAccountInput = {
        name: 'Updated Name',
      };

      await expect(updateAccount(nonExistentId, testUserId, input)).rejects.toThrow(RecordNotFoundError);
      await expect(updateAccount(nonExistentId, testUserId, input)).rejects.toThrow('Account not found');
    });

    it('should throw RecordNotFoundError when account belongs to different user', async () => {
      const input: UpdateAccountInput = {
        name: 'Updated Name',
      };

      await expect(updateAccount(testAccountId, testUser2Id, input)).rejects.toThrow(RecordNotFoundError);
    });

    it('should allow archiving account', async () => {
      const input: UpdateAccountInput = {
        isArchived: true,
      };

      const result = await updateAccount(testAccountId, testUserId, input);
      
      expect(result.isArchived).toBe(true);
    });
  });

  describe('deleteAccount', () => {
    beforeEach(async () => {
      // Create a test account for these tests
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Account to Delete',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000',
            currentBalance: '1000',
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });
    });

    it('should soft delete account by archiving', async () => {
      const result = await deleteAccount(testAccountId, testUserId);
      
      expect(result).toBe(true);

      // Verify account is archived, not deleted
      await withSeededData(async db => {
        const archivedAccount = await db
          .select()
          .from(accounts)
          .where(eq(accounts.id, testAccountId))
          .limit(1);
        
        expect(archivedAccount).toHaveLength(1);
        expect(archivedAccount[0].isArchived).toBe(true);
      });
    });

    it('should throw RecordNotFoundError when account does not exist', async () => {
      const nonExistentId = faker.string.uuid();

      await expect(deleteAccount(nonExistentId, testUserId)).rejects.toThrow(RecordNotFoundError);
      await expect(deleteAccount(nonExistentId, testUserId)).rejects.toThrow('Account not found');
    });

    it('should throw RecordNotFoundError when account belongs to different user', async () => {
      await expect(deleteAccount(testAccountId, testUser2Id)).rejects.toThrow(RecordNotFoundError);
    });

    it('should throw RecordNotFoundError when trying to delete already archived account', async () => {
      // First archive the account
      await withSeededData(async db => {
        await db
          .update(accounts)
          .set({ isArchived: true })
          .where(eq(accounts.id, testAccountId));
      });

      await expect(deleteAccount(testAccountId, testUserId)).rejects.toThrow(RecordNotFoundError);
    });
  });
}); 