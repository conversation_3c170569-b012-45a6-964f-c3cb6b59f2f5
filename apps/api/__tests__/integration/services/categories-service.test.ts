import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { eq } from 'drizzle-orm';
import { categories, users } from '../../../src/database/schema.js';
import { withSeededData } from '../../../src/test-utils/db.js';
import { faker } from '@faker-js/faker';
import {
  getUserCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryTree,
} from '../../../src/services/categories/categories.service.js';

describe('Integration Tests - Categories Service', () => {
  let testUserId: string;

  beforeEach(async () => {
    // Create a test user
    await withSeededData(async (db) => {
      const [testUser] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUserId = testUser.id;
    });
  });

  afterEach(async () => {
    // Clean up test data
    await withSeededData(async (db) => {
      await db.delete(categories).where(eq(categories.userId, testUserId));
      await db.delete(users).where(eq(users.id, testUserId));
    });
  });

  describe('getUserCategories', () => {
    it('should return empty array when user has no categories', async () => {
      const result = await getUserCategories(testUserId);
      expect(result).toEqual([]);
    });

    it('should return user categories when they exist', async () => {
      await withSeededData(async (db) => {
        // Create test categories
        const categoriesData = [
          {
            userId: testUserId,
            name: 'Groceries',
            type: 'expense' as const,
            icon: 'shopping-cart',
            color: '#FF5733',
          },
          {
            userId: testUserId,
            name: 'Salary',
            type: 'income' as const,
            icon: 'money',
            color: '#27AE60',
          },
        ];

        await db.insert(categories).values(categoriesData);
      });

      const result = await getUserCategories(testUserId);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Groceries');
      expect(result[0].type).toBe('expense');
      expect(result[1].name).toBe('Salary');
      expect(result[1].type).toBe('income');
    });

    it('should not return archived categories by default', async () => {
      await withSeededData(async (db) => {
        // Create active and archived categories
        const categoriesData = [
          {
            userId: testUserId,
            name: 'Active Category',
            type: 'expense' as const,
            icon: 'shopping',
            color: '#FF5733',
            isArchived: false,
          },
          {
            userId: testUserId,
            name: 'Archived Category',
            type: 'expense' as const,
            icon: 'archive',
            color: '#999999',
            isArchived: true,
          },
        ];

        await db.insert(categories).values(categoriesData);
      });

      const result = await getUserCategories(testUserId);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Active Category');
      expect(result[0].isArchived).toBe(false);
    });

    it('should return categories in display order', async () => {
      await withSeededData(async (db) => {
        // Create categories with different display orders
        const categoriesData = [
          {
            userId: testUserId,
            name: 'Third Category',
            type: 'expense' as const,
            icon: 'third',
            color: '#FF5733',
            displayOrder: 3,
          },
          {
            userId: testUserId,
            name: 'First Category',
            type: 'expense' as const,
            icon: 'first',
            color: '#27AE60',
            displayOrder: 1,
          },
          {
            userId: testUserId,
            name: 'Second Category',
            type: 'expense' as const,
            icon: 'second',
            color: '#3498DB',
            displayOrder: 2,
          },
        ];

        await db.insert(categories).values(categoriesData);
      });

      const result = await getUserCategories(testUserId);

      expect(result).toHaveLength(3);
      expect(result[0].name).toBe('First Category');
      expect(result[1].name).toBe('Second Category');
      expect(result[2].name).toBe('Third Category');
    });
  });

  describe('getCategoryById', () => {
    it('should return category by ID when it exists and belongs to user', async () => {
      let categoryId: string = '';

      await withSeededData(async (db) => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Test Category',
            type: 'expense',
            icon: 'test',
            color: '#FF5733',
          })
          .returning();
        categoryId = category.id;
      });

      const result = await getCategoryById(categoryId, testUserId);

      expect(result).toBeDefined();
      expect(result?.id).toBe(categoryId);
      expect(result?.name).toBe('Test Category');
      expect(result?.type).toBe('expense');
    });

    it('should throw error when category ID is invalid UUID', async () => {
      await expect(getCategoryById('non-existent-id', testUserId)).rejects.toThrow();
    });

    it('should return null when category belongs to different user', async () => {
      let categoryId: string = '';
      let otherUserId: string = '';

      await withSeededData(async (db) => {
        // Create another user and their category
        const [otherUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();
        otherUserId = otherUser.id;

        const [otherCategory] = await db
          .insert(categories)
          .values({
            userId: otherUserId,
            name: 'Other User Category',
            type: 'expense',
            icon: 'other',
            color: '#FF5733',
          })
          .returning();
        categoryId = otherCategory.id;
      });

      const result = await getCategoryById(categoryId, testUserId);
      expect(result).toBeNull();

      // Clean up
      await withSeededData(async (db) => {
        await db.delete(categories).where(eq(categories.id, categoryId));
        await db.delete(users).where(eq(users.id, otherUserId));
      });
    });
  });

  describe('createCategory', () => {
    it('should create a new category with valid data', async () => {
      const input = {
        name: 'New Expense Category',
        type: 'expense' as const,
        icon: 'shopping',
        color: '#FF5733',
      };

      const result = await createCategory(testUserId, input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);
      expect(result.type).toBe(input.type);
      expect(result.icon).toBe(input.icon);
      expect(result.color).toBe(input.color);
      expect(result.userId).toBe(testUserId);
      expect(result.isArchived).toBe(false);

      // Verify it was actually created in the database
      await withSeededData(async (db) => {
        const dbCategory = await db.select().from(categories).where(eq(categories.id, result.id)).limit(1);
        expect(dbCategory).toHaveLength(1);
        expect(dbCategory[0].name).toBe(input.name);
      });
    });

    it('should create category with minimal required fields', async () => {
      const input = {
        name: 'Minimal Category',
        type: 'income' as const,
      };

      const result = await createCategory(testUserId, input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);
      expect(result.type).toBe(input.type);
      expect(result.userId).toBe(testUserId);
    });

    it('should handle different category types', async () => {
      const categoryTypes = ['income', 'expense'] as const;

      for (const type of categoryTypes) {
        const input = {
          name: `${type} Category`,
          type,
        };

        const result = await createCategory(testUserId, input);

        expect(result).toBeDefined();
        expect(result.type).toBe(type);
        expect(result.name).toBe(`${type} Category`);
      }
    });

    it('should create hierarchical categories (parent-child)', async () => {
      // Create parent category first
      const parentInput = {
        name: 'Transportation',
        type: 'expense' as const,
        icon: 'car',
        color: '#3498DB',
      };

      const parentCategory = await createCategory(testUserId, parentInput);

      // Create child category
      const childInput = {
        name: 'Gas',
        type: 'expense' as const,
        icon: 'gas-pump',
        color: '#E74C3C',
        parentId: parentCategory.id,
      };

      const childCategory = await createCategory(testUserId, childInput);

      expect(childCategory.parentId).toBe(parentCategory.id);

      // Verify relationship in database
      await withSeededData(async (db) => {
        const childCategories = await db.select().from(categories).where(eq(categories.parentId, parentCategory.id));
        expect(childCategories).toHaveLength(1);
        expect(childCategories[0].id).toBe(childCategory.id);
      });
    });
  });

  describe('updateCategory', () => {
    it('should update category with valid data', async () => {
      let categoryId: string = '';

      await withSeededData(async (db) => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Original Name',
            type: 'expense',
            icon: 'original',
            color: '#FF5733',
          })
          .returning();
        categoryId = category.id;
      });

      const updateInput = {
        name: 'Updated Name',
        icon: 'updated-icon',
        color: '#27AE60',
      };

      const result = await updateCategory(categoryId, testUserId, updateInput);

      expect(result).toBeDefined();
      expect(result.name).toBe(updateInput.name);
      expect(result.icon).toBe(updateInput.icon);
      expect(result.color).toBe(updateInput.color);
      expect(result.type).toBe('expense'); // Unchanged
    });

    it('should throw error when updating non-existent category', async () => {
      const updateInput = {
        name: 'Updated Name',
      };

      await expect(updateCategory('non-existent-id', testUserId, updateInput)).rejects.toThrow();
    });

    it('should throw error when updating category belonging to different user', async () => {
      let categoryId: string = '';
      let otherUserId: string = '';

      await withSeededData(async (db) => {
        // Create another user and their category
        const [otherUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();
        otherUserId = otherUser.id;

        const [otherCategory] = await db
          .insert(categories)
          .values({
            userId: otherUserId,
            name: 'Other User Category',
            type: 'expense',
            icon: 'other',
            color: '#FF5733',
          })
          .returning();
        categoryId = otherCategory.id;
      });

      const updateInput = {
        name: 'Hacked Name',
      };

      await expect(updateCategory(categoryId, testUserId, updateInput)).rejects.toThrow();

      // Verify the category was not actually updated
      await withSeededData(async (db) => {
        const unchangedCategory = await db.select().from(categories).where(eq(categories.id, categoryId)).limit(1);
        expect(unchangedCategory[0].name).toBe('Other User Category');

        // Clean up
        await db.delete(categories).where(eq(categories.id, categoryId));
        await db.delete(users).where(eq(users.id, otherUserId));
      });
    });
  });

  describe('deleteCategory', () => {
    it('should archive category instead of deleting', async () => {
      let categoryId: string = '';

      await withSeededData(async (db) => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Category to Delete',
            type: 'expense',
            icon: 'delete',
            color: '#FF5733',
          })
          .returning();
        categoryId = category.id;
      });

      const result = await deleteCategory(categoryId, testUserId);

      expect(result).toBe(true);

      // Verify category is archived, not deleted
      await withSeededData(async (db) => {
        const archivedCategory = await db.select().from(categories).where(eq(categories.id, categoryId)).limit(1);
        expect(archivedCategory).toHaveLength(1);
        expect(archivedCategory[0].isArchived).toBe(true);
      });
    });

    it('should throw error when deleting non-existent category', async () => {
      await expect(deleteCategory('non-existent-id', testUserId)).rejects.toThrow();
    });

    it('should throw error when deleting category belonging to different user', async () => {
      let categoryId: string = '';
      let otherUserId: string = '';

      await withSeededData(async (db) => {
        // Create another user and their category
        const [otherUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();
        otherUserId = otherUser.id;

        const [otherCategory] = await db
          .insert(categories)
          .values({
            userId: otherUserId,
            name: 'Other User Category',
            type: 'expense',
            icon: 'other',
            color: '#FF5733',
          })
          .returning();
        categoryId = otherCategory.id;
      });

      await expect(deleteCategory(categoryId, testUserId)).rejects.toThrow();

      // Verify the category was not archived
      await withSeededData(async (db) => {
        const unchangedCategory = await db.select().from(categories).where(eq(categories.id, categoryId)).limit(1);
        expect(unchangedCategory[0].isArchived).toBe(false);

        // Clean up
        await db.delete(categories).where(eq(categories.id, categoryId));
        await db.delete(users).where(eq(users.id, otherUserId));
      });
    });
  });

  describe('getCategoryTree', () => {
    it('should return empty array when user has no categories', async () => {
      const result = await getCategoryTree(testUserId);
      expect(result).toEqual([]);
    });

    it('should return flat structure when no hierarchical categories exist', async () => {
      await withSeededData(async (db) => {
        const categoriesData = [
          {
            userId: testUserId,
            name: 'Category 1',
            type: 'expense' as const,
            icon: 'icon1',
            color: '#FF5733',
          },
          {
            userId: testUserId,
            name: 'Category 2',
            type: 'income' as const,
            icon: 'icon2',
            color: '#27AE60',
          },
        ];

        await db.insert(categories).values(categoriesData);
      });

      const result = await getCategoryTree(testUserId);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Category 1');
      expect(result[1].name).toBe('Category 2');
    });

    it('should return hierarchical structure with parent-child relationships', async () => {
      await withSeededData(async (db) => {
        // Create parent category
        const [parentCategory] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Transportation',
            type: 'expense',
            icon: 'car',
            color: '#3498DB',
          })
          .returning();

        // Create child categories
        const childCategoriesData = [
          {
            userId: testUserId,
            name: 'Gas',
            type: 'expense' as const,
            icon: 'gas-pump',
            color: '#E74C3C',
            parentId: parentCategory.id,
          },
          {
            userId: testUserId,
            name: 'Parking',
            type: 'expense' as const,
            icon: 'parking',
            color: '#F39C12',
            parentId: parentCategory.id,
          },
        ];

        await db.insert(categories).values(childCategoriesData);
      });

      const result = await getCategoryTree(testUserId);

      expect(result).toHaveLength(1); // Only parent category at root level
      expect(result[0].name).toBe('Transportation');
      expect(result[0].children).toHaveLength(2);
      expect(result[0].children?.[0].name).toBe('Gas');
      expect(result[0].children?.[1].name).toBe('Parking');
    });
  });
}); 