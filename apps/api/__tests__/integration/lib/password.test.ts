import { describe, it, expect } from 'vitest';
import {
  hashPassword,
  verifyPassword,
  generateRandomPassword,
} from '../../../src/lib/auth/password.js';

describe('Integration Tests - Password Utilities', () => {
  describe('Password Hashing', () => {
    it('should hash a password', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50); // bcrypt hashes are typically 60 chars
    });

    it('should generate different hashes for the same password', async () => {
      const password = 'TestPassword123!';
      const hash1 = await hashPassword(password);
      const hash2 = await hashPassword(password);
      
      expect(hash1).not.toBe(hash2); // bcrypt uses salt, so hashes should be different
    });

    it('should handle empty password', async () => {
      const password = '';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
    });

    it('should handle very long password', async () => {
      const password = 'a'.repeat(1000);
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
    });

    it('should handle special characters in password', async () => {
      const password = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
    });

    it('should handle unicode characters in password', async () => {
      const password = 'пароль123🔒';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
    });
  });

  describe('Password Verification', () => {
    it('should verify correct password', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const password = 'TestPassword123!';
      const wrongPassword = 'WrongPassword123!';
      const hash = await hashPassword(password);
      
      const isValid = await verifyPassword(wrongPassword, hash);
      
      expect(isValid).toBe(false);
    });

    it('should reject empty password against valid hash', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      const isValid = await verifyPassword('', hash);
      
      expect(isValid).toBe(false);
    });

    it('should handle empty password with empty hash', async () => {
      const password = '';
      const hash = await hashPassword(password);
      
      const isValid = await verifyPassword('', hash);
      
      expect(isValid).toBe(true);
    });

    it('should reject password against invalid hash', async () => {
      const password = 'TestPassword123!';
      const invalidHash = 'invalid-hash';
      
      const isValid = await verifyPassword(password, invalidHash);
      
      expect(isValid).toBe(false);
    });

    it('should handle case sensitivity', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      const isValidLower = await verifyPassword(password.toLowerCase(), hash);
      const isValidUpper = await verifyPassword(password.toUpperCase(), hash);
      
      expect(isValidLower).toBe(false);
      expect(isValidUpper).toBe(false);
    });

    it('should handle whitespace differences', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      const isValidWithSpaces = await verifyPassword(` ${password} `, hash);
      const isValidWithTabs = await verifyPassword(`\t${password}\t`, hash);
      
      expect(isValidWithSpaces).toBe(false);
      expect(isValidWithTabs).toBe(false);
    });
  });

  describe('Random Password Generation', () => {
    it('should generate password with default length', () => {
      const password = generateRandomPassword();
      
      expect(password).toBeDefined();
      expect(typeof password).toBe('string');
      expect(password.length).toBe(12); // Default length
    });

    it('should generate password with custom length', () => {
      const length = 20;
      const password = generateRandomPassword(length);
      
      expect(password).toBeDefined();
      expect(typeof password).toBe('string');
      expect(password.length).toBe(length);
    });

    it('should generate different passwords on each call', () => {
      const password1 = generateRandomPassword();
      const password2 = generateRandomPassword();
      
      expect(password1).not.toBe(password2);
    });

    it('should generate password with minimum length', () => {
      const password = generateRandomPassword(1);
      
      expect(password).toBeDefined();
      expect(password.length).toBe(1);
    });

    it('should generate long password', () => {
      const length = 100;
      const password = generateRandomPassword(length);
      
      expect(password).toBeDefined();
      expect(password.length).toBe(length);
    });

    it('should contain only valid characters', () => {
      const password = generateRandomPassword(50);
      // This regex matches the exact charset used in generateRandomPassword
      const validCharset = /^[a-zA-Z0-9!@#$%^&*()_+~`|}{[\]:;?><,.\/\-=]+$/;
      
      expect(password).toMatch(validCharset);
    });

    it('should handle zero length', () => {
      const password = generateRandomPassword(0);
      
      expect(password).toBeDefined();
      expect(password.length).toBe(0);
      expect(password).toBe('');
    });
  });

  describe('Password Lifecycle Integration', () => {
    it('should complete full password lifecycle: generate -> hash -> verify', async () => {
      // Generate random password
      const password = generateRandomPassword(16);
      
      // Hash the password
      const hash = await hashPassword(password);
      
      // Verify the password
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });

    it('should handle multiple password operations', async () => {
      const passwords = [
        'Password1!',
        'AnotherPass2@',
        'ThirdPassword3#',
      ];
      
      const hashes = await Promise.all(
        passwords.map(password => hashPassword(password))
      );
      
      // Verify each password against its hash
      for (let i = 0; i < passwords.length; i++) {
        const isValid = await verifyPassword(passwords[i], hashes[i]);
        expect(isValid).toBe(true);
      }
      
      // Verify passwords don't match other hashes
      for (let i = 0; i < passwords.length; i++) {
        for (let j = 0; j < hashes.length; j++) {
          if (i !== j) {
            const isValid = await verifyPassword(passwords[i], hashes[j]);
            expect(isValid).toBe(false);
          }
        }
      }
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle concurrent password operations', async () => {
      const password = 'ConcurrentTest123!';
      
      // Run multiple hash operations concurrently
      const hashPromises = Array(5).fill(null).map(() => hashPassword(password));
      const hashes = await Promise.all(hashPromises);
      
      // All hashes should be different but verify the same password
      for (let i = 0; i < hashes.length; i++) {
        for (let j = i + 1; j < hashes.length; j++) {
          expect(hashes[i]).not.toBe(hashes[j]);
        }
        
        const isValid = await verifyPassword(password, hashes[i]);
        expect(isValid).toBe(true);
      }
    });

    it('should handle very short passwords', async () => {
      const password = 'a';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });

    it('should handle passwords with only numbers', async () => {
      const password = '123456789';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });

    it('should handle passwords with only special characters', async () => {
      const password = '!@#$%^&*()';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });
  });
}); 