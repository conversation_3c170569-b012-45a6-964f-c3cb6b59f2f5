import { describe, it, expect, beforeEach } from 'vitest';
import {
  generateToken,
  verifyToken,
  extractTokenFromHeader,
  generateRefreshToken,
  type JwtPayload,
} from '../../../src/lib/auth/jwt.js';
import { authConfig } from '../../../src/config/auth.js';

describe('Integration Tests - JWT Utilities', () => {
  let testPayload: JwtPayload;

  beforeEach(() => {
    testPayload = {
      userId: 'test-user-123',
      email: '<EMAIL>',
      role: 'user',
    };
  });

  describe('Token Generation', () => {
    it('should generate a valid JWT token', () => {
      const token = generateToken(testPayload);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts
    });

    it('should generate different tokens for different payloads', () => {
      const payload1 = { userId: 'user1', email: '<EMAIL>', role: 'user' };
      const payload2 = { userId: 'user2', email: '<EMAIL>', role: 'user' };
      
      const token1 = generateToken(payload1);
      const token2 = generateToken(payload2);
      
      expect(token1).not.toBe(token2);
    });

    it('should generate refresh token', () => {
      const refreshToken = generateRefreshToken('test-user-123');
      
      expect(refreshToken).toBeDefined();
      expect(typeof refreshToken).toBe('string');
      expect(refreshToken.split('.')).toHaveLength(3);
    });
  });

  describe('Token Verification', () => {
    it('should verify a valid token', () => {
      const token = generateToken(testPayload);
      const decoded = verifyToken(token);
      
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(testPayload.userId);
      expect(decoded?.email).toBe(testPayload.email);
    });

    it('should return null for invalid token', () => {
      const invalidToken = 'invalid.token.here';
      const decoded = verifyToken(invalidToken);
      
      expect(decoded).toBeNull();
    });

    it('should return null for malformed token', () => {
      const malformedToken = 'not-a-jwt-token';
      const decoded = verifyToken(malformedToken);
      
      expect(decoded).toBeNull();
    });

    it('should return null for empty token', () => {
      const decoded = verifyToken('');
      
      expect(decoded).toBeNull();
    });

    it('should handle token with wrong signature', () => {
      // Generate token with different secret
      const fakeToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.wrong_signature';
      const decoded = verifyToken(fakeToken);
      
      expect(decoded).toBeNull();
    });
  });

  describe('Token Header Extraction', () => {
    it('should extract token from valid Bearer header', () => {
      const token = 'valid-jwt-token';
      const authHeader = `Bearer ${token}`;
      
      const extracted = extractTokenFromHeader(authHeader);
      
      expect(extracted).toBe(token);
    });

    it('should return null for missing header', () => {
      const extracted = extractTokenFromHeader(undefined);
      
      expect(extracted).toBeNull();
    });

    it('should return null for empty header', () => {
      const extracted = extractTokenFromHeader('');
      
      expect(extracted).toBeNull();
    });

    it('should return null for invalid format', () => {
      const extracted = extractTokenFromHeader('InvalidFormat token');
      
      expect(extracted).toBeNull();
    });

    it('should return null for missing token part', () => {
      const extracted = extractTokenFromHeader('Bearer');
      
      expect(extracted).toBeNull();
    });

    it('should return null for missing Bearer prefix', () => {
      const extracted = extractTokenFromHeader('token-without-bearer');
      
      expect(extracted).toBeNull();
    });

    it('should return null for extra spaces between Bearer and token', () => {
      const token = 'valid-jwt-token';
      const authHeader = `Bearer  ${token}`; // Two spaces
      
      const extracted = extractTokenFromHeader(authHeader);
      
      // Current implementation doesn't handle multiple spaces
      expect(extracted).toBeNull();
    });
  });

  describe('Token Lifecycle Integration', () => {
    it('should complete full token lifecycle: generate -> extract -> verify', () => {
      // Generate token
      const token = generateToken(testPayload);
      
      // Simulate authorization header
      const authHeader = `Bearer ${token}`;
      
      // Extract token
      const extractedToken = extractTokenFromHeader(authHeader);
      expect(extractedToken).toBe(token);
      
      // Verify token
      const decoded = verifyToken(extractedToken!);
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(testPayload.userId);
      expect(decoded?.email).toBe(testPayload.email);
    });

    it('should handle refresh token lifecycle', () => {
      const userId = 'test-user-123';
      
      // Generate refresh token
      const refreshToken = generateRefreshToken(userId);
      
      // Verify refresh token
      const decoded = verifyToken(refreshToken);
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(userId);
    });
  });

  describe('Token Expiration', () => {
    it('should include expiration in token payload', () => {
      const token = generateToken(testPayload);
      const decoded = verifyToken(token);
      
      expect(decoded).toBeDefined();
      expect(decoded?.exp).toBeDefined();
      expect(typeof decoded?.exp).toBe('number');
      
      // Should expire in the future
      const now = Math.floor(Date.now() / 1000);
      expect(decoded!.exp!).toBeGreaterThan(now);
    });

    it('should include issued at time in token payload', () => {
      const token = generateToken(testPayload);
      const decoded = verifyToken(token);
      
      expect(decoded).toBeDefined();
      expect(decoded?.iat).toBeDefined();
      expect(typeof decoded?.iat).toBe('number');
      
      // Should be issued recently
      const now = Math.floor(Date.now() / 1000);
      expect(decoded!.iat!).toBeLessThanOrEqual(now);
    });
  });

  describe('Error Handling', () => {
    it('should handle token verification with corrupted payload', () => {
      // Create a token with corrupted middle part
      const validToken = generateToken(testPayload);
      const parts = validToken.split('.');
      const corruptedToken = `${parts[0]}.corrupted_payload.${parts[2]}`;
      
      const decoded = verifyToken(corruptedToken);
      expect(decoded).toBeNull();
    });

    it('should handle token verification with corrupted header', () => {
      // Create a token with corrupted header
      const validToken = generateToken(testPayload);
      const parts = validToken.split('.');
      const corruptedToken = `corrupted_header.${parts[1]}.${parts[2]}`;
      
      const decoded = verifyToken(corruptedToken);
      expect(decoded).toBeNull();
    });

    it('should handle token verification with corrupted signature', () => {
      // Create a token with corrupted signature
      const validToken = generateToken(testPayload);
      const parts = validToken.split('.');
      const corruptedToken = `${parts[0]}.${parts[1]}.corrupted_signature`;
      
      const decoded = verifyToken(corruptedToken);
      expect(decoded).toBeNull();
    });
  });
}); 