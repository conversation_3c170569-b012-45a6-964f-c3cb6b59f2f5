import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ZodError, z } from 'zod';
import { GraphQLError } from 'graphql';
import {
  AppError,
  AuthenticationError,
  InvalidCredentialsError,
  TokenExpiredError,
  InvalidTokenError,
  UserNotFoundError,
  EmailAlreadyExistsError,
  WeakPasswordError,
  OAuthError,
  DatabaseError,
  RecordNotFoundError,
  ValidationError,
  ErrorCode,
  formatZodError,
  errorFormatter,
  handleErrors,
} from '../../../src/utils/error-handler.js';

describe('Integration Tests - Error Handler Utilities', () => {
  describe('Error Classes', () => {
    describe('AppError', () => {
      it('should create AppError with all properties', () => {
        const details = { field: 'test' };
        const error = new AppError(
          'Test error',
          ErrorCode.VALIDATION_ERROR,
          400,
          details
        );

        expect(error.message).toBe('Test error');
        expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
        expect(error.statusCode).toBe(400);
        expect(error.details).toEqual(details);
        expect(error.name).toBe('AppError');
        expect(error.stack).toBeDefined();
      });

      it('should create AppError with default status code', () => {
        const error = new AppError('Test error', ErrorCode.UNKNOWN_ERROR);

        expect(error.statusCode).toBe(500);
        expect(error.details).toBeUndefined();
      });

      it('should convert to GraphQL error', () => {
        const details = { field: 'test' };
        const error = new AppError(
          'Test error',
          ErrorCode.VALIDATION_ERROR,
          400,
          details
        );

        const graphqlError = error.toGraphQLError();

        expect(graphqlError).toBeInstanceOf(GraphQLError);
        expect(graphqlError.message).toBe('Test error');
        expect(graphqlError.extensions?.code).toBe(ErrorCode.VALIDATION_ERROR);
        expect(graphqlError.extensions?.statusCode).toBe(400);
        expect(graphqlError.extensions?.details).toEqual(details);
      });
    });

    describe('AuthenticationError', () => {
      it('should create AuthenticationError with default code', () => {
        const error = new AuthenticationError('Auth failed');

        expect(error.message).toBe('Auth failed');
        expect(error.code).toBe(ErrorCode.UNAUTHORIZED);
        expect(error.statusCode).toBe(401);
        expect(error).toBeInstanceOf(AppError);
      });

      it('should create AuthenticationError with custom code', () => {
        const error = new AuthenticationError(
          'Token expired',
          ErrorCode.TOKEN_EXPIRED
        );

        expect(error.code).toBe(ErrorCode.TOKEN_EXPIRED);
      });
    });

    describe('InvalidCredentialsError', () => {
      it('should create with default message', () => {
        const error = new InvalidCredentialsError();

        expect(error.message).toBe('Invalid email or password');
        expect(error.code).toBe(ErrorCode.INVALID_CREDENTIALS);
        expect(error.statusCode).toBe(401);
      });

      it('should create with custom message and details', () => {
        const details = { attempts: 3 };
        const error = new InvalidCredentialsError('Custom message', details);

        expect(error.message).toBe('Custom message');
        expect(error.details).toEqual(details);
      });
    });

    describe('TokenExpiredError', () => {
      it('should create with default message', () => {
        const error = new TokenExpiredError();

        expect(error.message).toBe('Authentication token has expired');
        expect(error.code).toBe(ErrorCode.TOKEN_EXPIRED);
      });
    });

    describe('InvalidTokenError', () => {
      it('should create with default message', () => {
        const error = new InvalidTokenError();

        expect(error.message).toBe('Invalid authentication token');
        expect(error.code).toBe(ErrorCode.INVALID_TOKEN);
      });
    });

    describe('UserNotFoundError', () => {
      it('should create with default message', () => {
        const error = new UserNotFoundError();

        expect(error.message).toBe('User not found');
        expect(error.code).toBe(ErrorCode.USER_NOT_FOUND);
      });
    });

    describe('EmailAlreadyExistsError', () => {
      it('should create with default message', () => {
        const error = new EmailAlreadyExistsError();

        expect(error.message).toBe('Email already exists');
        expect(error.code).toBe(ErrorCode.EMAIL_ALREADY_EXISTS);
        expect(error.statusCode).toBe(409);
      });
    });

    describe('WeakPasswordError', () => {
      it('should create with default message', () => {
        const error = new WeakPasswordError();

        expect(error.message).toBe('Password does not meet security requirements');
        expect(error.code).toBe(ErrorCode.WEAK_PASSWORD);
        expect(error.statusCode).toBe(400);
      });
    });

    describe('OAuthError', () => {
      it('should create with default message', () => {
        const error = new OAuthError();

        expect(error.message).toBe('OAuth authentication failed');
        expect(error.code).toBe(ErrorCode.OAUTH_ERROR);
        expect(error.statusCode).toBe(401);
      });
    });

    describe('DatabaseError', () => {
      it('should create with default message', () => {
        const error = new DatabaseError();

        expect(error.message).toBe('Database operation failed');
        expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
        expect(error.statusCode).toBe(500);
      });
    });

    describe('RecordNotFoundError', () => {
      it('should create with default message', () => {
        const error = new RecordNotFoundError();

        expect(error.message).toBe('Record not found');
        expect(error.code).toBe(ErrorCode.RECORD_NOT_FOUND);
        expect(error.statusCode).toBe(404);
      });
    });

    describe('ValidationError', () => {
      it('should create with default message', () => {
        const error = new ValidationError();

        expect(error.message).toBe('Validation failed');
        expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
        expect(error.statusCode).toBe(400);
      });
    });
  });

  describe('formatZodError', () => {
    it('should format Zod validation error', () => {
      const schema = z.object({
        email: z.string().email(),
        age: z.number().min(18),
      });

      try {
        schema.parse({ email: 'invalid-email', age: 15 });
      } catch (zodError) {
        const formattedError = formatZodError(zodError as ZodError);

        expect(formattedError).toBeInstanceOf(ValidationError);
        expect(formattedError.message).toBe('Validation failed');
        expect(formattedError.code).toBe(ErrorCode.VALIDATION_ERROR);
        expect(formattedError.statusCode).toBe(400);
        expect(formattedError.details?.errors).toHaveLength(2);
        
        const errors = formattedError.details?.errors as Array<{ path: string; message: string }>;
        expect(errors[0].path).toBe('email');
        expect(errors[0].message).toContain('email');
        expect(errors[1].path).toBe('age');
        expect(errors[1].message).toContain('18');
      }
    });

    it('should handle nested object validation errors', () => {
      const schema = z.object({
        user: z.object({
          profile: z.object({
            name: z.string().min(1),
          }),
        }),
      });

      try {
        schema.parse({ user: { profile: { name: '' } } });
      } catch (zodError) {
        const formattedError = formatZodError(zodError as ZodError);
        const errors = formattedError.details?.errors as Array<{ path: string; message: string }>;
        
        expect(errors[0].path).toBe('user.profile.name');
      }
    });
  });

  describe('errorFormatter', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('should return execution as-is when no errors', () => {
      const execution = { data: { test: 'value' } };
      const result = errorFormatter(execution, {});

      expect(result).toEqual(execution);
    });

    it('should format AppError correctly', () => {
      const appError = new ValidationError('Test validation error', { field: 'email' });
      const execution = {
        data: null,
        errors: [{
          message: 'Test validation error',
          originalError: appError,
          locations: [{ line: 1, column: 1 }],
          path: ['test'],
        }],
      };

      const result = errorFormatter(execution, {});

      expect(result.statusCode).toBe(400);
      expect(result.response?.errors).toHaveLength(1);
      expect(result.response?.errors?.[0].message).toBe('Test validation error');
      expect(result.response?.errors?.[0].extensions.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(result.response?.errors?.[0].extensions.statusCode).toBe(400);
      expect(result.response?.errors?.[0].extensions.details).toEqual({ field: 'email' });
    });

    it('should format ZodError correctly', () => {
      const schema = z.object({ email: z.string().email() });
      let zodError: ZodError;
      
      try {
        schema.parse({ email: 'invalid' });
      } catch (error) {
        zodError = error as ZodError;
      }

      const execution = {
        data: null,
        errors: [{
          message: 'Validation failed',
          originalError: zodError!,
          locations: [{ line: 1, column: 1 }],
          path: ['input'],
        }],
      };

      const result = errorFormatter(execution, {});

      expect(result.statusCode).toBe(400);
      expect(result.response?.errors?.[0].extensions.code).toBe(ErrorCode.VALIDATION_ERROR);
    });

    it('should handle unknown errors in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const execution = {
        data: null,
        errors: [{
          message: 'Sensitive internal error',
          extensions: { code: 'UNKNOWN_CODE' },
        }],
      };

      const result = errorFormatter(execution, {});

      expect(result.response?.errors?.[0].message).toBe('An unexpected error occurred');
      
      process.env.NODE_ENV = originalEnv;
    });

    it('should preserve error message in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const execution = {
        data: null,
        errors: [{
          message: 'Detailed error message',
          extensions: { code: 'UNKNOWN_CODE' },
        }],
      };

      const result = errorFormatter(execution, {});

      expect(result.response?.errors?.[0].message).toBe('Detailed error message');
      
      process.env.NODE_ENV = originalEnv;
    });

    it('should handle errors with known error codes', () => {
      const execution = {
        data: null,
        errors: [{
          message: 'User not found',
          extensions: { code: ErrorCode.USER_NOT_FOUND },
        }],
      };

      const result = errorFormatter(execution, {});

      expect(result.statusCode).toBe(404);
      expect(result.response?.errors?.[0].extensions.statusCode).toBe(404);
    });
  });

  describe('handleErrors', () => {
    it('should handle empty errors array', () => {
      const result = handleErrors([], {}, {});

      expect(result.statusCode).toBe(500);
      expect(result.response?.errors).toHaveLength(1);
      expect(result.response?.errors?.[0].message).toBe('Unknown error');
      expect(result.response?.errors?.[0].extensions.code).toBe(ErrorCode.UNKNOWN_ERROR);
    });

    it('should handle AppError', () => {
      const appError = new RecordNotFoundError('Account not found');
      const errors = [{
        message: 'Account not found',
        originalError: appError,
      }];

      const result = handleErrors(errors, {}, {});

      expect(result.statusCode).toBe(404);
      expect(result.response?.errors?.[0].message).toBe('Account not found');
      expect(result.response?.errors?.[0].extensions.code).toBe(ErrorCode.RECORD_NOT_FOUND);
    });

    it('should handle generic errors', () => {
      const errors = [{
        message: 'Generic error',
        extensions: { code: ErrorCode.INTERNAL_SERVER_ERROR },
      }];

      const result = handleErrors(errors, {}, {});

      expect(result.statusCode).toBe(500);
      expect(result.response?.errors?.[0].message).toBe('Generic error');
    });

    it('should mask sensitive errors in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const errors = [{
        message: 'Sensitive database connection details',
        extensions: { code: 'UNKNOWN_CODE' },
      }];

      const result = handleErrors(errors, {}, {});

      expect(result.response?.errors?.[0].message).toBe('An unexpected error occurred');
      
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Error Code Status Mapping', () => {
    it('should map authentication errors to 401', () => {
      const errors = [
        new InvalidCredentialsError(),
        new TokenExpiredError(),
        new InvalidTokenError(),
        new AuthenticationError('Test'),
      ];

      errors.forEach(error => {
        expect(error.statusCode).toBe(401);
      });
    });

    it('should map not found errors to correct status codes', () => {
      // UserNotFoundError extends AuthenticationError (401)
      const userError = new UserNotFoundError();
      expect(userError.statusCode).toBe(401);
      
      // RecordNotFoundError extends AppError with 404
      const recordError = new RecordNotFoundError();
      expect(recordError.statusCode).toBe(404);
    });

    it('should map conflict errors to 409', () => {
      const error = new EmailAlreadyExistsError();
      expect(error.statusCode).toBe(409);
    });

    it('should map validation errors to 400', () => {
      const errors = [
        new ValidationError(),
        new WeakPasswordError(),
      ];

      errors.forEach(error => {
        expect(error.statusCode).toBe(400);
      });
    });

    it('should map server errors to 500', () => {
      const error = new DatabaseError();
      expect(error.statusCode).toBe(500);
    });
  });

  describe('Error Inheritance', () => {
    it('should maintain proper inheritance chain', () => {
      const authError = new InvalidCredentialsError();
      
      expect(authError).toBeInstanceOf(InvalidCredentialsError);
      expect(authError).toBeInstanceOf(AuthenticationError);
      expect(authError).toBeInstanceOf(AppError);
      expect(authError).toBeInstanceOf(Error);
    });

    it('should have correct constructor names', () => {
      const errors = [
        new AppError('test', ErrorCode.UNKNOWN_ERROR),
        new AuthenticationError('test'),
        new InvalidCredentialsError(),
        new ValidationError(),
        new DatabaseError(),
      ];

      expect(errors[0].name).toBe('AppError');
      expect(errors[1].name).toBe('AuthenticationError');
      expect(errors[2].name).toBe('InvalidCredentialsError');
      expect(errors[3].name).toBe('ValidationError');
      expect(errors[4].name).toBe('DatabaseError');
    });
  });

  describe('Error Details', () => {
    it('should preserve error details through formatting', () => {
      const details = {
        field: 'email',
        value: 'invalid@',
        constraint: 'email format',
      };
      
      const error = new ValidationError('Email validation failed', details);
      const graphqlError = error.toGraphQLError();

      expect(graphqlError.extensions?.details).toEqual(details);
    });

    it('should handle complex nested details', () => {
      const details = {
        validation: {
          fields: ['email', 'password'],
          rules: {
            email: ['required', 'email'],
            password: ['required', 'min:8'],
          },
        },
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'registration',
        },
      };

      const error = new ValidationError('Complex validation failed', details);
      expect(error.details).toEqual(details);
    });
  });
}); 