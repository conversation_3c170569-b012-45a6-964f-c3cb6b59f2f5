import { and, eq } from 'drizzle-orm';
import type { InferInsertModel } from 'drizzle-orm';
import { beforeEach, describe, expect, it } from 'vitest';
import { accounts, users } from '../../../src/database/schema.js';
import { getTestDb, withTransaction } from '../../../src/test-utils/db.js';
import {
  generateTestAccount,
  generateTestUser,
} from '../../../src/test-utils/test-data.js';

describe('Database Integration - Accounts Table', () => {
  const db = getTestDb();

  describe('Account Creation', () => {
    it('should create an account with all required fields', async () => {
      await withTransaction(async db => {
        // Create a test user first
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Checking Account',
          type: 'checking',
          currency: 'USD',
          initialBalance: '1500.00',
          currentBalance: '1500.00',
        });

        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        expect(insertedAccount).toBeDefined();
        expect(insertedAccount.id).toBeDefined();
        expect(insertedAccount.userId).toBe(insertedUser.id);
        expect(insertedAccount.name).toBe('Test Checking Account');
        expect(insertedAccount.type).toBe('checking');
        expect(insertedAccount.currency).toBe('USD');
        expect(insertedAccount.initialBalance).toBe('1500.0000');
        expect(insertedAccount.currentBalance).toBe('1500.0000');
        expect(insertedAccount.isArchived).toBe(false);
        expect(insertedAccount.includeInNetWorth).toBe(true);
        expect(insertedAccount.displayOrder).toBe(0);
        expect(insertedAccount.createdAt).toBeDefined();
        expect(insertedAccount.updatedAt).toBeDefined();
      });
    });

    it('should create accounts with different types', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const accountTypes = [
          'checking',
          'savings',
          'credit_card',
          'cash',
          'investment',
          'loan',
          'asset',
          'liability',
        ] as const;

        for (const type of accountTypes) {
          const testAccount = generateTestAccount(insertedUser.id, {
            name: `Test ${type} Account`,
            type,
          });

          const [insertedAccount] = await db
            .insert(accounts)
            .values(testAccount)
            .returning();
          expect(insertedAccount.type).toBe(type);
        }
      });
    });

    it('should create account with default values', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Default Account',
          type: 'checking',
          initialBalance: undefined,
          currentBalance: undefined,
          isArchived: undefined,
          includeInNetWorth: undefined,
          displayOrder: undefined,
        });

        // Remove undefined values to test defaults
        const accountData = Object.fromEntries(
          Object.entries(testAccount).filter(
            ([_, value]) => value !== undefined
          )
        ) as InferInsertModel<typeof accounts>;

        const [insertedAccount] = await db
          .insert(accounts)
          .values(accountData)
          .returning();

        expect(insertedAccount.currency).toBe('USD'); // Default
        expect(insertedAccount.initialBalance).toBe('0.0000'); // Default
        expect(insertedAccount.currentBalance).toBe('0.0000'); // Default
        expect(insertedAccount.isArchived).toBe(false); // Default
        expect(insertedAccount.includeInNetWorth).toBe(true); // Default
        expect(insertedAccount.displayOrder).toBe(0); // Default
      });
    });

    it('should enforce foreign key constraint with users table', async () => {
      await withTransaction(async db => {
        const nonExistentUserId = '********-0000-0000-0000-********0000';
        const testAccount = generateTestAccount(nonExistentUserId, {
          name: 'Orphan Account',
        });

        await expect(db.insert(accounts).values(testAccount)).rejects.toThrow();
      });
    });
  });

  describe('Account Retrieval', () => {
    it('should retrieve accounts by user id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const account1 = generateTestAccount(insertedUser.id, {
          name: 'Account 1',
        });
        const account2 = generateTestAccount(insertedUser.id, {
          name: 'Account 2',
        });

        await db.insert(accounts).values([account1, account2]);

        const userAccounts = await db
          .select()
          .from(accounts)
          .where(eq(accounts.userId, insertedUser.id));

        expect(userAccounts).toHaveLength(2);
        expect(userAccounts.map(a => a.name)).toContain('Account 1');
        expect(userAccounts.map(a => a.name)).toContain('Account 2');
      });
    });

    it('should retrieve account by id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({ email: '<EMAIL>' });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Find Me',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const retrievedAccount = await db
          .select()
          .from(accounts)
          .where(eq(accounts.id, insertedAccount.id));

        expect(retrievedAccount).toHaveLength(1);
        expect(retrievedAccount[0].name).toBe('Find Me');
      });
    });

    it('should filter archived accounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const activeAccount = generateTestAccount(insertedUser.id, {
          name: 'Active',
          isArchived: false,
        });
        const archivedAccount = generateTestAccount(insertedUser.id, {
          name: 'Archived',
          isArchived: true,
        });

        await db.insert(accounts).values([activeAccount, archivedAccount]);

        const activeAccounts = await db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.userId, insertedUser.id),
              eq(accounts.isArchived, false)
            )
          );

        expect(activeAccounts).toHaveLength(1);
        expect(activeAccounts[0].name).toBe('Active');
      });
    });
  });

  describe('Account Updates', () => {
    it('should update account balance', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Balance Test',
          currentBalance: '1000.00',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const [updatedAccount] = await db
          .update(accounts)
          .set({
            currentBalance: '1250.50',
            updatedAt: new Date(),
          })
          .where(eq(accounts.id, insertedAccount.id))
          .returning();

        expect(updatedAccount.currentBalance).toBe('1250.5000');
        expect(updatedAccount.updatedAt.getTime()).toBeGreaterThan(
          insertedAccount.updatedAt.getTime()
        );
      });
    });

    it('should archive an account', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'To Archive',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const [archivedAccount] = await db
          .update(accounts)
          .set({ isArchived: true })
          .where(eq(accounts.id, insertedAccount.id))
          .returning();

        expect(archivedAccount.isArchived).toBe(true);
      });
    });

    it('should update display order', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({ email: '<EMAIL>' });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          displayOrder: 1,
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const [updatedAccount] = await db
          .update(accounts)
          .set({ displayOrder: 5 })
          .where(eq(accounts.id, insertedAccount.id))
          .returning();

        expect(updatedAccount.displayOrder).toBe(5);
      });
    });
  });

  describe('Account Deletion', () => {
    it('should delete an account', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'To Delete',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        await db.delete(accounts).where(eq(accounts.id, insertedAccount.id));

        const retrievedAccount = await db
          .select()
          .from(accounts)
          .where(eq(accounts.id, insertedAccount.id));
        expect(retrievedAccount).toHaveLength(0);
      });
    });

    it('should cascade delete accounts when user is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Will Be Deleted',
        });
        await db.insert(accounts).values(testAccount);

        // Delete the user
        await db.delete(users).where(eq(users.id, insertedUser.id));

        // Account should be deleted due to cascade
        const remainingAccounts = await db
          .select()
          .from(accounts)
          .where(eq(accounts.userId, insertedUser.id));
        expect(remainingAccounts).toHaveLength(0);
      });
    });
  });

  describe('Account Validation', () => {
    it('should handle different currencies', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD'];

        for (const currency of currencies) {
          const testAccount = generateTestAccount(insertedUser.id, {
            name: `${currency} Account`,
            currency,
          });

          const [insertedAccount] = await db
            .insert(accounts)
            .values(testAccount)
            .returning();
          expect(insertedAccount.currency).toBe(currency);
        }
      });
    });

    it('should handle large balance amounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Large Balance',
          initialBalance: '***************.9999',
          currentBalance: '***************.9999',
        });

        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();
        expect(insertedAccount.initialBalance).toBe('***************.9999');
        expect(insertedAccount.currentBalance).toBe('***************.9999');
      });
    });

    it('should handle negative balances', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Negative Balance',
          type: 'credit_card',
          currentBalance: '-500.25',
        });

        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();
        expect(insertedAccount.currentBalance).toBe('-500.2500');
      });
    });
  });
});
