import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { drizzle } from 'drizzle-orm/postgres-js';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { migrate } from '../../../src/database/migrate.js';
import * as schema from '../../../src/database/schema.js';

describe('Integration Tests - Database Migration', () => {
  let testClient: postgres.Sql;
  let testDb: PostgresJsDatabase<typeof schema>;
  let originalEnv: NodeJS.ProcessEnv;

  beforeAll(() => {
    // Save original environment
    originalEnv = { ...process.env };
  });

  afterAll(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  beforeEach(async () => {
    // Set up test database connection
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is required for integration tests');
    }

    testClient = postgres(databaseUrl, { max: 1 });
    testDb = drizzle(testClient, { schema });
  });

  afterEach(async () => {
    // Clean up database connection
    if (testClient) {
      await testClient.end();
    }
  });

  describe('migrate function', () => {
    it('should run migrations successfully with valid database and migrations folder', async () => {
      const migrationsFolder = './migrations';
      
      // This should not throw an error
      await expect(migrate(testDb, { migrationsFolder })).resolves.not.toThrow();
    });

    it('should handle migrations folder path correctly', async () => {
      const migrationsFolder = './migrations';
      
      // Test that the function accepts the migrations folder parameter
      const result = await migrate(testDb, { migrationsFolder });
      
      // The migrate function should complete without error
      expect(result).toBeUndefined(); // drizzle migrate returns void
    });

    it('should work with different migration folder paths', async () => {
      // Test with absolute path
      const absolutePath = process.cwd() + '/migrations';
      
      await expect(migrate(testDb, { migrationsFolder: absolutePath })).resolves.not.toThrow();
    });
  });

  describe('migration error handling', () => {
    it('should handle invalid migrations folder gracefully', async () => {
      const invalidFolder = './non-existent-migrations';
      
      // This might throw an error or handle it gracefully depending on drizzle implementation
      try {
        await migrate(testDb, { migrationsFolder: invalidFolder });
      } catch (error) {
        // If it throws, it should be a meaningful error
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('database connection validation', () => {
    it('should work with valid database connection', async () => {
      // Test that we can perform a simple query to validate connection
      const result = await testDb.execute('SELECT 1 as test');
      expect(result).toBeDefined();
    });
  });
}); 