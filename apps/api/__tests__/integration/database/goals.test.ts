import { and, eq } from 'drizzle-orm';
import type { InferInsertModel } from 'drizzle-orm';
import { beforeEach, describe, expect, it } from 'vitest';
import { accounts, goals, users } from '../../../src/database/schema.js';
import { getTestDb, withTransaction } from '../../../src/test-utils/db.js';
import {
  generateTestAccount,
  generateTestGoal,
  generateTestUser,
} from '../../../src/test-utils/test-data.js';

describe('Database Integration - Goals Table', () => {
  const db = getTestDb();

  describe('Goal Creation', () => {
    it('should create a goal with all required fields', async () => {
      await withTransaction(async db => {
        // Create test user
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        // Create test account
        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Savings Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Emergency Fund',
          type: 'emergency_fund',
          targetAmount: '10000.00',
          currentAmount: '2500.00',
          currency: 'USD',
          deadline: new Date('2024-12-31'),
          accountId: insertedAccount.id,
          notes: 'Save for emergencies',
          icon: 'shield',
          color: '#FF9800',
        });

        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        expect(insertedGoal).toBeDefined();
        expect(insertedGoal.id).toBeDefined();
        expect(insertedGoal.userId).toBe(insertedUser.id);
        expect(insertedGoal.name).toBe('Emergency Fund');
        expect(insertedGoal.type).toBe('emergency_fund');
        expect(insertedGoal.targetAmount).toBe('10000.0000');
        expect(insertedGoal.currentAmount).toBe('2500.0000');
        expect(insertedGoal.currency).toBe('USD');
        expect(insertedGoal.deadline).toBeInstanceOf(Date);
        expect(insertedGoal.accountId).toBe(insertedAccount.id);
        expect(insertedGoal.isArchived).toBe(false);
        expect(insertedGoal.isCompleted).toBe(false);
        expect(insertedGoal.notes).toBe('Save for emergencies');
        expect(insertedGoal.icon).toBe('shield');
        expect(insertedGoal.color).toBe('#FF9800');
        expect(insertedGoal.createdAt).toBeDefined();
        expect(insertedGoal.updatedAt).toBeDefined();
      });
    });

    it('should create goals with different types', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const goalTypes = [
          'savings',
          'debt_payoff',
          'purchase',
          'emergency_fund',
          'other',
        ] as const;

        for (const type of goalTypes) {
          const testGoal = generateTestGoal(insertedUser.id, {
            name: `${type} Goal`,
            type,
            targetAmount: '5000.00',
          });

          const [insertedGoal] = await db
            .insert(goals)
            .values(testGoal)
            .returning();
          expect(insertedGoal.type).toBe(type);
        }
      });
    });

    it('should create goal without account', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'General Goal',
          type: 'savings',
          targetAmount: '1000.00',
          accountId: null,
        });

        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        expect(insertedGoal.accountId).toBeNull();
        expect(insertedGoal.name).toBe('General Goal');
      });
    });

    it('should create goal with minimal required fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Minimal Goal',
          type: 'savings',
          targetAmount: '500.00',
          currentAmount: undefined,
          deadline: null,
          accountId: null,
          isArchived: undefined,
          isCompleted: undefined,
          notes: null,
          icon: null,
          color: null,
        });

        // Remove undefined values to test defaults
        const goalData = Object.fromEntries(
          Object.entries(testGoal).filter(
            ([_, value]) => value !== undefined && value !== null
          )
        ) as InferInsertModel<typeof goals>;

        const [insertedGoal] = await db
          .insert(goals)
          .values(goalData)
          .returning();

        expect(insertedGoal.name).toBe('Minimal Goal');
        expect(insertedGoal.currentAmount).toBe('0.0000'); // Default
        expect(insertedGoal.currency).toBe('USD'); // Default
        expect(insertedGoal.deadline).toBeNull();
        expect(insertedGoal.isArchived).toBe(false); // Default
        expect(insertedGoal.isCompleted).toBe(false); // Default
      });
    });

    it('should enforce foreign key constraint with users table', async () => {
      await withTransaction(async db => {
        const nonExistentUserId = '00000000-0000-0000-0000-000000000000';
        const testGoal = generateTestGoal(nonExistentUserId, {
          name: 'Orphan Goal',
        });

        await expect(db.insert(goals).values(testGoal)).rejects.toThrow();
      });
    });
  });

  describe('Goal Retrieval', () => {
    it('should retrieve goals by user id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const goal1 = generateTestGoal(insertedUser.id, {
          name: 'Goal 1',
          targetAmount: '1000.00',
        });
        const goal2 = generateTestGoal(insertedUser.id, {
          name: 'Goal 2',
          targetAmount: '2000.00',
        });

        await db.insert(goals).values([goal1, goal2]);

        const userGoals = await db
          .select()
          .from(goals)
          .where(eq(goals.userId, insertedUser.id));

        expect(userGoals).toHaveLength(2);
        expect(userGoals.map(g => g.name)).toContain('Goal 1');
        expect(userGoals.map(g => g.name)).toContain('Goal 2');
      });
    });

    it('should retrieve goals by type', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const savingsGoal = generateTestGoal(insertedUser.id, {
          name: 'Savings Goal',
          type: 'savings',
        });
        const debtGoal = generateTestGoal(insertedUser.id, {
          name: 'Debt Goal',
          type: 'debt_payoff',
        });

        await db.insert(goals).values([savingsGoal, debtGoal]);

        const savingsGoals = await db
          .select()
          .from(goals)
          .where(
            and(eq(goals.userId, insertedUser.id), eq(goals.type, 'savings'))
          );

        expect(savingsGoals).toHaveLength(1);
        expect(savingsGoals[0].name).toBe('Savings Goal');
      });
    });

    it('should retrieve goals by account id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const account1 = generateTestAccount(insertedUser.id, {
          name: 'Account 1',
        });
        const account2 = generateTestAccount(insertedUser.id, {
          name: 'Account 2',
        });
        const [insertedAccount1] = await db
          .insert(accounts)
          .values(account1)
          .returning();
        const [insertedAccount2] = await db
          .insert(accounts)
          .values(account2)
          .returning();

        const goal1 = generateTestGoal(insertedUser.id, {
          name: 'Goal 1',
          accountId: insertedAccount1.id,
        });
        const goal2 = generateTestGoal(insertedUser.id, {
          name: 'Goal 2',
          accountId: insertedAccount2.id,
        });

        await db.insert(goals).values([goal1, goal2]);

        const account1Goals = await db
          .select()
          .from(goals)
          .where(eq(goals.accountId, insertedAccount1.id));

        expect(account1Goals).toHaveLength(1);
        expect(account1Goals[0].name).toBe('Goal 1');
      });
    });

    it('should filter completed goals', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const activeGoal = generateTestGoal(insertedUser.id, {
          name: 'Active Goal',
          isCompleted: false,
        });
        const completedGoal = generateTestGoal(insertedUser.id, {
          name: 'Completed Goal',
          isCompleted: true,
        });

        await db.insert(goals).values([activeGoal, completedGoal]);

        const activeGoals = await db
          .select()
          .from(goals)
          .where(
            and(eq(goals.userId, insertedUser.id), eq(goals.isCompleted, false))
          );

        expect(activeGoals).toHaveLength(1);
        expect(activeGoals[0].name).toBe('Active Goal');
      });
    });

    it('should filter archived goals', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const activeGoal = generateTestGoal(insertedUser.id, {
          name: 'Active Goal',
          isArchived: false,
        });
        const archivedGoal = generateTestGoal(insertedUser.id, {
          name: 'Archived Goal',
          isArchived: true,
        });

        await db.insert(goals).values([activeGoal, archivedGoal]);

        const activeGoals = await db
          .select()
          .from(goals)
          .where(
            and(eq(goals.userId, insertedUser.id), eq(goals.isArchived, false))
          );

        expect(activeGoals).toHaveLength(1);
        expect(activeGoals[0].name).toBe('Active Goal');
      });
    });
  });

  describe('Goal Updates', () => {
    it('should update goal progress', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Progress Goal',
          targetAmount: '1000.00',
          currentAmount: '250.00',
        });
        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        const [updatedGoal] = await db
          .update(goals)
          .set({
            currentAmount: '500.00',
            updatedAt: new Date(),
          })
          .where(eq(goals.id, insertedGoal.id))
          .returning();

        expect(updatedGoal.currentAmount).toBe('500.0000');
        expect(updatedGoal.updatedAt.getTime()).toBeGreaterThan(
          insertedGoal.updatedAt.getTime()
        );
      });
    });

    it('should complete a goal', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Complete Goal',
          targetAmount: '1000.00',
          currentAmount: '900.00',
          isCompleted: false,
        });
        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        const [completedGoal] = await db
          .update(goals)
          .set({
            currentAmount: '1000.00',
            isCompleted: true,
          })
          .where(eq(goals.id, insertedGoal.id))
          .returning();

        expect(completedGoal.currentAmount).toBe('1000.0000');
        expect(completedGoal.isCompleted).toBe(true);
      });
    });

    it('should archive a goal', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'To Archive',
        });
        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        const [archivedGoal] = await db
          .update(goals)
          .set({ isArchived: true })
          .where(eq(goals.id, insertedGoal.id))
          .returning();

        expect(archivedGoal.isArchived).toBe(true);
      });
    });

    it('should update goal deadline', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Deadline Goal',
          deadline: new Date('2024-06-30'),
        });
        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        const newDeadline = new Date('2024-12-31');
        const [updatedGoal] = await db
          .update(goals)
          .set({ deadline: newDeadline })
          .where(eq(goals.id, insertedGoal.id))
          .returning();

        expect(updatedGoal.deadline?.getTime()).toBe(newDeadline.getTime());
      });
    });
  });

  describe('Goal Deletion', () => {
    it('should delete a goal', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'To Delete',
        });
        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        await db.delete(goals).where(eq(goals.id, insertedGoal.id));

        const retrievedGoal = await db
          .select()
          .from(goals)
          .where(eq(goals.id, insertedGoal.id));
        expect(retrievedGoal).toHaveLength(0);
      });
    });

    it('should cascade delete goals when user is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Will Be Deleted',
        });
        await db.insert(goals).values(testGoal);

        // Delete the user
        await db.delete(users).where(eq(users.id, insertedUser.id));

        // Goal should be deleted due to cascade
        const remainingGoals = await db
          .select()
          .from(goals)
          .where(eq(goals.userId, insertedUser.id));
        expect(remainingGoals).toHaveLength(0);
      });
    });

    it('should set account to null when account is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Account Goal',
          accountId: insertedAccount.id,
        });
        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        // Delete the account
        await db.delete(accounts).where(eq(accounts.id, insertedAccount.id));

        // Goal should have accountId set to null
        const updatedGoal = await db
          .select()
          .from(goals)
          .where(eq(goals.id, insertedGoal.id));
        expect(updatedGoal[0].accountId).toBeNull();
      });
    });
  });

  describe('Goal Validation', () => {
    it('should handle large target amounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Large Goal',
          targetAmount: '***************.9999',
        });

        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();
        expect(insertedGoal.targetAmount).toBe('***************.9999');
      });
    });

    it('should handle different currencies', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD'];

        for (const currency of currencies) {
          const testGoal = generateTestGoal(insertedUser.id, {
            name: `${currency} Goal`,
            currency,
          });

          const [insertedGoal] = await db
            .insert(goals)
            .values(testGoal)
            .returning();
          expect(insertedGoal.currency).toBe(currency);
        }
      });
    });

    it('should handle long goal names', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const longName = 'a'.repeat(255);
        const testGoal = generateTestGoal(insertedUser.id, { name: longName });

        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();
        expect(insertedGoal.name).toBe(longName);
      });
    });

    it('should handle goals with and without deadlines', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const goalWithDeadline = generateTestGoal(insertedUser.id, {
          name: 'Goal With Deadline',
          deadline: new Date('2024-12-31'),
        });
        const goalWithoutDeadline = generateTestGoal(insertedUser.id, {
          name: 'Goal Without Deadline',
          deadline: null,
        });

        const [insertedWithDeadline] = await db
          .insert(goals)
          .values(goalWithDeadline)
          .returning();
        const [insertedWithoutDeadline] = await db
          .insert(goals)
          .values(goalWithoutDeadline)
          .returning();

        expect(insertedWithDeadline.deadline).toBeInstanceOf(Date);
        expect(insertedWithoutDeadline.deadline).toBeNull();
      });
    });
  });

  describe('Goal Timestamps', () => {
    it('should set createdAt and updatedAt on creation', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testGoal = generateTestGoal(insertedUser.id, {
          name: 'Timestamp Test',
        });

        const [insertedGoal] = await db
          .insert(goals)
          .values(testGoal)
          .returning();

        expect(insertedGoal.createdAt).toBeDefined();
        expect(insertedGoal.updatedAt).toBeDefined();
        expect(insertedGoal.createdAt).toBeInstanceOf(Date);
        expect(insertedGoal.updatedAt).toBeInstanceOf(Date);
      });
    });
  });
});
