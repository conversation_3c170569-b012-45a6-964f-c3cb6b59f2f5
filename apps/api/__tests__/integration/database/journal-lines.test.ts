import { and, eq } from 'drizzle-orm';
import { beforeEach, describe, expect, it } from 'vitest';
import {
  accounts,
  categories,
  journalEntries,
  journalLines,
  users,
} from '../../../src/database/schema.js';
import { getTestDb, withTransaction } from '../../../src/test-utils/db.js';
import {
  generateTestAccount,
  generateTestCategory,
  generateTestJournalEntry,
  generateTestJournalLine,
  generateTestUser,
} from '../../../src/test-utils/test-data.js';

describe('Database Integration - Journal Lines Table', () => {
  const db = getTestDb();

  describe('Journal Line Creation', () => {
    it('should create a journal line with all required fields', async () => {
      await withTransaction(async db => {
        // Create test user
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        // Create test account
        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        // Create test category
        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Test Category',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        // Create test journal entry
        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        // Create journal line
        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            categoryId: insertedCategory.id,
            amount: '100.50',
            type: 'debit',
            notes: 'Test line notes',
          }
        );

        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();

        expect(insertedLine).toBeDefined();
        expect(insertedLine.id).toBeDefined();
        expect(insertedLine.journalEntryId).toBe(insertedEntry.id);
        expect(insertedLine.accountId).toBe(insertedAccount.id);
        expect(insertedLine.categoryId).toBe(insertedCategory.id);
        expect(insertedLine.amount).toBe('100.5000');
        expect(insertedLine.type).toBe('debit');
        expect(insertedLine.notes).toBe('Test line notes');
        expect(insertedLine.createdAt).toBeDefined();
        expect(insertedLine.updatedAt).toBeDefined();
      });
    });

    it('should create journal lines with different types', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const debitLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            type: 'debit',
          }
        );

        const creditLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            type: 'credit',
          }
        );

        const [insertedDebit] = await db
          .insert(journalLines)
          .values(debitLine)
          .returning();
        const [insertedCredit] = await db
          .insert(journalLines)
          .values(creditLine)
          .returning();

        expect(insertedDebit.type).toBe('debit');
        expect(insertedCredit.type).toBe('credit');
      });
    });

    it('should create journal line without category', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            categoryId: null,
            amount: '50.00',
            type: 'debit',
          }
        );

        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();

        expect(insertedLine.categoryId).toBeNull();
        expect(insertedLine.amount).toBe('50.0000');
      });
    });

    it('should enforce foreign key constraint with journal entries', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const nonExistentEntryId = '********-0000-0000-0000-********0000';
        const testLine = generateTestJournalLine(
          nonExistentEntryId,
          insertedAccount.id,
          {
            amount: '100.00',
          }
        );

        await expect(
          db.insert(journalLines).values(testLine)
        ).rejects.toThrow();
      });
    });

    it('should enforce foreign key constraint with accounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const nonExistentAccountId = '********-0000-0000-0000-********0000';
        const testLine = generateTestJournalLine(
          insertedEntry.id,
          nonExistentAccountId,
          {
            amount: '100.00',
          }
        );

        await expect(
          db.insert(journalLines).values(testLine)
        ).rejects.toThrow();
      });
    });
  });

  describe('Journal Line Retrieval', () => {
    it('should retrieve journal lines by journal entry id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const line1 = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            type: 'debit',
          }
        );
        const line2 = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            type: 'credit',
          }
        );

        await db.insert(journalLines).values([line1, line2]);

        const entryLines = await db
          .select()
          .from(journalLines)
          .where(eq(journalLines.journalEntryId, insertedEntry.id));

        expect(entryLines).toHaveLength(2);
        expect(entryLines.map(l => l.type)).toContain('debit');
        expect(entryLines.map(l => l.type)).toContain('credit');
      });
    });

    it('should retrieve journal lines by account id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const account1 = generateTestAccount(insertedUser.id, {
          name: 'Account 1',
        });
        const account2 = generateTestAccount(insertedUser.id, {
          name: 'Account 2',
        });
        const [insertedAccount1] = await db
          .insert(accounts)
          .values(account1)
          .returning();
        const [insertedAccount2] = await db
          .insert(accounts)
          .values(account2)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const line1 = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount1.id,
          { amount: '100.00' }
        );
        const line2 = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount2.id,
          { amount: '50.00' }
        );

        await db.insert(journalLines).values([line1, line2]);

        const account1Lines = await db
          .select()
          .from(journalLines)
          .where(eq(journalLines.accountId, insertedAccount1.id));

        expect(account1Lines).toHaveLength(1);
        expect(account1Lines[0].amount).toBe('100.0000');
      });
    });

    it('should retrieve journal lines by category id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const category1 = generateTestCategory(insertedUser.id, {
          name: 'Category 1',
        });
        const category2 = generateTestCategory(insertedUser.id, {
          name: 'Category 2',
        });
        const [insertedCategory1] = await db
          .insert(categories)
          .values(category1)
          .returning();
        const [insertedCategory2] = await db
          .insert(categories)
          .values(category2)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const line1 = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            categoryId: insertedCategory1.id,
            amount: '100.00',
          }
        );
        const line2 = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            categoryId: insertedCategory2.id,
            amount: '50.00',
          }
        );

        await db.insert(journalLines).values([line1, line2]);

        const category1Lines = await db
          .select()
          .from(journalLines)
          .where(eq(journalLines.categoryId, insertedCategory1.id));

        expect(category1Lines).toHaveLength(1);
        expect(category1Lines[0].amount).toBe('100.0000');
      });
    });

    it('should retrieve journal lines by type', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const debitLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            type: 'debit',
          }
        );
        const creditLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            type: 'credit',
          }
        );

        const [insertedDebitLine, insertedCreditLine] = await db
          .insert(journalLines)
          .values([debitLine, creditLine])
          .returning();

        // Query only the lines created in this transaction by filtering by journal entry
        const debitLines = await db
          .select()
          .from(journalLines)
          .where(
            and(
              eq(journalLines.type, 'debit'),
              eq(journalLines.journalEntryId, insertedEntry.id)
            )
          );

        expect(debitLines).toHaveLength(1);
        expect(debitLines[0].type).toBe('debit');
        expect(debitLines[0].id).toBe(insertedDebitLine.id);
      });
    });
  });

  describe('Journal Line Updates', () => {
    it('should update journal line fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '100.00',
            notes: 'Original notes',
          }
        );
        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();

        const [updatedLine] = await db
          .update(journalLines)
          .set({
            amount: '150.75',
            notes: 'Updated notes',
            updatedAt: new Date(),
          })
          .where(eq(journalLines.id, insertedLine.id))
          .returning();

        expect(updatedLine.amount).toBe('150.7500');
        expect(updatedLine.notes).toBe('Updated notes');
        expect(updatedLine.updatedAt.getTime()).toBeGreaterThan(
          insertedLine.updatedAt.getTime()
        );
      });
    });

    it('should update category assignment', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const category1 = generateTestCategory(insertedUser.id, {
          name: 'Category 1',
        });
        const category2 = generateTestCategory(insertedUser.id, {
          name: 'Category 2',
        });
        const [insertedCategory1] = await db
          .insert(categories)
          .values(category1)
          .returning();
        const [insertedCategory2] = await db
          .insert(categories)
          .values(category2)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            categoryId: insertedCategory1.id,
            amount: '100.00',
          }
        );
        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();

        const [updatedLine] = await db
          .update(journalLines)
          .set({ categoryId: insertedCategory2.id })
          .where(eq(journalLines.id, insertedLine.id))
          .returning();

        expect(updatedLine.categoryId).toBe(insertedCategory2.id);
      });
    });
  });

  describe('Journal Line Deletion', () => {
    it('should delete a journal line', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          { amount: '100.00' }
        );
        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();

        await db
          .delete(journalLines)
          .where(eq(journalLines.id, insertedLine.id));

        const retrievedLine = await db
          .select()
          .from(journalLines)
          .where(eq(journalLines.id, insertedLine.id));
        expect(retrievedLine).toHaveLength(0);
      });
    });

    it('should cascade delete journal lines when journal entry is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          { amount: '100.00' }
        );
        await db.insert(journalLines).values(testLine);

        // Delete the journal entry
        await db
          .delete(journalEntries)
          .where(eq(journalEntries.id, insertedEntry.id));

        // Journal line should be deleted due to cascade
        const remainingLines = await db
          .select()
          .from(journalLines)
          .where(eq(journalLines.journalEntryId, insertedEntry.id));
        expect(remainingLines).toHaveLength(0);
      });
    });

    it('should set category to null when category is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Test Category',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            categoryId: insertedCategory.id,
            amount: '100.00',
          }
        );
        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();

        // Delete the category
        await db
          .delete(categories)
          .where(eq(categories.id, insertedCategory.id));

        // Journal line should have categoryId set to null
        const updatedLine = await db
          .select()
          .from(journalLines)
          .where(eq(journalLines.id, insertedLine.id));
        expect(updatedLine[0].categoryId).toBeNull();
      });
    });
  });

  describe('Journal Line Validation', () => {
    it('should handle large amounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '***************.9999',
          }
        );

        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();
        expect(insertedLine.amount).toBe('***************.9999');
      });
    });

    it('should handle negative amounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testAccount = generateTestAccount(insertedUser.id, {
          name: 'Test Account',
        });
        const [insertedAccount] = await db
          .insert(accounts)
          .values(testAccount)
          .returning();

        const testEntry = generateTestJournalEntry(insertedUser.id, {
          description: 'Test Entry',
        });
        const [insertedEntry] = await db
          .insert(journalEntries)
          .values(testEntry)
          .returning();

        const testLine = generateTestJournalLine(
          insertedEntry.id,
          insertedAccount.id,
          {
            amount: '-500.25',
          }
        );

        const [insertedLine] = await db
          .insert(journalLines)
          .values(testLine)
          .returning();
        expect(insertedLine.amount).toBe('-500.2500');
      });
    });
  });
});
