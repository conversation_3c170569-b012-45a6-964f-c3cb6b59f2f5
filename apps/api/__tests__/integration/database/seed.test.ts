import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { eq } from 'drizzle-orm';
import { accounts, categories, users } from '../../../src/database/schema.js';
import { withSeededData } from '../../../src/test-utils/db.js';
import { faker } from '@faker-js/faker';

describe('Integration Tests - Database Seeding', () => {
  let testUserId: string;

  beforeEach(async () => {
    // Create a test user for seeding tests
    await withSeededData(async (db) => {
      const [testUser] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUserId = testUser.id;
    });
  });

  afterEach(async () => {
    // Clean up test data
    await withSeededData(async (db) => {
      await db.delete(accounts).where(eq(accounts.userId, testUserId));
      await db.delete(categories).where(eq(categories.userId, testUserId));
      await db.delete(users).where(eq(users.id, testUserId));
    });
  });

  describe('User data seeding', () => {
    it('should create user with valid data', async () => {
      await withSeededData(async (db) => {
        const user = await db.select().from(users).where(eq(users.id, testUserId)).limit(1);

        expect(user).toBeDefined();
        expect(user[0]?.id).toBe(testUserId);
        expect(user[0]?.email).toBeDefined();
        expect(user[0]?.firstName).toBeDefined();
        expect(user[0]?.lastName).toBeDefined();
        expect(user[0]?.emailVerified).toBe(true);
      });
    });

    it('should handle user creation with minimal required fields', async () => {
      await withSeededData(async (db) => {
        const [minimalUser] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();

        expect(minimalUser).toBeDefined();
        expect(minimalUser.emailVerified).toBe(false); // Default value
        expect(minimalUser.createdAt).toBeDefined();
        expect(minimalUser.updatedAt).toBeDefined();

        // Clean up
        await db.delete(users).where(eq(users.id, minimalUser.id));
      });
    });
  });

  describe('Account data seeding', () => {
    it('should create account with valid data', async () => {
      await withSeededData(async (db) => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: faker.finance.accountName(),
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
          .returning();

        expect(account).toBeDefined();
        expect(account.userId).toBe(testUserId);
        expect(account.name).toBeDefined();
        expect(account.type).toBe('checking');
        expect(account.currency).toBe('USD');
        expect(account.isArchived).toBe(false); // Default value
      });
    });

    it('should create multiple accounts for same user', async () => {
      await withSeededData(async (db) => {
        const accountsData = [
          {
            userId: testUserId,
            name: 'Checking Account',
            type: 'checking' as const,
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          },
          {
            userId: testUserId,
            name: 'Savings Account',
            type: 'savings' as const,
            initialBalance: '5000.00',
            currentBalance: '5000.00',
            currency: 'USD',
          },
        ];

        const createdAccounts = await db
          .insert(accounts)
          .values(accountsData)
          .returning();

        expect(createdAccounts).toHaveLength(2);
        expect(createdAccounts[0].type).toBe('checking');
        expect(createdAccounts[1].type).toBe('savings');
      });
    });
  });

  describe('Category data seeding', () => {
    it('should create category with valid data', async () => {
      await withSeededData(async (db) => {
        const [category] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: faker.commerce.department(),
            type: 'expense',
            icon: 'shopping-cart',
            color: '#FF5733',
          })
          .returning();

        expect(category).toBeDefined();
        expect(category.userId).toBe(testUserId);
        expect(category.name).toBeDefined();
        expect(category.type).toBe('expense');
        expect(category.icon).toBe('shopping-cart');
        expect(category.color).toBe('#FF5733');
        expect(category.isArchived).toBe(false); // Default value
      });
    });

    it('should create hierarchical categories (parent-child)', async () => {
      await withSeededData(async (db) => {
        // Create parent category
        const [parentCategory] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Transportation',
            type: 'expense',
            icon: 'car',
            color: '#3498DB',
          })
          .returning();

        // Create child category
        const [childCategory] = await db
          .insert(categories)
          .values({
            userId: testUserId,
            name: 'Gas',
            type: 'expense',
            icon: 'gas-pump',
            color: '#E74C3C',
            parentId: parentCategory.id,
          })
          .returning();

        expect(parentCategory).toBeDefined();
        expect(childCategory).toBeDefined();
        expect(childCategory.parentId).toBe(parentCategory.id);

        // Verify relationship
        const childCategories = await db.select().from(categories).where(eq(categories.parentId, parentCategory.id));

        expect(childCategories).toHaveLength(1);
        expect(childCategories[0].id).toBe(childCategory.id);
      });
    });

    it('should create categories with different types', async () => {
      await withSeededData(async (db) => {
        const categoriesData = [
          {
            userId: testUserId,
            name: 'Salary',
            type: 'income' as const,
            icon: 'money',
            color: '#27AE60',
          },
          {
            userId: testUserId,
            name: 'Groceries',
            type: 'expense' as const,
            icon: 'shopping-cart',
            color: '#E74C3C',
          },
        ];

        const createdCategories = await db
          .insert(categories)
          .values(categoriesData)
          .returning();

        expect(createdCategories).toHaveLength(2);
        expect(createdCategories[0].type).toBe('income');
        expect(createdCategories[1].type).toBe('expense');
      });
    });
  });

  describe('Data integrity and constraints', () => {
    it('should enforce foreign key constraints', async () => {
      await withSeededData(async (db) => {
        // Try to create account with non-existent user ID
        const invalidUserId = 'non-existent-user-id';

        await expect(
          db.insert(accounts).values({
            userId: invalidUserId,
            name: 'Test Account',
            type: 'checking',
            initialBalance: '1000.00',
            currentBalance: '1000.00',
            currency: 'USD',
          })
        ).rejects.toThrow();
      });
    });

    it('should enforce unique constraints where applicable', async () => {
      await withSeededData(async (db) => {
        const email = faker.internet.email();

        // Create first user
        await db.insert(users).values({
          email,
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
        });

        // Try to create second user with same email
        await expect(
          db.insert(users).values({
            email, // Same email
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
        ).rejects.toThrow();

        // Clean up
        await db.delete(users).where(eq(users.email, email));
      });
    });
  });

  describe('Default values and timestamps', () => {
    it('should set default values correctly', async () => {
      await withSeededData(async (db) => {
        const [user] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();

        expect(user.emailVerified).toBe(false); // Default value
        expect(user.createdAt).toBeDefined();
        expect(user.updatedAt).toBeDefined();
        expect(user.createdAt).toEqual(user.updatedAt); // Should be same on creation

        // Clean up
        await db.delete(users).where(eq(users.id, user.id));
      });
    });

    it('should allow updating user data', async () => {
      await withSeededData(async (db) => {
        const [user] = await db
          .insert(users)
          .values({
            email: faker.internet.email(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            passwordHash: faker.internet.password(),
          })
          .returning();

        // Update the user
        const [updatedUser] = await db
          .update(users)
          .set({ firstName: 'Updated Name' })
          .where(eq(users.id, user.id))
          .returning();

        expect(updatedUser.firstName).toBe('Updated Name');
        expect(updatedUser.id).toBe(user.id);

        // Clean up
        await db.delete(users).where(eq(users.id, user.id));
      });
    });
  });
}); 