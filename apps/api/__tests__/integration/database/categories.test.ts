import { and, eq, isNull } from 'drizzle-orm';
import { beforeEach, describe, expect, it } from 'vitest';
import { categories, users } from '../../../src/database/schema.js';
import { getTestDb, withTransaction } from '../../../src/test-utils/db.js';
import {
  generateTestCategory,
  generateTestUser,
} from '../../../src/test-utils/test-data.js';

describe('Database Integration - Categories Table', () => {
  const db = getTestDb();

  describe('Category Creation', () => {
    it('should create a category with all required fields', async () => {
      await withTransaction(async db => {
        // Create a test user first
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Test Expense Category',
          type: 'expense',
          icon: 'shopping-cart',
          color: '#FF5722',
        });

        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        expect(insertedCategory).toBeDefined();
        expect(insertedCategory.id).toBeDefined();
        expect(insertedCategory.userId).toBe(insertedUser.id);
        expect(insertedCategory.name).toBe('Test Expense Category');
        expect(insertedCategory.type).toBe('expense');
        expect(insertedCategory.icon).toBe('shopping-cart');
        expect(insertedCategory.color).toBe('#FF5722');
        expect(insertedCategory.parentId).toBeNull();
        expect(insertedCategory.isDefault).toBe(false);
        expect(insertedCategory.isSystem).toBe(false);
        expect(insertedCategory.isArchived).toBe(false);
        expect(insertedCategory.displayOrder).toBe(0);
        expect(insertedCategory.createdAt).toBeDefined();
        expect(insertedCategory.updatedAt).toBeDefined();
      });
    });

    it('should create categories with different types', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const incomeCategory = generateTestCategory(insertedUser.id, {
          name: 'Salary',
          type: 'income',
        });

        const expenseCategory = generateTestCategory(insertedUser.id, {
          name: 'Groceries',
          type: 'expense',
        });

        const [insertedIncome] = await db
          .insert(categories)
          .values(incomeCategory)
          .returning();
        const [insertedExpense] = await db
          .insert(categories)
          .values(expenseCategory)
          .returning();

        expect(insertedIncome.type).toBe('income');
        expect(insertedExpense.type).toBe('expense');
      });
    });

    it('should create a subcategory with parent reference', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        // Create parent category
        const parentCategory = generateTestCategory(insertedUser.id, {
          name: 'Food & Dining',
          type: 'expense',
        });
        const [insertedParent] = await db
          .insert(categories)
          .values(parentCategory)
          .returning();

        // Create subcategory
        const subCategory = generateTestCategory(insertedUser.id, {
          name: 'Restaurants',
          type: 'expense',
          parentId: insertedParent.id,
        });
        const [insertedSub] = await db
          .insert(categories)
          .values(subCategory)
          .returning();

        expect(insertedSub.parentId).toBe(insertedParent.id);
        expect(insertedSub.name).toBe('Restaurants');
      });
    });

    it('should create system and default categories', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const systemCategory = generateTestCategory(insertedUser.id, {
          name: 'Uncategorized',
          type: 'expense',
          isSystem: true,
          isDefault: true,
        });

        const [insertedCategory] = await db
          .insert(categories)
          .values(systemCategory)
          .returning();

        expect(insertedCategory.isSystem).toBe(true);
        expect(insertedCategory.isDefault).toBe(true);
      });
    });

    it('should enforce foreign key constraint with users table', async () => {
      await withTransaction(async db => {
        const nonExistentUserId = '00000000-0000-0000-0000-000000000000';
        const testCategory = generateTestCategory(nonExistentUserId, {
          name: 'Orphan Category',
        });

        await expect(
          db.insert(categories).values(testCategory)
        ).rejects.toThrow();
      });
    });
  });

  describe('Category Retrieval', () => {
    it('should retrieve categories by user id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const category1 = generateTestCategory(insertedUser.id, {
          name: 'Category 1',
          type: 'income',
        });
        const category2 = generateTestCategory(insertedUser.id, {
          name: 'Category 2',
          type: 'expense',
        });

        await db.insert(categories).values([category1, category2]);

        const userCategories = await db
          .select()
          .from(categories)
          .where(eq(categories.userId, insertedUser.id));

        expect(userCategories).toHaveLength(2);
        expect(userCategories.map(c => c.name)).toContain('Category 1');
        expect(userCategories.map(c => c.name)).toContain('Category 2');
      });
    });

    it('should retrieve categories by type', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const incomeCategory = generateTestCategory(insertedUser.id, {
          name: 'Income Cat',
          type: 'income',
        });
        const expenseCategory = generateTestCategory(insertedUser.id, {
          name: 'Expense Cat',
          type: 'expense',
        });

        await db.insert(categories).values([incomeCategory, expenseCategory]);

        const expenseCategories = await db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.userId, insertedUser.id),
              eq(categories.type, 'expense')
            )
          );

        expect(expenseCategories).toHaveLength(1);
        expect(expenseCategories[0].name).toBe('Expense Cat');
      });
    });

    it('should retrieve parent categories (no parent)', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        // Create parent category
        const parentCategory = generateTestCategory(insertedUser.id, {
          name: 'Parent Category',
          parentId: null,
        });
        const [insertedParent] = await db
          .insert(categories)
          .values(parentCategory)
          .returning();

        // Create subcategory
        const subCategory = generateTestCategory(insertedUser.id, {
          name: 'Sub Category',
          parentId: insertedParent.id,
        });
        await db.insert(categories).values(subCategory);

        const parentCategories = await db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.userId, insertedUser.id),
              isNull(categories.parentId)
            )
          );

        expect(parentCategories).toHaveLength(1);
        expect(parentCategories[0].name).toBe('Parent Category');
      });
    });

    it('should retrieve subcategories by parent id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        // Create parent category
        const parentCategory = generateTestCategory(insertedUser.id, {
          name: 'Parent',
        });
        const [insertedParent] = await db
          .insert(categories)
          .values(parentCategory)
          .returning();

        // Create multiple subcategories
        const sub1 = generateTestCategory(insertedUser.id, {
          name: 'Sub 1',
          parentId: insertedParent.id,
        });
        const sub2 = generateTestCategory(insertedUser.id, {
          name: 'Sub 2',
          parentId: insertedParent.id,
        });

        await db.insert(categories).values([sub1, sub2]);

        const subcategories = await db
          .select()
          .from(categories)
          .where(eq(categories.parentId, insertedParent.id));

        expect(subcategories).toHaveLength(2);
        expect(subcategories.map(c => c.name)).toContain('Sub 1');
        expect(subcategories.map(c => c.name)).toContain('Sub 2');
      });
    });
  });

  describe('Category Updates', () => {
    it('should update category properties', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Original Name',
          color: '#FF0000',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        const [updatedCategory] = await db
          .update(categories)
          .set({
            name: 'Updated Name',
            color: '#00FF00',
            updatedAt: new Date(),
          })
          .where(eq(categories.id, insertedCategory.id))
          .returning();

        expect(updatedCategory.name).toBe('Updated Name');
        expect(updatedCategory.color).toBe('#00FF00');
        expect(updatedCategory.updatedAt.getTime()).toBeGreaterThan(
          insertedCategory.updatedAt.getTime()
        );
      });
    });

    it('should archive a category', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'To Archive',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        const [archivedCategory] = await db
          .update(categories)
          .set({ isArchived: true })
          .where(eq(categories.id, insertedCategory.id))
          .returning();

        expect(archivedCategory.isArchived).toBe(true);
      });
    });

    it('should update display order', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({ email: '<EMAIL>' });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          displayOrder: 1,
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        const [updatedCategory] = await db
          .update(categories)
          .set({ displayOrder: 10 })
          .where(eq(categories.id, insertedCategory.id))
          .returning();

        expect(updatedCategory.displayOrder).toBe(10);
      });
    });
  });

  describe('Category Deletion', () => {
    it('should delete a category', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'To Delete',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        await db
          .delete(categories)
          .where(eq(categories.id, insertedCategory.id));

        const retrievedCategory = await db
          .select()
          .from(categories)
          .where(eq(categories.id, insertedCategory.id));
        expect(retrievedCategory).toHaveLength(0);
      });
    });

    it('should cascade delete categories when user is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Will Be Deleted',
        });
        await db.insert(categories).values(testCategory);

        // Delete the user
        await db.delete(users).where(eq(users.id, insertedUser.id));

        // Category should be deleted due to cascade
        const remainingCategories = await db
          .select()
          .from(categories)
          .where(eq(categories.userId, insertedUser.id));
        expect(remainingCategories).toHaveLength(0);
      });
    });
  });

  describe('Category Validation', () => {
    it('should handle long category names', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const longName = 'a'.repeat(255);
        const testCategory = generateTestCategory(insertedUser.id, {
          name: longName,
        });

        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();
        expect(insertedCategory.name).toBe(longName);
      });
    });

    it('should handle color codes', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000'];

        for (const color of colors) {
          const testCategory = generateTestCategory(insertedUser.id, {
            name: `${color} Category`,
            color,
          });

          const [insertedCategory] = await db
            .insert(categories)
            .values(testCategory)
            .returning();
          expect(insertedCategory.color).toBe(color);
        }
      });
    });

    it('should filter archived categories', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const activeCategory = generateTestCategory(insertedUser.id, {
          name: 'Active',
          isArchived: false,
        });
        const archivedCategory = generateTestCategory(insertedUser.id, {
          name: 'Archived',
          isArchived: true,
        });

        await db.insert(categories).values([activeCategory, archivedCategory]);

        const activeCategories = await db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.userId, insertedUser.id),
              eq(categories.isArchived, false)
            )
          );

        expect(activeCategories).toHaveLength(1);
        expect(activeCategories[0].name).toBe('Active');
      });
    });
  });
});
