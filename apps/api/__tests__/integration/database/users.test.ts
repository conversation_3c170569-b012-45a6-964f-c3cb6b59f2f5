import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
import { describe, expect, it } from 'vitest';
import { users } from '../../../src/database/schema.js';
import { withSeededData, withTransaction } from '../../../src/test-utils/db.js';
import { generateTestUser } from '../../../src/test-utils/test-data.js';

describe('Database Integration - Users Table', () => {
  // Note: No more beforeEach database reset - using transaction isolation instead

  describe('User Creation', () => {
    it('should create a user with all required fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
          firstName: 'John',
          lastName: 'Doe',
          role: 'user' as const,
          emailVerified: true,
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        expect(insertedUser).toBeDefined();
        expect(insertedUser.id).toBeDefined();
        expect(insertedUser.email).toBe(testUser.email);
        expect(insertedUser.firstName).toBe('John');
        expect(insertedUser.lastName).toBe('Doe');
        expect(insertedUser.role).toBe('user');
        expect(insertedUser.emailVerified).toBe(true);
        expect(insertedUser.phoneVerified).toBe(false);
        expect(insertedUser.isDeleted).toBe(false);
        expect(insertedUser.createdAt).toBeDefined();
        expect(insertedUser.updatedAt).toBeDefined();
      });
    });

    it('should create a user with minimal required fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
          firstName: null,
          lastName: null,
          phoneNumber: null,
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        expect(insertedUser).toBeDefined();
        expect(insertedUser.email).toBe(testUser.email);
        expect(insertedUser.firstName).toBeNull();
        expect(insertedUser.lastName).toBeNull();
        expect(insertedUser.phoneNumber).toBeNull();
        expect(insertedUser.role).toBe('user'); // Default value
        expect(insertedUser.emailVerified).toBe(false); // Default value
      });
    });

    it('should create an admin user', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
          role: 'admin' as const,
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        expect(insertedUser.role).toBe('admin');
      });
    });

    it('should enforce unique email constraint', async () => {
      await withTransaction(async db => {
        const email = faker.internet.email().toLowerCase();
        const testUser1 = await generateTestUser({ email });
        const testUser2 = await generateTestUser({ email });

        // First user should be created successfully
        await db.insert(users).values(testUser1);

        // Second user with same email should fail
        await expect(db.insert(users).values(testUser2)).rejects.toThrow();
      });
    });
  });

  describe('User Retrieval', () => {
    it('should retrieve a user by id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();
        const retrievedUser = await db
          .select()
          .from(users)
          .where(eq(users.id, insertedUser.id));

        expect(retrievedUser).toHaveLength(1);
        expect(retrievedUser[0].id).toBe(insertedUser.id);
        expect(retrievedUser[0].email).toBe(testUser.email);
      });
    });

    it('should retrieve a user by email', async () => {
      await withTransaction(async db => {
        const email = faker.internet.email().toLowerCase();
        const testUser = await generateTestUser({ email });

        await db.insert(users).values(testUser);
        const retrievedUser = await db
          .select()
          .from(users)
          .where(eq(users.email, email));

        expect(retrievedUser).toHaveLength(1);
        expect(retrievedUser[0].email).toBe(email);
      });
    });

    it('should return empty array for non-existent user', async () => {
      await withTransaction(async db => {
        const nonExistentId = '00000000-0000-0000-0000-000000000000';
        const retrievedUser = await db
          .select()
          .from(users)
          .where(eq(users.id, nonExistentId));

        expect(retrievedUser).toHaveLength(0);
      });
    });
  });

  describe('User Updates', () => {
    it('should update user fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
          firstName: 'Original',
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const [updatedUser] = await db
          .update(users)
          .set({
            firstName: 'Updated',
            emailVerified: true,
            updatedAt: new Date(),
          })
          .where(eq(users.id, insertedUser.id))
          .returning();

        expect(updatedUser.firstName).toBe('Updated');
        expect(updatedUser.emailVerified).toBe(true);
        expect(updatedUser.updatedAt.getTime()).toBeGreaterThan(
          insertedUser.updatedAt.getTime()
        );
      });
    });

    it('should soft delete a user', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const [deletedUser] = await db
          .update(users)
          .set({ isDeleted: true, updatedAt: new Date() })
          .where(eq(users.id, insertedUser.id))
          .returning();

        expect(deletedUser.isDeleted).toBe(true);
      });
    });
  });

  describe('User Deletion', () => {
    it('should hard delete a user', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        await db.delete(users).where(eq(users.id, insertedUser.id));

        const retrievedUser = await db
          .select()
          .from(users)
          .where(eq(users.id, insertedUser.id));
        expect(retrievedUser).toHaveLength(0);
      });
    });
  });

  describe('User Validation', () => {
    it('should handle long email addresses', async () => {
      await withTransaction(async db => {
        const longEmail = `${'a'.repeat(240)}@example.com`; // 255 characters total
        const testUser = await generateTestUser({ email: longEmail });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();
        expect(insertedUser.email).toBe(longEmail);
      });
    });

    it('should handle long names', async () => {
      await withTransaction(async db => {
        const longName = 'a'.repeat(255);
        const testUser = await generateTestUser({
          firstName: longName,
          lastName: longName,
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();
        expect(insertedUser.firstName).toBe(longName);
        expect(insertedUser.lastName).toBe(longName);
      });
    });

    it('should handle phone number length constraints', async () => {
      await withTransaction(async db => {
        const phoneNumber = '1'.repeat(20); // Maximum length
        const testUser = await generateTestUser({ phoneNumber });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();
        expect(insertedUser.phoneNumber).toBe(phoneNumber);
      });
    });
  });

  describe('User Timestamps', () => {
    it('should set createdAt and updatedAt on creation', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        expect(insertedUser.createdAt).toBeDefined();
        expect(insertedUser.updatedAt).toBeDefined();
        expect(insertedUser.createdAt).toBeInstanceOf(Date);
        expect(insertedUser.updatedAt).toBeInstanceOf(Date);
      });
    });

    it('should update lastLoginAt field', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: faker.internet.email().toLowerCase(),
        });

        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();
        expect(insertedUser.lastLoginAt).toBeNull();

        const loginTime = new Date();
        const [updatedUser] = await db
          .update(users)
          .set({ lastLoginAt: loginTime })
          .where(eq(users.id, insertedUser.id))
          .returning();

        expect(updatedUser.lastLoginAt).toBeDefined();
        expect(updatedUser.lastLoginAt?.getTime()).toBe(loginTime.getTime());
      });
    });
  });
});
