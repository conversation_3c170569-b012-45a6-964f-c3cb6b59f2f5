import { and, eq } from 'drizzle-orm';
import type { InferInsertModel } from 'drizzle-orm';
import { beforeEach, describe, expect, it } from 'vitest';
import { budgets, categories, users } from '../../../src/database/schema.js';
import { getTestDb, withTransaction } from '../../../src/test-utils/db.js';
import {
  generateTestBudget,
  generateTestCategory,
  generateTestUser,
} from '../../../src/test-utils/test-data.js';

describe('Database Integration - Budgets Table', () => {
  const db = getTestDb();

  describe('Budget Creation', () => {
    it('should create a budget with all required fields', async () => {
      await withTransaction(async db => {
        // Create test user
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        // Create test category
        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Groceries',
          type: 'expense',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Monthly Grocery Budget',
          amount: '500.00',
          period: 'monthly',
          categoryId: insertedCategory.id,
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          isRecurring: true,
          notes: 'Budget for monthly groceries',
        });

        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        expect(insertedBudget).toBeDefined();
        expect(insertedBudget.id).toBeDefined();
        expect(insertedBudget.userId).toBe(insertedUser.id);
        expect(insertedBudget.name).toBe('Monthly Grocery Budget');
        expect(insertedBudget.amount).toBe('500.0000');
        expect(insertedBudget.period).toBe('monthly');
        expect(insertedBudget.categoryId).toBe(insertedCategory.id);
        expect(insertedBudget.startDate).toBeInstanceOf(Date);
        expect(insertedBudget.endDate).toBeInstanceOf(Date);
        expect(insertedBudget.isRecurring).toBe(true);
        expect(insertedBudget.isArchived).toBe(false);
        expect(insertedBudget.notes).toBe('Budget for monthly groceries');
        expect(insertedBudget.createdAt).toBeDefined();
        expect(insertedBudget.updatedAt).toBeDefined();
      });
    });

    it('should create budgets with different periods', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const periods = [
          'daily',
          'weekly',
          'monthly',
          'quarterly',
          'yearly',
        ] as const;

        for (const period of periods) {
          const testBudget = generateTestBudget(insertedUser.id, {
            name: `${period} Budget`,
            amount: '1000.00',
            period,
            startDate: new Date('2024-01-01'),
          });

          const [insertedBudget] = await db
            .insert(budgets)
            .values(testBudget)
            .returning();
          expect(insertedBudget.period).toBe(period);
        }
      });
    });

    it('should create budget without category', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Overall Budget',
          amount: '2000.00',
          period: 'monthly',
          categoryId: null,
          startDate: new Date('2024-01-01'),
        });

        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        expect(insertedBudget.categoryId).toBeNull();
        expect(insertedBudget.name).toBe('Overall Budget');
      });
    });

    it('should create budget with minimal required fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Minimal Budget',
          amount: '100.00',
          period: 'monthly',
          startDate: new Date('2024-01-01'),
          endDate: null,
          isRecurring: undefined,
          isArchived: undefined,
          notes: null,
        });

        // Remove undefined values to test defaults
        const budgetData = Object.fromEntries(
          Object.entries(testBudget).filter(
            ([_, value]) => value !== undefined && value !== null
          )
        ) as InferInsertModel<typeof budgets>;

        const [insertedBudget] = await db
          .insert(budgets)
          .values(budgetData)
          .returning();

        expect(insertedBudget.name).toBe('Minimal Budget');
        expect(insertedBudget.endDate).toBeNull();
        expect(insertedBudget.isRecurring).toBe(false); // Default
        expect(insertedBudget.isArchived).toBe(false); // Default
        expect(insertedBudget.notes).toBeNull();
      });
    });

    it('should enforce foreign key constraint with users table', async () => {
      await withTransaction(async db => {
        const nonExistentUserId = '00000000-0000-0000-0000-000000000000';
        const testBudget = generateTestBudget(nonExistentUserId, {
          name: 'Orphan Budget',
        });

        await expect(db.insert(budgets).values(testBudget)).rejects.toThrow();
      });
    });
  });

  describe('Budget Retrieval', () => {
    it('should retrieve budgets by user id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const budget1 = generateTestBudget(insertedUser.id, {
          name: 'Budget 1',
          amount: '500.00',
        });
        const budget2 = generateTestBudget(insertedUser.id, {
          name: 'Budget 2',
          amount: '300.00',
        });

        await db.insert(budgets).values([budget1, budget2]);

        const userBudgets = await db
          .select()
          .from(budgets)
          .where(eq(budgets.userId, insertedUser.id));

        expect(userBudgets).toHaveLength(2);
        expect(userBudgets.map(b => b.name)).toContain('Budget 1');
        expect(userBudgets.map(b => b.name)).toContain('Budget 2');
      });
    });

    it('should retrieve budgets by category id', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const category1 = generateTestCategory(insertedUser.id, {
          name: 'Category 1',
        });
        const category2 = generateTestCategory(insertedUser.id, {
          name: 'Category 2',
        });
        const [insertedCategory1] = await db
          .insert(categories)
          .values(category1)
          .returning();
        const [insertedCategory2] = await db
          .insert(categories)
          .values(category2)
          .returning();

        const budget1 = generateTestBudget(insertedUser.id, {
          name: 'Budget 1',
          categoryId: insertedCategory1.id,
        });
        const budget2 = generateTestBudget(insertedUser.id, {
          name: 'Budget 2',
          categoryId: insertedCategory2.id,
        });

        await db.insert(budgets).values([budget1, budget2]);

        const category1Budgets = await db
          .select()
          .from(budgets)
          .where(eq(budgets.categoryId, insertedCategory1.id));

        expect(category1Budgets).toHaveLength(1);
        expect(category1Budgets[0].name).toBe('Budget 1');
      });
    });

    it('should retrieve budgets by period', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const monthlyBudget = generateTestBudget(insertedUser.id, {
          name: 'Monthly Budget',
          period: 'monthly',
        });
        const yearlyBudget = generateTestBudget(insertedUser.id, {
          name: 'Yearly Budget',
          period: 'yearly',
        });

        await db.insert(budgets).values([monthlyBudget, yearlyBudget]);

        const monthlyBudgets = await db
          .select()
          .from(budgets)
          .where(
            and(
              eq(budgets.userId, insertedUser.id),
              eq(budgets.period, 'monthly')
            )
          );

        expect(monthlyBudgets).toHaveLength(1);
        expect(monthlyBudgets[0].name).toBe('Monthly Budget');
      });
    });

    it('should filter archived budgets', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const activeBudget = generateTestBudget(insertedUser.id, {
          name: 'Active Budget',
          isArchived: false,
        });
        const archivedBudget = generateTestBudget(insertedUser.id, {
          name: 'Archived Budget',
          isArchived: true,
        });

        await db.insert(budgets).values([activeBudget, archivedBudget]);

        const activeBudgets = await db
          .select()
          .from(budgets)
          .where(
            and(
              eq(budgets.userId, insertedUser.id),
              eq(budgets.isArchived, false)
            )
          );

        expect(activeBudgets).toHaveLength(1);
        expect(activeBudgets[0].name).toBe('Active Budget');
      });
    });
  });

  describe('Budget Updates', () => {
    it('should update budget fields', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Original Budget',
          amount: '500.00',
        });
        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        const [updatedBudget] = await db
          .update(budgets)
          .set({
            name: 'Updated Budget',
            amount: '750.00',
            updatedAt: new Date(),
          })
          .where(eq(budgets.id, insertedBudget.id))
          .returning();

        expect(updatedBudget.name).toBe('Updated Budget');
        expect(updatedBudget.amount).toBe('750.0000');
        expect(updatedBudget.updatedAt.getTime()).toBeGreaterThan(
          insertedBudget.updatedAt.getTime()
        );
      });
    });

    it('should archive a budget', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'To Archive',
        });
        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        const [archivedBudget] = await db
          .update(budgets)
          .set({ isArchived: true })
          .where(eq(budgets.id, insertedBudget.id))
          .returning();

        expect(archivedBudget.isArchived).toBe(true);
      });
    });

    it('should update budget period and dates', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Period Test',
          period: 'monthly',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        });
        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        const newStartDate = new Date('2024-06-01');
        const newEndDate = new Date('2025-05-31');

        const [updatedBudget] = await db
          .update(budgets)
          .set({
            period: 'yearly',
            startDate: newStartDate,
            endDate: newEndDate,
          })
          .where(eq(budgets.id, insertedBudget.id))
          .returning();

        expect(updatedBudget.period).toBe('yearly');
        expect(updatedBudget.startDate.getTime()).toBe(newStartDate.getTime());
        expect(updatedBudget.endDate?.getTime()).toBe(newEndDate.getTime());
      });
    });
  });

  describe('Budget Deletion', () => {
    it('should delete a budget', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'To Delete',
        });
        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        await db.delete(budgets).where(eq(budgets.id, insertedBudget.id));

        const retrievedBudget = await db
          .select()
          .from(budgets)
          .where(eq(budgets.id, insertedBudget.id));
        expect(retrievedBudget).toHaveLength(0);
      });
    });

    it('should cascade delete budgets when user is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Will Be Deleted',
        });
        await db.insert(budgets).values(testBudget);

        // Delete the user
        await db.delete(users).where(eq(users.id, insertedUser.id));

        // Budget should be deleted due to cascade
        const remainingBudgets = await db
          .select()
          .from(budgets)
          .where(eq(budgets.userId, insertedUser.id));
        expect(remainingBudgets).toHaveLength(0);
      });
    });

    it('should set category to null when category is deleted', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testCategory = generateTestCategory(insertedUser.id, {
          name: 'Test Category',
        });
        const [insertedCategory] = await db
          .insert(categories)
          .values(testCategory)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Category Budget',
          categoryId: insertedCategory.id,
        });
        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        // Delete the category
        await db
          .delete(categories)
          .where(eq(categories.id, insertedCategory.id));

        // Budget should have categoryId set to null
        const updatedBudget = await db
          .select()
          .from(budgets)
          .where(eq(budgets.id, insertedBudget.id));
        expect(updatedBudget[0].categoryId).toBeNull();
      });
    });
  });

  describe('Budget Validation', () => {
    it('should handle large budget amounts', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Large Budget',
          amount: '999999999999999.9999',
        });

        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();
        expect(insertedBudget.amount).toBe('999999999999999.9999');
      });
    });

    it('should handle long budget names', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const longName = 'a'.repeat(255);
        const testBudget = generateTestBudget(insertedUser.id, {
          name: longName,
        });

        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();
        expect(insertedBudget.name).toBe(longName);
      });
    });

    it('should handle recurring and non-recurring budgets', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const recurringBudget = generateTestBudget(insertedUser.id, {
          name: 'Recurring Budget',
          isRecurring: true,
        });
        const oneTimeBudget = generateTestBudget(insertedUser.id, {
          name: 'One Time Budget',
          isRecurring: false,
        });

        const [insertedRecurring] = await db
          .insert(budgets)
          .values(recurringBudget)
          .returning();
        const [insertedOneTime] = await db
          .insert(budgets)
          .values(oneTimeBudget)
          .returning();

        expect(insertedRecurring.isRecurring).toBe(true);
        expect(insertedOneTime.isRecurring).toBe(false);
      });
    });

    it('should handle budgets with and without end dates', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const budgetWithEnd = generateTestBudget(insertedUser.id, {
          name: 'Budget With End',
          endDate: new Date('2024-12-31'),
        });
        const budgetWithoutEnd = generateTestBudget(insertedUser.id, {
          name: 'Budget Without End',
          endDate: null,
        });

        const [insertedWithEnd] = await db
          .insert(budgets)
          .values(budgetWithEnd)
          .returning();
        const [insertedWithoutEnd] = await db
          .insert(budgets)
          .values(budgetWithoutEnd)
          .returning();

        expect(insertedWithEnd.endDate).toBeInstanceOf(Date);
        expect(insertedWithoutEnd.endDate).toBeNull();
      });
    });
  });

  describe('Budget Timestamps', () => {
    it('should set createdAt and updatedAt on creation', async () => {
      await withTransaction(async db => {
        const testUser = await generateTestUser({
          email: '<EMAIL>',
        });
        const [insertedUser] = await db
          .insert(users)
          .values(testUser)
          .returning();

        const testBudget = generateTestBudget(insertedUser.id, {
          name: 'Timestamp Test',
        });

        const [insertedBudget] = await db
          .insert(budgets)
          .values(testBudget)
          .returning();

        expect(insertedBudget.createdAt).toBeDefined();
        expect(insertedBudget.updatedAt).toBeDefined();
        expect(insertedBudget.createdAt).toBeInstanceOf(Date);
        expect(insertedBudget.updatedAt).toBeInstanceOf(Date);
      });
    });
  });
});
