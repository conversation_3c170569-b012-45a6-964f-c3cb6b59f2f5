import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
// Integration tests for GraphQL authentication flow
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { userSettings, users } from '../../../src/database/schema.js';
import { verifyToken } from '../../../src/lib/auth/jwt.js';
import { generateTestToken } from '../../../src/test-utils/auth-helpers.js';
import { withSeededData, withTransaction } from '../../../src/test-utils/db.js';
import {
  type TestGraphQLClient,
  createTestClient,
  gqlQueries,
} from '../../../src/test-utils/gql-client.js';

describe('GraphQL Authentication Flow Integration Tests', () => {
  let client: TestGraphQLClient;

  beforeAll(async () => {
    client = await createTestClient();
  });

  afterAll(async () => {
    // Clean up any resources if needed
  });

  describe('User Registration', () => {
    it('should successfully register a new user via GraphQL', async () => {
      await withTransaction(async db => {
        const registerInput = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'GraphQL',
          lastName: 'User',
        };

        const response = await client.mutate(gqlQueries.register, {
          input: registerInput,
        });

        // Verify no GraphQL errors
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.register).toBeDefined();

        const { token, user } = response.data.register;

        // Verify response structure
        expect(typeof token).toBe('string');
        expect(user).toMatchObject({
          email: registerInput.email.toLowerCase(),
          firstName: registerInput.firstName,
          lastName: registerInput.lastName,
          role: 'user',
          emailVerified: false,
        });
        expect(user.id).toBeDefined();
        expect(user.createdAt).toBeDefined();
        expect(user.updatedAt).toBeDefined();

        // Verify JWT token is valid
        const tokenPayload = verifyToken(token);
        expect(tokenPayload).toBeDefined();
        expect(tokenPayload!.userId).toBe(user.id);
        expect(tokenPayload!.email).toBe(user.email);
        expect(tokenPayload!.role).toBe(user.role);

        // Verify user was created in database
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, registerInput.email.toLowerCase()),
        });
        expect(dbUser).toBeDefined();
        expect(dbUser!.email).toBe(registerInput.email.toLowerCase());
        expect(dbUser!.passwordHash).toBeDefined();
        expect(dbUser!.passwordHash).not.toBe(registerInput.password); // Should be hashed

        // Verify user settings were created
        const dbUserSettings = await db.query.userSettings.findFirst({
          where: eq(userSettings.userId, dbUser!.id),
        });
        expect(dbUserSettings).toBeDefined();
        expect(dbUserSettings!.defaultCurrency).toBe('USD');
        expect(dbUserSettings!.theme).toBe('light');
      });
    });

    it('should return error for duplicate email registration', async () => {
      await withTransaction(async db => {
        const registerInput = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Duplicate',
          lastName: 'User',
        };

        // Register first user
        const firstResponse = await client.mutate(gqlQueries.register, {
          input: registerInput,
        });
        expect(firstResponse.errors).toBeUndefined();

        // Try to register with same email
        const secondResponse = await client.mutate(gqlQueries.register, {
          input: registerInput,
        });

        // Should have GraphQL errors
        expect(secondResponse.errors).toBeDefined();
        expect(secondResponse.errors).toHaveLength(1);
        expect(secondResponse.errors![0].message).toContain(
          'User with this email already exists'
        );
      });
    });

    it('should return validation error for weak password', async () => {
      await withTransaction(async db => {
        const registerInput = {
          email: faker.internet.email().toLowerCase(),
          password: 'weak',
          firstName: 'Weak',
          lastName: 'Password',
        };

        const response = await client.mutate(gqlQueries.register, {
          input: registerInput,
        });

        // Should have GraphQL errors
        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain('Validation failed');
      });
    });

    it('should return validation error for invalid email', async () => {
      await withTransaction(async db => {
        const registerInput = {
          email: 'invalid-email',
          password: 'SecurePass123!',
          firstName: 'Invalid',
          lastName: 'Email',
        };

        const response = await client.mutate(gqlQueries.register, {
          input: registerInput,
        });

        // Should have GraphQL errors
        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain('Validation failed');
      });
    });

    it('should handle registration with minimal data', async () => {
      await withTransaction(async db => {
        const registerInput = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          // No firstName or lastName
        };

        const response = await client.mutate(gqlQueries.register, {
          input: registerInput,
        });

        // Verify no GraphQL errors
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();

        const { user } = response.data.register;
        expect(user.email).toBe(registerInput.email.toLowerCase());
        expect(user.firstName).toBeNull();
        expect(user.lastName).toBeNull();
      });
    });
  });

  describe('User Login', () => {
    it('should successfully login with valid credentials via GraphQL', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Login',
          lastName: 'Test',
        };

        // Register user first
        await client.mutate(gqlQueries.register, {
          input: userData,
        });

        // Login with same credentials
        const loginInput = {
          email: userData.email,
          password: userData.password,
        };

        const response = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        // Verify no GraphQL errors
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.login).toBeDefined();

        const { token, user } = response.data.login;

        // Verify response structure
        expect(typeof token).toBe('string');
        expect(user).toMatchObject({
          email: userData.email.toLowerCase(),
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
        });

        // Verify JWT token is valid
        const tokenPayload = verifyToken(token);
        expect(tokenPayload).toBeDefined();
        expect(tokenPayload!.userId).toBe(user.id);
        expect(tokenPayload!.email).toBe(user.email);
      });
    });

    it('should return error for non-existent user', async () => {
      await withTransaction(async db => {
        const loginInput = {
          email: faker.internet.email().toLowerCase(),
          password: 'AnyPassword123!',
        };

        const response = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        // Should have GraphQL errors
        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain(
          'Invalid email or password'
        );
      });
    });

    it('should return error for wrong password', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'CorrectPass123!',
          firstName: 'Wrong',
          lastName: 'Password',
        };

        // Register user
        await client.mutate(gqlQueries.register, {
          input: userData,
        });

        // Try to login with wrong password
        const loginInput = {
          email: userData.email,
          password: 'WrongPass123!',
        };

        const response = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        // Should have GraphQL errors
        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain(
          'Invalid email or password'
        );
      });
    });

    it('should handle email case insensitivity during login', async () => {
      await withTransaction(async db => {
        const baseEmail = faker.internet.email().toLowerCase();
        const userData = {
          email: baseEmail,
          password: 'SecurePass123!',
          firstName: 'Case',
          lastName: 'Login',
        };

        // Register user
        await client.mutate(gqlQueries.register, {
          input: userData,
        });

        // Login with different case
        const loginInput = {
          email: baseEmail.toUpperCase(),
          password: userData.password,
        };

        const response = await client.mutate(gqlQueries.login, {
          input: loginInput,
        });

        // Verify successful login
        expect(response.errors).toBeUndefined();
        expect(response.data.login.user.email).toBe(baseEmail);
      });
    });
  });

  describe('Me Query (Protected Route)', () => {
    it('should return current user when authenticated', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Me',
          lastName: 'Query',
        };

        // Register and login user
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        const { token, user: registeredUser } = registerResponse.data.register;

        // Set token for authenticated request
        client.setToken(token);

        // Query current user
        const response = await client.query(gqlQueries.me);

        // Verify no GraphQL errors
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.me).toBeDefined();

        const user = response.data.me;
        expect(user).toMatchObject({
          id: registeredUser.id,
          email: userData.email.toLowerCase(),
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
          emailVerified: false,
        });
      });
    });

    it('should return null when not authenticated', async () => {
      await withTransaction(async db => {
        // Clear any existing token
        client.setToken(null);

        // Query current user without authentication
        const response = await client.query(gqlQueries.me);

        // Verify no GraphQL errors but null data
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.me).toBeNull();
      });
    });

    it('should return null with invalid token', async () => {
      await withTransaction(async db => {
        // Set invalid token
        client.setToken('invalid-token');

        // Query current user with invalid token
        const response = await client.query(gqlQueries.me);

        // Verify no GraphQL errors but null data
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.me).toBeNull();
      });
    });

    it('should return null with expired token', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Expired',
          lastName: 'Token',
        };

        // Register user
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        const { user } = registerResponse.data.register;

        // Generate expired token (exp in the past)
        const expiredToken = generateTestToken(user.id, user.email, user.role);
        // Note: In a real test, we'd need to mock the JWT library to create an actually expired token
        // For now, we'll test with an invalid token format

        client.setToken('expired.token.here');

        // Query current user with expired token
        const response = await client.query(gqlQueries.me);

        // Verify no GraphQL errors but null data
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.me).toBeNull();
      });
    });
  });

  describe('Refresh Token', () => {
    it('should refresh token for authenticated user', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Refresh',
          lastName: 'Token',
        };

        // Register and login user
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        const { token, user: registeredUser } = registerResponse.data.register;

        // Set token for authenticated request
        client.setToken(token);

        // Add a small delay to ensure different timestamps in JWT tokens
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Refresh token
        const response = await client.mutate(gqlQueries.refreshToken);

        // Verify no GraphQL errors
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.refreshToken).toBeDefined();

        const { token: newToken, user } = response.data.refreshToken;

        // Verify response structure
        expect(typeof newToken).toBe('string');
        expect(user).toMatchObject({
          id: registeredUser.id,
          email: userData.email.toLowerCase(),
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: 'user',
        });

        // Verify the new token is a valid JWT token
        expect(typeof newToken).toBe('string');
        expect(newToken.length).toBeGreaterThan(0);

        // Verify the token is different from the original token (new token generated)
        expect(newToken).not.toBe(token);

        // Verify the token can be decoded and contains correct user info
        const decoded = verifyToken(newToken);
        expect(decoded).toBeDefined();
        expect(decoded!.userId).toBe(registeredUser.id);
        expect(decoded!.email).toBe(userData.email.toLowerCase());
        expect(decoded!.role).toBe('user');
      });
    });

    it('should return error when not authenticated', async () => {
      await withTransaction(async db => {
        // Clear any existing token
        client.setToken(null);

        // Try to refresh token without authentication
        const response = await client.mutate(gqlQueries.refreshToken);

        // Should have GraphQL errors
        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain('Not authenticated');
      });
    });

    it('should return error with invalid token', async () => {
      await withTransaction(async db => {
        // Set invalid token
        client.setToken('invalid-token');

        // Try to refresh token with invalid token
        const response = await client.mutate(gqlQueries.refreshToken);

        // Should have GraphQL errors
        expect(response.errors).toBeDefined();
        expect(response.errors).toHaveLength(1);
        expect(response.errors![0].message).toContain('Not authenticated');
      });
    });
  });

  describe('Logout', () => {
    it('should successfully logout authenticated user', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Logout',
          lastName: 'Test',
        };

        // Register and login user
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        const { token } = registerResponse.data.register;

        // Set token for authenticated request
        client.setToken(token);

        // Logout
        const response = await client.mutate(gqlQueries.logout);

        // Verify no GraphQL errors
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.logout).toBe(true);
      });
    });

    it('should successfully logout even when not authenticated', async () => {
      await withTransaction(async db => {
        // Clear any existing token
        client.setToken(null);

        // Logout without authentication
        const response = await client.mutate(gqlQueries.logout);

        // Verify no GraphQL errors (logout is idempotent)
        expect(response.errors).toBeUndefined();
        expect(response.data).toBeDefined();
        expect(response.data.logout).toBe(true);
      });
    });
  });

  describe('End-to-End Authentication Flow', () => {
    it('should complete full authentication lifecycle', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'E2E',
          lastName: 'Test',
        };

        // Step 1: Register user
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });

        expect(registerResponse.errors).toBeUndefined();
        const { token: registerToken, user: registeredUser } =
          registerResponse.data.register;
        expect(typeof registerToken).toBe('string');
        expect(registeredUser.email).toBe(userData.email.toLowerCase());

        // Step 2: Login with registered credentials
        const loginResponse = await client.mutate(gqlQueries.login, {
          input: {
            email: userData.email,
            password: userData.password,
          },
        });

        expect(loginResponse.errors).toBeUndefined();
        const { token: loginToken, user: loggedInUser } =
          loginResponse.data.login;
        expect(typeof loginToken).toBe('string');
        expect(loggedInUser.id).toBe(registeredUser.id);

        // Step 3: Use token to access protected route
        client.setToken(loginToken);
        const meResponse = await client.query(gqlQueries.me);

        expect(meResponse.errors).toBeUndefined();
        expect(meResponse.data.me).toMatchObject({
          id: registeredUser.id,
          email: userData.email.toLowerCase(),
          firstName: userData.firstName,
          lastName: userData.lastName,
        });

        // Step 4: Refresh token
        const refreshResponse = await client.mutate(gqlQueries.refreshToken);

        expect(refreshResponse.errors).toBeUndefined();
        expect(refreshResponse.data.refreshToken.user.id).toBe(
          registeredUser.id
        );

        // Step 5: Logout
        const logoutResponse = await client.mutate(gqlQueries.logout);

        expect(logoutResponse.errors).toBeUndefined();
        expect(logoutResponse.data.logout).toBe(true);

        // Step 6: Verify access is denied after logout (clear token)
        client.setToken(null);
        const meAfterLogoutResponse = await client.query(gqlQueries.me);

        expect(meAfterLogoutResponse.errors).toBeUndefined();
        expect(meAfterLogoutResponse.data.me).toBeNull();
      });
    });

    it('should handle authentication state transitions correctly', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'State',
          lastName: 'Transition',
        };

        // Start unauthenticated
        client.setToken(null);
        let meResponse = await client.query(gqlQueries.me);
        expect(meResponse.data.me).toBeNull();

        // Register and become authenticated
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        const { token } = registerResponse.data.register;
        client.setToken(token);

        // Verify authenticated state
        meResponse = await client.query(gqlQueries.me);
        expect(meResponse.data.me).toBeDefined();
        expect(meResponse.data.me.email).toBe(userData.email.toLowerCase());

        // Clear token and verify unauthenticated state
        client.setToken(null);
        meResponse = await client.query(gqlQueries.me);
        expect(meResponse.data.me).toBeNull();

        // Login again and verify authenticated state
        const loginResponse = await client.mutate(gqlQueries.login, {
          input: {
            email: userData.email,
            password: userData.password,
          },
        });
        client.setToken(loginResponse.data.login.token);

        meResponse = await client.query(gqlQueries.me);
        expect(meResponse.data.me).toBeDefined();
        expect(meResponse.data.me.email).toBe(userData.email.toLowerCase());
      });
    });

    it('should maintain user data consistency across operations', async () => {
      await withTransaction(async db => {
        const userData = {
          email: faker.internet.email().toLowerCase(),
          password: 'SecurePass123!',
          firstName: 'Consistency',
          lastName: 'Test',
        };

        // Register user
        const registerResponse = await client.mutate(gqlQueries.register, {
          input: userData,
        });
        const registeredUser = registerResponse.data.register.user;

        // Login and verify same user data
        const loginResponse = await client.mutate(gqlQueries.login, {
          input: {
            email: userData.email,
            password: userData.password,
          },
        });
        const loggedInUser = loginResponse.data.login.user;

        expect(loggedInUser).toMatchObject({
          id: registeredUser.id,
          email: registeredUser.email,
          firstName: registeredUser.firstName,
          lastName: registeredUser.lastName,
          role: registeredUser.role,
          emailVerified: registeredUser.emailVerified,
        });

        // Query me and verify same user data
        client.setToken(loginResponse.data.login.token);
        const meResponse = await client.query(gqlQueries.me);
        const currentUser = meResponse.data.me;

        expect(currentUser).toMatchObject({
          id: registeredUser.id,
          email: registeredUser.email,
          firstName: registeredUser.firstName,
          lastName: registeredUser.lastName,
          role: registeredUser.role,
          emailVerified: registeredUser.emailVerified,
        });

        // Refresh token and verify same user data
        const refreshResponse = await client.mutate(gqlQueries.refreshToken);
        const refreshedUser = refreshResponse.data.refreshToken.user;

        expect(refreshedUser).toMatchObject({
          id: registeredUser.id,
          email: registeredUser.email,
          firstName: registeredUser.firstName,
          lastName: registeredUser.lastName,
          role: registeredUser.role,
          emailVerified: registeredUser.emailVerified,
        });
      });
    });
  });
});
