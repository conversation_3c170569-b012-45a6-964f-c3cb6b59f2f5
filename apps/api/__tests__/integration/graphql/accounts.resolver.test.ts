import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { faker } from '@faker-js/faker';
import { eq } from 'drizzle-orm';
import { accounts, users } from '../../../src/database/schema.js';
import { withSeededData } from '../../../src/test-utils/db.js';
import { createTestClient, type TestGraphQLClient } from '../../../src/test-utils/gql-client.js';
import { generateTestToken } from '../../../src/test-utils/auth-helpers.js';

describe('Integration Tests - Accounts GraphQL Resolver', () => {
  let testUserId: string;
  let testAccountId: string;
  let client: TestGraphQLClient;
  let authToken: string;

  beforeAll(async () => {
    // Create test client
    client = await createTestClient();
  });

  afterAll(async () => {
    // Clean up any resources if needed
  });

  beforeEach(async () => {
    // Create test user that persists for the service functions
    await withSeededData(async db => {
      const [testUser] = await db
        .insert(users)
        .values({
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          passwordHash: faker.internet.password(),
          emailVerified: true,
        })
        .returning();
      testUserId = testUser.id;
      
      // Generate auth token for the test user
      authToken = generateTestToken(testUser.id, testUser.email, 'user');
    });
  });

  afterEach(async () => {
    // Clean up test data
    await withSeededData(async db => {
      if (testAccountId) {
        await db.delete(accounts).where(eq(accounts.id, testAccountId));
      }
      await db.delete(accounts).where(eq(accounts.userId, testUserId));
      await db.delete(users).where(eq(users.id, testUserId));
    });
  });

  describe('Query: accounts', () => {
    it('should return user accounts', async () => {
      // Create test account
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Test Account',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000.0000',
            currentBalance: '1000.0000',
            includeInNetWorth: true,
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });

      const query = `
        query GetAccounts {
          accounts {
            id
            name
            type
            currency
            initialBalance
            currentBalance
            includeInNetWorth
            isArchived
          }
        }
      `;

      const response = await client.setToken(authToken).query(query);

      expect(response.errors).toBeUndefined();
      expect((response.data as any)?.accounts).toHaveLength(1);
      
      const account = (response.data as any)?.accounts[0];
      expect(account.id).toBe(testAccountId);
      expect(account.name).toBe('Test Account');
      expect(account.type).toBe('CHECKING');
      expect(account.currency).toBe('USD');
      expect(account.initialBalance).toBe(1000);
      expect(account.currentBalance).toBe(1000);
      expect(account.includeInNetWorth).toBe(true);
      expect(account.isArchived).toBe(false);
    });

    it('should return empty array when user has no accounts', async () => {
      const query = `
        query GetAccounts {
          accounts {
            id
            name
          }
        }
      `;

      const response = await client.setToken(authToken).query(query);

      expect(response.errors).toBeUndefined();
      expect(response.data?.accounts).toEqual([]);
    });

    it('should require authentication', async () => {
      const query = `
        query GetAccounts {
          accounts {
            id
            name
          }
        }
      `;

      const response = await client.setToken(null).query(query);

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Authentication required');
    });
  });

  describe('Query: account', () => {
    beforeEach(async () => {
      // Create test account for these tests
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Test Account',
            type: 'savings',
            currency: 'EUR',
            initialBalance: '2500.5000',
            currentBalance: '2500.5000',
            includeInNetWorth: true,
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });
    });

    it('should return specific account by ID', async () => {
      const query = `
        query GetAccount($id: ID!) {
          account(id: $id) {
            id
            name
            type
            currency
            initialBalance
            currentBalance
          }
        }
      `;

      const response = await client.setToken(authToken).query(query, { id: testAccountId });

      expect(response.errors).toBeUndefined();
      
      const account = response.data?.account;
      expect(account).not.toBeNull();
      expect(account.id).toBe(testAccountId);
      expect(account.name).toBe('Test Account');
      expect(account.type).toBe('SAVINGS');
      expect(account.currency).toBe('EUR');
      expect(account.initialBalance).toBe(2500.5);
      expect(account.currentBalance).toBe(2500.5);
    });

    it('should return null for non-existent account', async () => {
      const nonExistentId = faker.string.uuid();
      const query = `
        query GetAccount($id: ID!) {
          account(id: $id) {
            id
            name
          }
        }
      `;

      const response = await client.setToken(authToken).query(query, { id: nonExistentId });

      expect(response.errors).toBeUndefined();
      expect(response.data?.account).toBeNull();
    });

    it('should require authentication', async () => {
      const query = `
        query GetAccount($id: ID!) {
          account(id: $id) {
            id
            name
          }
        }
      `;

      const response = await client.setToken(null).query(query, { id: testAccountId });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Authentication required');
    });
  });

  describe('Mutation: createAccount', () => {
    it('should create account with minimal data', async () => {
      const mutation = `
        mutation CreateAccount($input: CreateAccountInput!) {
          createAccount(input: $input) {
            id
            name
            type
            currency
            initialBalance
            currentBalance
            includeInNetWorth
            isArchived
          }
        }
      `;

      const input = {
        name: 'New Checking Account',
        type: 'CHECKING',
        currency: 'USD',
        initialBalance: 0,
        includeInNetWorth: true,
      };

      const response = await client.setToken(authToken).mutate(mutation, { input });

      expect(response.errors).toBeUndefined();
      
      const account = response.data?.createAccount;
      expect(account).not.toBeNull();
      expect(account.id).toBeDefined();
      expect(account.name).toBe('New Checking Account');
      expect(account.type).toBe('CHECKING');
      expect(account.currency).toBe('USD');
      expect(account.initialBalance).toBe(0);
      expect(account.currentBalance).toBe(0);
      expect(account.includeInNetWorth).toBe(true);
      expect(account.isArchived).toBe(false);

      testAccountId = account.id; // For cleanup
    });

    it('should create account with all fields', async () => {
      const mutation = `
        mutation CreateAccount($input: CreateAccountInput!) {
          createAccount(input: $input) {
            id
            name
            type
            currency
            initialBalance
            currentBalance
            includeInNetWorth
          }
        }
      `;

      const input = {
        name: 'Complete Savings Account',
        type: 'SAVINGS',
        currency: 'EUR',
        initialBalance: 5000.75,
        includeInNetWorth: false,
      };

      const response = await client.setToken(authToken).mutate(mutation, { input });

      expect(response.errors).toBeUndefined();
      
      const account = response.data?.createAccount;
      expect(account.name).toBe('Complete Savings Account');
      expect(account.type).toBe('SAVINGS');
      expect(account.currency).toBe('EUR');
      expect(account.initialBalance).toBe(5000.75);
      expect(account.currentBalance).toBe(5000.75);
      expect(account.includeInNetWorth).toBe(false);

      testAccountId = account.id; // For cleanup
    });

    it('should validate required fields', async () => {
      const mutation = `
        mutation CreateAccount($input: CreateAccountInput!) {
          createAccount(input: $input) {
            id
            name
          }
        }
      `;

      const input = {
        // Missing required fields
        type: 'CHECKING',
      };

      const response = await client.setToken(authToken).mutate(mutation, { input });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Field "name" of required type "String!" was not provided');
    });

    it('should require authentication', async () => {
      const mutation = `
        mutation CreateAccount($input: CreateAccountInput!) {
          createAccount(input: $input) {
            id
            name
          }
        }
      `;

      const input = {
        name: 'Test Account',
        type: 'CHECKING',
        currency: 'USD',
        initialBalance: 0,
        includeInNetWorth: true,
      };

      const response = await client.setToken(null).mutate(mutation, { input });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Authentication required');
    });
  });

  describe('Mutation: updateAccount', () => {
    beforeEach(async () => {
      // Create test account for these tests
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Original Account',
            type: 'checking',
            currency: 'USD',
            initialBalance: '1000.0000',
            currentBalance: '1000.0000',
            includeInNetWorth: true,
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });
    });

    it('should update account fields', async () => {
      const mutation = `
        mutation UpdateAccount($id: ID!, $input: UpdateAccountInput!) {
          updateAccount(id: $id, input: $input) {
            id
            name
            type
            currency
            includeInNetWorth
          }
        }
      `;

      const input = {
        name: 'Updated Account Name',
        type: 'SAVINGS',
        currency: 'EUR',
        includeInNetWorth: false,
      };

      const response = await client.setToken(authToken).mutate(mutation, { id: testAccountId, input });

      expect(response.errors).toBeUndefined();
      
      const account = response.data?.updateAccount;
      expect(account.id).toBe(testAccountId);
      expect(account.name).toBe('Updated Account Name');
      expect(account.type).toBe('SAVINGS');
      expect(account.currency).toBe('EUR');
      expect(account.includeInNetWorth).toBe(false);
    });

    it('should return error for non-existent account', async () => {
      const nonExistentId = faker.string.uuid();
      const mutation = `
        mutation UpdateAccount($id: ID!, $input: UpdateAccountInput!) {
          updateAccount(id: $id, input: $input) {
            id
            name
          }
        }
      `;

      const input = {
        name: 'Updated Name',
      };

      const response = await client.setToken(authToken).mutate(mutation, { id: nonExistentId, input });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Account not found');
    });

    it('should require authentication', async () => {
      const mutation = `
        mutation UpdateAccount($id: ID!, $input: UpdateAccountInput!) {
          updateAccount(id: $id, input: $input) {
            id
            name
          }
        }
      `;

      const input = {
        name: 'Updated Name',
      };

      const response = await client.setToken(null).mutate(mutation, { id: testAccountId, input });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Authentication required');
    });
  });

  describe('Mutation: deleteAccount', () => {
    beforeEach(async () => {
      // Create test account for these tests
      await withSeededData(async db => {
        const [account] = await db
          .insert(accounts)
          .values({
            userId: testUserId,
            name: 'Account to Delete',
            type: 'checking',
            currency: 'USD',
            initialBalance: '500.0000',
            currentBalance: '500.0000',
            includeInNetWorth: true,
            isArchived: false,
          })
          .returning();
        testAccountId = account.id;
      });
    });

    it('should soft delete account', async () => {
      const mutation = `
        mutation DeleteAccount($id: ID!) {
          deleteAccount(id: $id)
        }
      `;

      const response = await client.setToken(authToken).mutate(mutation, { id: testAccountId });

      expect(response.errors).toBeUndefined();
      expect(response.data?.deleteAccount).toBe(true);

      // Verify account is archived
      await withSeededData(async db => {
        const archivedAccount = await db
          .select()
          .from(accounts)
          .where(eq(accounts.id, testAccountId))
          .limit(1);
        
        expect(archivedAccount).toHaveLength(1);
        expect(archivedAccount[0].isArchived).toBe(true);
      });
    });

    it('should return error for non-existent account', async () => {
      const nonExistentId = faker.string.uuid();
      const mutation = `
        mutation DeleteAccount($id: ID!) {
          deleteAccount(id: $id)
        }
      `;

      const response = await client.setToken(authToken).mutate(mutation, { id: nonExistentId });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Account not found');
    });

    it('should require authentication', async () => {
      const mutation = `
        mutation DeleteAccount($id: ID!) {
          deleteAccount(id: $id)
        }
      `;

      const response = await client.setToken(null).mutate(mutation, { id: testAccountId });

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].message).toContain('Authentication required');
    });
  });
}); 