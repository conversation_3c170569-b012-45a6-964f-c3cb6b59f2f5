import { eq } from 'drizzle-orm';
import type { FastifyInstance } from 'fastify';
// Integration tests for REST authentication routes
import { afterAll, beforeAll, describe, expect, it, vi } from 'vitest';
import { userSettings, users } from '../../../src/database/schema.js';
import { createServer } from '../../../src/server.js';
import { withTransaction } from '../../../src/test-utils/db.js';

describe('REST Authentication Routes Integration Tests', () => {
  let app: FastifyInstance;
  let authConfig: typeof import('../../../src/config/auth.js').authConfig;

  beforeAll(async () => {
    // Clear module cache to ensure fresh config with test environment variables
    vi.resetModules();

    // Dynamically import auth config to pick up test environment variables
    const authModule = await import('../../../src/config/auth.js');
    authConfig = authModule.authConfig;

    app = await createServer();
    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Google OAuth Flow', () => {
    it('should initiate Google OAuth flow', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/google',
      });

      // Should redirect to Google OAuth URL (in test mode)
      expect(response.statusCode).toBe(302);
      expect(response.headers.location).toContain(
        'accounts.google.com/oauth/authorize'
      );
      expect(response.headers.location).toContain('client_id=mock-client-id');
      expect(response.headers.location).toContain('redirect_uri=');
      expect(response.headers.location).toContain('auth%2Fgoogle%2Fcallback');
    });

    it('should handle Google OAuth callback successfully', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code',
        });

        // Should redirect after successful authentication
        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/');

        // Should set authentication cookie
        const cookies = response.cookies;
        expect(cookies).toBeDefined();
        const authCookie = cookies.find(
          cookie => cookie.name === authConfig.AUTH_COOKIE_NAME
        );
        expect(authCookie).toBeDefined();
        expect(authCookie!.value).toBeDefined();
        expect(authCookie!.httpOnly).toBe(authConfig.AUTH_COOKIE_HTTP_ONLY);
        expect(authCookie!.secure).toBe(authConfig.AUTH_COOKIE_SECURE);

        // Verify user was created in database
        const user = await db.query.users.findFirst({
          where: eq(users.email, '<EMAIL>'),
        });
        expect(user).toBeDefined();
        expect(user!.email).toBe('<EMAIL>');
        expect(user!.firstName).toBe('Google');
        expect(user!.lastName).toBe('User');
        expect(user!.emailVerified).toBe(true); // OAuth users have verified emails
        expect(user!.passwordHash).toBeNull(); // OAuth users don't have passwords
      });
    });

    it('should handle Google OAuth callback with mobile platform', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code&platform=mobile',
        });

        // Should redirect to mobile app with token
        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toContain(
          authConfig.AUTH_MOBILE_REDIRECT_URL
        );
        expect(response.headers.location).toContain('token=');

        // Extract token from redirect URL
        const redirectUrl = response.headers.location as string;
        const tokenMatch = redirectUrl.match(/token=([^&]+)/);
        expect(tokenMatch).toBeDefined();
        const token = tokenMatch![1];
        expect(token).toBeDefined();

        // Should still set authentication cookie
        const cookies = response.cookies;
        expect(cookies).toBeDefined();
        const authCookie = cookies.find(
          cookie => cookie.name === authConfig.AUTH_COOKIE_NAME
        );
        expect(authCookie).toBeDefined();
      });
    });

    it('should handle existing Google user on subsequent logins', async () => {
      await withTransaction(async db => {
        // First login - creates user
        const firstResponse = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code',
        });
        expect(firstResponse.statusCode).toBe(302);

        // Second login - should find existing user
        const secondResponse = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code',
        });
        expect(secondResponse.statusCode).toBe(302);
        expect(secondResponse.headers.location).toBe('/');

        // Should only have one user in database
        const users_list = await db.query.users.findMany({
          where: eq(users.email, '<EMAIL>'),
        });
        expect(users_list).toHaveLength(1);
      });
    });
  });

  describe('Apple Sign In Flow', () => {
    it('should initiate Apple Sign In flow', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/apple',
      });

      // Should redirect to Apple OAuth URL (in test mode)
      expect(response.statusCode).toBe(302);
      expect(response.headers.location).toContain(
        'appleid.apple.com/auth/authorize'
      );
      expect(response.headers.location).toContain('client_id=mock-client-id');
      expect(response.headers.location).toContain('redirect_uri=');
      expect(response.headers.location).toContain('auth%2Fapple%2Fcallback');
    });

    it('should handle Apple Sign In callback successfully', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            code: 'mock-auth-code',
          },
        });

        // Should redirect after successful authentication
        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/');

        // Should set authentication cookie
        const cookies = response.cookies;
        expect(cookies).toBeDefined();
        const authCookie = cookies.find(
          cookie => cookie.name === authConfig.AUTH_COOKIE_NAME
        );
        expect(authCookie).toBeDefined();
        expect(authCookie!.value).toBeDefined();

        // Verify user was created in database
        const user = await db.query.users.findFirst({
          where: eq(users.email, '<EMAIL>'),
        });
        expect(user).toBeDefined();
        expect(user!.email).toBe('<EMAIL>');
        expect(user!.firstName).toBe('Apple');
        expect(user!.lastName).toBe('User');
        expect(user!.emailVerified).toBe(true); // OAuth users have verified emails
        expect(user!.passwordHash).toBeNull(); // OAuth users don't have passwords
      });
    });

    it('should handle Apple Sign In callback with mobile platform', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback?platform=mobile',
          payload: {
            code: 'mock-auth-code',
          },
        });

        // Should redirect to mobile app with token
        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toContain(
          authConfig.AUTH_MOBILE_REDIRECT_URL
        );
        expect(response.headers.location).toContain('token=');

        // Extract token from redirect URL
        const redirectUrl = response.headers.location as string;
        const tokenMatch = redirectUrl.match(/token=([^&]+)/);
        expect(tokenMatch).toBeDefined();
        const token = tokenMatch![1];
        expect(token).toBeDefined();
      });
    });

    it('should handle existing Apple user on subsequent logins', async () => {
      await withTransaction(async db => {
        // First login - creates user
        const firstResponse = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            code: 'mock-auth-code',
          },
        });
        expect(firstResponse.statusCode).toBe(302);

        // Second login - should find existing user
        const secondResponse = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            code: 'mock-auth-code',
          },
        });
        expect(secondResponse.statusCode).toBe(302);
        expect(secondResponse.headers.location).toBe('/');

        // Should only have one user in database
        const users_list = await db.query.users.findMany({
          where: eq(users.email, '<EMAIL>'),
        });
        expect(users_list).toHaveLength(1);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication error route', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/error',
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body).toEqual({ error: 'Authentication failed' });
    });

    it('should redirect to error page on OAuth callback failure', async () => {
      // Mock a scenario where OAuth service throws an error
      // This would require mocking the handleOAuthLogin function
      // For now, we'll test the error route directly
      const response = await app.inject({
        method: 'GET',
        url: '/auth/error',
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Authentication failed',
      });
    });
  });

  describe('Security and Edge Cases', () => {
    it('should handle missing authorization code in Google callback', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/google/callback',
      });

      // Should still redirect (mock implementation doesn't validate code)
      expect(response.statusCode).toBe(302);
    });

    it('should handle missing authorization code in Apple callback', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/apple/callback',
        payload: {},
      });

      // Should still redirect (mock implementation doesn't validate code)
      expect(response.statusCode).toBe(302);
    });

    it('should handle invalid HTTP method for Google OAuth initiation', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/google',
      });

      // Should return method not allowed or not found
      expect([404, 405].includes(response.statusCode)).toBe(true);
    });

    it('should handle invalid HTTP method for Apple OAuth initiation', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/apple',
      });

      // Should return method not allowed or not found
      expect([404, 405].includes(response.statusCode)).toBe(true);
    });

    it('should handle invalid HTTP method for Google callback', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/google/callback',
      });

      // Should return method not allowed or not found
      expect([404, 405].includes(response.statusCode)).toBe(true);
    });

    it('should handle invalid HTTP method for Apple callback', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/apple/callback',
      });

      // Apple callback supports both GET and POST, so GET should work
      expect(response.statusCode).toBe(302);
    });
  });

  describe('Cookie Configuration', () => {
    it('should set cookie with correct security attributes', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code',
        });

        const cookies = response.cookies;
        const authCookie = cookies.find(
          cookie => cookie.name === authConfig.AUTH_COOKIE_NAME
        );

        expect(authCookie).toBeDefined();
        expect(authCookie!.httpOnly).toBe(authConfig.AUTH_COOKIE_HTTP_ONLY);
        expect(authCookie!.secure).toBe(authConfig.AUTH_COOKIE_SECURE);
        expect(authCookie!.maxAge).toBe(authConfig.AUTH_COOKIE_MAX_AGE);
        expect(authCookie!.path).toBe('/');

        if (authConfig.AUTH_COOKIE_DOMAIN) {
          expect(authCookie!.domain).toBe(authConfig.AUTH_COOKIE_DOMAIN);
        }
      });
    });

    it('should set cookie for Apple Sign In with correct attributes', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            code: 'mock-auth-code',
          },
        });

        const cookies = response.cookies;
        const authCookie = cookies.find(
          cookie => cookie.name === authConfig.AUTH_COOKIE_NAME
        );

        expect(authCookie).toBeDefined();
        expect(authCookie!.httpOnly).toBe(authConfig.AUTH_COOKIE_HTTP_ONLY);
        expect(authCookie!.secure).toBe(authConfig.AUTH_COOKIE_SECURE);
        expect(authCookie!.maxAge).toBe(authConfig.AUTH_COOKIE_MAX_AGE);
        expect(authCookie!.path).toBe('/');
      });
    });
  });

  describe('Token Validation', () => {
    it('should generate valid JWT tokens for Google OAuth', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code&platform=mobile',
        });

        // Extract token from redirect URL
        const redirectUrl = response.headers.location as string;
        const tokenMatch = redirectUrl.match(/token=([^&]+)/);
        const token = tokenMatch![1];

        // Verify token can be decoded
        try {
          const decoded = app.jwt.verify(token);
          expect(decoded).toBeDefined();
          expect(typeof decoded).toBe('object');

          const payload = decoded as {
            userId: string;
            email: string;
            role: string;
          };
          expect(payload.userId).toBeDefined();
          expect(payload.email).toBe('<EMAIL>');
          expect(payload.role).toBe('user');
        } catch (error) {
          throw new Error(`Token verification failed: ${error}`);
        }
      });
    });

    it('should generate valid JWT tokens for Apple Sign In', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback?platform=mobile',
          payload: {
            code: 'mock-auth-code',
          },
        });

        // Extract token from redirect URL
        const redirectUrl = response.headers.location as string;
        const tokenMatch = redirectUrl.match(/token=([^&]+)/);
        const token = tokenMatch![1];

        // Verify token can be decoded
        try {
          const decoded = app.jwt.verify(token);
          expect(decoded).toBeDefined();
          expect(typeof decoded).toBe('object');

          const payload = decoded as {
            userId: string;
            email: string;
            role: string;
          };
          expect(payload.userId).toBeDefined();
          expect(payload.email).toBe('<EMAIL>');
          expect(payload.role).toBe('user');
        } catch (error) {
          throw new Error(`Token verification failed: ${error}`);
        }
      });
    });
  });

  describe('Database Integration', () => {
    it('should create user settings for Google OAuth users', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=mock-auth-code',
        });

        expect(response.statusCode).toBe(302);

        // Verify user was created
        const user = await db.query.users.findFirst({
          where: eq(users.email, '<EMAIL>'),
        });
        expect(user).toBeDefined();

        // Verify user settings were created
        const dbUserSettings = await db.query.userSettings.findFirst({
          where: eq(userSettings.userId, user!.id),
        });
        expect(dbUserSettings).toBeDefined();
        expect(dbUserSettings!.defaultCurrency).toBe('USD');
        expect(dbUserSettings!.theme).toBe('light');
      });
    });

    it('should create user settings for Apple Sign In users', async () => {
      await withTransaction(async db => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            code: 'mock-auth-code',
          },
        });

        expect(response.statusCode).toBe(302);

        // Verify user was created
        const user = await db.query.users.findFirst({
          where: eq(users.email, '<EMAIL>'),
        });
        expect(user).toBeDefined();

        // Verify user settings were created
        const dbUserSettings = await db.query.userSettings.findFirst({
          where: eq(userSettings.userId, user!.id),
        });
        expect(dbUserSettings).toBeDefined();
        expect(dbUserSettings!.defaultCurrency).toBe('USD');
        expect(dbUserSettings!.theme).toBe('light');
      });
    });
  });
});
