<Climb>
  <header>
    <id>8c40</id>
    <type>task</type>
    <description>Set up the monorepo structure with Turborepo and pnpm workspaces</description>
  <newDependencies>
    - pnpm (package manager)
    - turborepo (monorepo build system)
    - TypeScript (for type definitions)
    - ESLint (for code linting)
    - Prettier (for code formatting)
  </newDependencies>
  <prerequisitChanges>
    None. This is a foundational task that will establish the project structure.
  </prerequisitChanges>
  <relevantFiles>
    - package.json (root)
    - pnpm-workspace.yaml
    - turbo.json
    - tsconfig.json (base configuration)
    - .eslintrc.js
    - .prettierrc
    - apps/api/package.json
    - apps/mobile/pubspec.yaml
    - packages/*/package.json
  </relevantFiles>
  
  ## Feature Overview
  
  ### Purpose Statement
  Set up a monorepo structure using Turborepo and pnpm workspaces to manage the BudApp project, which consists of a Node.js backend API and a Flutter mobile application, along with shared packages.
  
  ### Problem Being Solved
  Managing multiple related projects (backend API, mobile app, shared libraries) in separate repositories creates challenges with versioning, dependency management, and code sharing. A monorepo approach simplifies these issues by keeping all code in a single repository with a unified build system.
  
  ### Success Metrics
  - All project components (API, mobile app, shared packages) are organized in a clear directory structure
  - Dependencies can be installed with a single command
  - Build scripts can be run for individual packages or the entire project
  - Shared code can be easily referenced across packages
  - Development workflow is streamlined with efficient caching and parallel execution
  
  ## Requirements
  
  ### Functional Requirements
  1. Create a monorepo structure with `apps` and `packages` directories
  2. Set up pnpm workspaces for dependency management
  3. Configure Turborepo for build orchestration and caching
  4. Initialize the API project with Node.js, TypeScript, and ESM
  5. Initialize the mobile project with Flutter
  6. Create shared packages for common code
  7. Set up linting and formatting tools
  8. Configure TypeScript for the Node.js projects
  
  ### Technical Requirements
  1. Use pnpm as the package manager
  2. Use Turborepo for build orchestration
  3. Use ESM modules for all JavaScript/TypeScript code
  4. Ensure proper TypeScript configuration for all packages
  5. Set up appropriate .gitignore files
  
  ### Constraints
  - Must support both Node.js (backend) and Flutter (mobile) projects
  - Must enable efficient development workflows with caching and parallel execution
  - Must facilitate code sharing between packages
  
  ## Implementation Details
  
  ### Directory Structure
  ```
  .
  ├── apps
  │   ├── api/                 # Fastify 5 + GraphQL API (Apollo Server), Drizzle ORM, Vitest
  │   └── mobile/              # Flutter (Dart), GraphQL client (graphql_flutter)
  ├── packages
  │   ├── graphql-schema/      # Shared GraphQL schema and type definitions
  │   ├── db/                  # Drizzle schema & migrations
  │   ├── config/              # eslint, tsconfig, jest/vitest presets
  │   └── design/              # Theme tokens for UI (with codegen for Flutter)
  ├── scripts/                 # build, deploy & utility scripts
  └── docs/                    # documentation
  ```
  
  ### Configuration Files
  
  #### Root package.json
  - Define workspace scripts for common operations
  - Specify pnpm as the package manager
  - Include development dependencies shared across packages
  
  #### pnpm-workspace.yaml
  - Define workspace packages pattern
  - Include apps and packages directories
  
  #### turbo.json
  - Define pipeline for build, test, lint, and other tasks
  - Configure caching strategy
  - Set up task dependencies
  
  #### Base tsconfig.json
  - Configure TypeScript settings to be extended by packages
  - Set up path aliases for easier imports
  - Configure ESM module support
  
  ### API Project Setup
  - Initialize with Node.js and TypeScript
  - Configure Fastify and GraphQL dependencies
  - Set up ESM module format
  - Configure development scripts
  
  ### Mobile Project Setup
  - Initialize Flutter project
  - Configure pubspec.yaml
  - Set up development scripts
  
  ### Shared Packages Setup
  - Create package.json for each shared package
  - Configure TypeScript for JavaScript packages
  - Set up build scripts
  
  ## Testing Approach
  
  ### Verification Steps
  1. Verify that pnpm install works correctly at the root level
  2. Verify that Turborepo can run tasks across all packages
  3. Verify that shared packages can be imported in the API project
  4. Verify that the API project can be started in development mode
  5. Verify that the mobile project can be built
  
  ## Future Considerations
  
  ### Scalability
  - The monorepo structure should support adding more packages as the project grows
  - Consider setting up remote caching for Turborepo to improve CI/CD performance
  - Plan for handling growing dependencies and potential conflicts
  
  ### Maintenance
  - Regularly update dependencies across all packages
  - Monitor build performance and optimize as needed
  - Consider tools for visualizing dependencies between packages
</Climb>
