{"Climb": "k7Zt", "moves": [{"status": "complete", "description": "Define database enums and common types", "details": "Create all necessary enum types (account types, transaction types, etc.) and common utility types that will be used across the schema."}, {"status": "complete", "description": "Enhance the users table", "details": "Extend the existing users table with additional fields needed for user preferences and settings."}, {"status": "complete", "description": "Implement accounts table", "details": "Create the accounts table with all necessary fields to support different account types and balances."}, {"status": "complete", "description": "Implement categories and subcategories tables", "details": "Create the categories table with support for hierarchical subcategories, icons, and colors."}, {"status": "complete", "description": "Implement transactions and transaction entries tables", "details": "Create the transactions and transaction entries tables with support for double-entry accounting principles.", "rest": true}, {"status": "complete", "description": "Implement budgets table", "details": "Create the budgets table with support for different budget periods and category-specific budgets."}, {"status": "complete", "description": "Implement goals table", "details": "Create the goals table with support for tracking progress towards financial goals."}, {"status": "complete", "description": "Implement user settings table", "details": "Create the user settings table for storing user preferences and application settings."}, {"status": "complete", "description": "Define relationships between tables", "details": "Implement foreign key references and relationships between all tables.", "rest": true}, {"status": "complete", "description": "Update index.ts to export all schema components", "details": "Ensure all schema components are properly exported from the package entry point."}, {"status": "complete", "description": "Generate and test migrations", "details": "Generate migrations using Drizzle Kit and verify they can be applied successfully.", "rest": true}]}