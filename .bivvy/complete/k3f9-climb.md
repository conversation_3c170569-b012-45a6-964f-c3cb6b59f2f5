<Climb>
  <header>
    <id>k3f9</id>
    <type>feature</type>
    <description>Full vertical slice of authentication with JWT and OAuth 2.0</description>
    <newDependencies>
      - API: jsonwebtoken, @fastify/jwt, @fastify/cookie, bcrypt, zod
      - API: passport, passport-google-oauth20, passport-apple
      - Mobile: flutter_appauth, flutter_secure_storage
    </newDependencies>
    <prerequisitChanges>
      - Update environment variables in .env files
      - Set up OAuth credentials in Google Cloud Console and Apple Developer Portal
      - Ensure Supabase database is properly configured with the users table
    </prerequisitChanges>
    <relevantFiles>
      - packages/db/src/schema.ts (users table)
      - apps/api/src/server.ts (API setup)
      - apps/api/src/graphql/schema.ts (GraphQL schema)
      - apps/api/src/graphql/resolvers.ts (GraphQL resolvers)
      - apps/mobile/lib/screens/login_screen.dart (Login UI)
      - apps/mobile/lib/config/app_router.dart (Navigation)
    </relevantFiles>
  </header>

# Authentication System for BudApp

## Feature Overview

**Feature Name**: Authentication System with JWT and OAuth 2.0
**Purpose**: Implement a secure, reliable authentication system that allows users to sign up, log in, and manage their accounts using both email/password and social login options.
**Problem Being Solved**: Users need a secure way to authenticate and access their personal financial data across devices while maintaining privacy and security.
**Success Metrics**:
- 99.9% authentication success rate
- < 3 second login time
- 100% of user data protected by proper authentication
- Support for both email/password and social login options (Google, Apple)

## Requirements

### Functional Requirements

#### API (Backend)
1. User registration with email and password
2. User login with email and password
3. Social login with Google OAuth 2.0
4. Social login with Apple Sign In
5. JWT token generation and validation
6. Password hashing and verification
7. Email verification (placeholder for future implementation)
8. Password reset (placeholder for future implementation)
9. User profile retrieval
10. Token refresh mechanism
11. Logout functionality

#### Mobile App
1. Login screen with email/password fields
2. Registration screen for new users
3. Social login buttons for Google and Apple
4. Secure token storage
5. Automatic token refresh
6. Authentication state management
7. Protected routes based on authentication state
8. Profile screen showing user information
9. Logout functionality

### Technical Requirements

#### API (Backend)
1. Implement JWT-based authentication with appropriate expiration
2. Use bcrypt for password hashing
3. Implement OAuth 2.0 flows for Google and Apple
4. Create GraphQL mutations and queries for authentication
5. Implement proper error handling for authentication failures
6. Use environment variables for sensitive configuration
7. Implement Row-Level Security in Supabase

#### Mobile App
1. Implement secure storage for authentication tokens
2. Create authentication state provider using Riverpod
3. Implement OAuth redirects and handling
4. Add authentication headers to GraphQL requests
5. Handle token expiration and refresh
6. Implement proper error handling and user feedback

### User Requirements
1. Simple, intuitive login and registration process
2. Quick social login options
3. Persistent login across app restarts
4. Clear error messages for authentication issues
5. Ability to log out from any device

## Design and Implementation

### User Flow

#### Email/Password Registration
1. User navigates to registration screen
2. User enters email, password, and basic profile information
3. System validates input and checks for existing email
4. System creates user account and sends verification email (placeholder)
5. System generates JWT token and logs user in
6. User is redirected to home screen

#### Email/Password Login
1. User navigates to login screen
2. User enters email and password
3. System validates credentials
4. System generates JWT token
5. User is redirected to home screen

#### Social Login (Google/Apple)
1. User clicks on social login button
2. System initiates OAuth flow with selected provider
3. User authenticates with provider
4. Provider redirects back to app with authorization code
5. System exchanges code for user information
6. System creates or updates user account
7. System generates JWT token
8. User is redirected to home screen

### Architecture Overview

#### API Layer
- GraphQL API with authentication-specific mutations and queries
- JWT middleware for token validation
- OAuth handlers for social login providers
- User service for account management

#### Mobile Layer
- Authentication state provider
- Secure token storage
- OAuth redirect handling
- Protected route navigation

### API Specifications

#### GraphQL Schema
```graphql
type User {
  id: ID!
  email: String!
  firstName: String
  lastName: String
  emailVerified: Boolean!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type AuthPayload {
  token: String!
  user: User!
}

input RegisterInput {
  email: String!
  password: String!
  firstName: String
  lastName: String
}

input LoginInput {
  email: String!
  password: String!
}

type Mutation {
  register(input: RegisterInput!): AuthPayload!
  login(input: LoginInput!): AuthPayload!
  refreshToken: AuthPayload!
  logout: Boolean!
}

type Query {
  me: User
}
```

#### REST Endpoints (for OAuth)
- `GET /auth/google`: Initiate Google OAuth flow
- `GET /auth/google/callback`: Handle Google OAuth callback
- `GET /auth/apple`: Initiate Apple Sign In flow
- `POST /auth/apple/callback`: Handle Apple Sign In callback

### Data Models
- User model (already defined in database schema)
- JWT payload structure
- OAuth user profile mapping

## Development Details

### Implementation Approach
1. Set up authentication packages and dependencies
2. Implement JWT generation and validation
3. Create user registration and login resolvers
4. Implement password hashing and verification
5. Set up OAuth providers
6. Create mobile authentication state management
7. Implement secure token storage
8. Create login and registration UI
9. Implement social login buttons and flows
10. Add authentication headers to GraphQL client
11. Test all authentication flows

### Security Considerations
- Store JWT secret in environment variables
- Use HTTPS for all API communication
- Implement proper password hashing with bcrypt
- Use secure storage for tokens on mobile
- Implement token expiration and refresh
- Apply Row-Level Security in Supabase
- Validate all user inputs

## Testing Approach

### Test Cases
1. User registration with valid data
2. User registration with existing email
3. User login with valid credentials
4. User login with invalid credentials
5. Google OAuth login flow
6. Apple Sign In flow
7. Token refresh
8. Protected route access with valid token
9. Protected route access with invalid token
10. Logout functionality

### Acceptance Criteria
1. Users can register with email and password
2. Users can log in with email and password
3. Users can log in with Google account
4. Users can log in with Apple ID
5. Authentication state persists across app restarts
6. Protected routes are only accessible when authenticated
7. Users can view their profile information
8. Users can log out
9. Appropriate error messages are displayed for authentication failures
