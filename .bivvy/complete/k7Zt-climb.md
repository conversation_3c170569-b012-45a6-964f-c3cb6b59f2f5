<Climb>
  <header>
    <id>k7Zt</id>
    <type>feature</type>
    <description>Implement the database schema using Drizzle ORM</description>
    <newDependencies>No new dependencies required. The project already has drizzle-orm, pg, postgres, and drizzle-kit installed.</newDependencies>
    <prerequisitChanges>None. The basic structure for the database package is already set up with placeholder files.</prerequisitChanges>
    <relevantFiles>
      - packages/db/src/schema.ts (main schema file to be expanded)
      - packages/db/src/client.ts (database client setup)
      - packages/db/src/migrate.ts (migration script)
      - packages/db/src/index.ts (package entry point)
      - packages/db/drizzle.config.ts (Drizzle configuration)
    </relevantFiles>
  </header>

## Feature Overview

### Purpose Statement
Implement a comprehensive database schema using Drizzle ORM to support the core functionality of BudApp, including user management, accounts, transactions, categories, budgets, and goals.

### Problem Being Solved
The current database schema is a placeholder with only a basic users table. We need to implement the complete schema to support all the core features of BudApp as defined in the project requirements.

### Success Metrics
- All required tables and relationships are properly defined using Drizzle ORM
- Database migrations can be generated and applied successfully
- The schema supports all the core functionality of BudApp
- The schema follows best practices for financial data management, including double-entry accounting principles

## Requirements

### Functional Requirements
1. Define all necessary tables to support the core functionality of BudApp:
   - Users (already partially implemented)
   - Accounts/Wallets
   - Categories and Subcategories
   - Transactions (supporting double-entry accounting)
   - Budgets
   - Financial Goals
   - User Settings and Preferences

2. Implement proper relationships between tables:
   - Users to Accounts (one-to-many)
   - Users to Categories (one-to-many)
   - Users to Budgets (one-to-many)
   - Users to Goals (one-to-many)
   - Categories to Subcategories (one-to-many)
   - Accounts to Transactions (one-to-many)
   - Categories to Transactions (one-to-many)

3. Support for double-entry accounting principles in the transaction schema:
   - Each transaction must affect at least two accounts
   - Credits must equal debits for each transaction

4. Support for recurring transactions

### Technical Requirements
1. Use Drizzle ORM's schema definition syntax for all tables
2. Implement proper indexes for performance optimization
3. Use appropriate data types for all fields
4. Implement proper constraints (NOT NULL, UNIQUE, etc.)
5. Support for Row-Level Security (RLS) policies in PostgreSQL
6. Generate and apply migrations using Drizzle Kit

### Constraints
1. The schema must be compatible with PostgreSQL (via Supabase)
2. The schema must support the MVP features as defined in the project brief
3. The schema should be extensible for future features

## Design and Implementation

### Data Models

#### Users Table (Already Partially Implemented)
- Extend with additional fields as needed for user preferences and settings

#### Accounts Table
- Fields: id, userId, name, type, currency, initialBalance, currentBalance, isArchived, etc.
- Types: checking, savings, credit card, cash, investment, etc.

#### Categories Table
- Fields: id, userId, name, icon, color, type (income/expense), isDefault, parentId (for subcategories), etc.

#### Transactions Table
- Fields: id, userId, description, amount, date, notes, isRecurring, recurringPattern, etc.
- Support for double-entry accounting with transaction entries

#### Transaction Entries Table
- Fields: id, transactionId, accountId, amount, type (credit/debit), etc.

#### Budgets Table
- Fields: id, userId, name, amount, period, categoryId, startDate, endDate, etc.

#### Goals Table
- Fields: id, userId, name, targetAmount, currentAmount, deadline, notes, etc.

#### User Settings Table
- Fields: id, userId, defaultCurrency, theme, notificationPreferences, etc.

### Implementation Considerations
1. The schema should support multi-user environments with proper data isolation
2. Consider using enums for fixed value sets (account types, transaction types, etc.)
3. Implement proper timestamps for created_at and updated_at fields
4. Consider soft delete patterns for data that should not be permanently deleted
5. Implement proper foreign key constraints for referential integrity
6. Consider performance implications of the schema design, especially for transaction queries

## Testing Approach

### Test Cases
1. Generate migrations from the schema and verify they can be applied
2. Test creating and querying data for each table
3. Test relationships between tables
4. Test double-entry accounting principles in transaction operations
5. Test data isolation between different users

### Acceptance Criteria
1. All required tables are properly defined with appropriate fields and relationships
2. Migrations can be generated and applied successfully
3. The schema supports all the core functionality of BudApp
4. The schema follows best practices for financial data management

## Future Considerations
1. Support for multi-currency transactions
2. Support for shared accounts and budgets
3. Support for attachments in transactions
4. Support for investment tracking
5. Support for debt management
</Climb>
