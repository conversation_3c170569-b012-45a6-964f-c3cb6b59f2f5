<Climb>
  <header>
    <id>xK4p</id>
    <type>task</type>
    <description>Change API stack from Fastify 4.4.0 with Apollo Server to Fastify 5 with Mercurius</description>
    <newDependencies>
      - fastify@5 (latest)
      - mercurius (latest)
      - mercurius-tracing (latest)
      - mercurius-opentelemetry (latest)
      - @opentelemetry/sdk-node (latest)
      - mercurius-cache (latest)
    </newDependencies>
    <prerequisitChanges>
      - Remove @apollo/server
      - Remove @as-integrations/fastify
      - Update fastify from 4.4.0 to 5.x
      - Update fastify plugins to versions compatible with Fastify 5
    </prerequisitChanges>
    <relevantFiles>
      - apps/api/package.json
      - apps/api/src/server.ts
      - apps/api/src/utils/error-handler.ts
      - apps/api/src/graphql/schema.ts
      - apps/api/src/graphql/resolvers.ts
      - apps/api/src/graphql/resolvers/auth.ts
    </relevantFiles>
  </header>

## Feature Overview

**Task Name**: Change API stack from Fastify 4.4.0 with Apollo Server to Fastify 5 with Mercurius
**Purpose**: Upgrade the GraphQL implementation from Apollo Server to Mercurius for better integration with Fastify 5 and improved performance

### Problem Being Solved
The current implementation uses Apollo Server with Fastify 4.4.0, which requires additional integration packages and has compatibility issues. Switching to Mercurius will provide:
1. Native integration with Fastify
2. Better performance
3. Simpler configuration
4. Enhanced features like tracing, caching, and OpenTelemetry support
5. Improved developer experience

### Success Metrics
1. Successful migration with all existing GraphQL functionality working
2. Improved error handling with Mercurius-specific features
3. Reduced bundle size by removing Apollo Server dependencies
4. Improved performance for GraphQL operations
5. Better integration with Fastify ecosystem

## Requirements

### Functional Requirements
1. All existing GraphQL queries and mutations must continue to work
2. Authentication flow must be preserved
3. Error handling must be adapted to Mercurius format
4. GraphQL schema definition approach must be maintained (code-first)
5. GraphQL Playground/IDE must be available for development

### Technical Requirements
1. Update to Fastify 5.x
2. Replace Apollo Server with Mercurius
3. Update all Fastify plugins to versions compatible with Fastify 5
4. Implement proper error handling with Mercurius
5. Add OpenTelemetry support with mercurius-opentelemetry
6. Add caching capabilities with mercurius-cache
7. Add tracing with mercurius-tracing

### Constraints
1. Maintain backward compatibility with existing GraphQL clients
2. Ensure all tests pass after migration
3. Maintain ESM module format
4. Follow TypeScript best practices with proper typing

## Design and Implementation

### Architecture Changes
1. Replace Apollo Server with Mercurius as the GraphQL server
2. Update the server initialization code to use Mercurius plugin
3. Adapt the context creation for authentication
4. Update error handling to use Mercurius error formatting
5. Add OpenTelemetry, tracing, and caching capabilities

### Implementation Details

#### Package Changes
- Remove:
  - @apollo/server
  - @as-integrations/fastify
- Add:
  - fastify@5.x
  - mercurius
  - mercurius-tracing
  - mercurius-opentelemetry
  - @opentelemetry/sdk-node
  - mercurius-cache
- Update:
  - @fastify/jwt to a version compatible with Fastify 5
  - @fastify/cookie to a version compatible with Fastify 5
  - @fastify/cors to a version compatible with Fastify 5

#### Server Configuration Changes
1. Update server.ts to use Mercurius plugin instead of Apollo Server
2. Adapt context creation for authentication
3. Update error handling to use Mercurius error formatting
4. Add OpenTelemetry, tracing, and caching setup

#### Error Handling Changes
1. Update error-handler.ts to work with Mercurius error format
2. Implement Mercurius-specific error hooks
3. Ensure all custom error types are properly formatted

## Testing Approach

### Test Cases
1. Test all existing GraphQL queries and mutations
2. Test authentication flow
3. Test error handling with various error scenarios
4. Test performance with basic load testing

### Acceptance Criteria
1. All existing GraphQL operations work as before
2. Authentication flow works correctly
3. Error messages are properly formatted
4. GraphQL Playground/IDE is accessible
5. No regressions in functionality

## Future Considerations
1. Explore additional Mercurius plugins for enhanced functionality
2. Implement subscription support if needed in the future
3. Optimize caching strategy for frequently accessed data
4. Fine-tune OpenTelemetry configuration for production monitoring
</Climb>
