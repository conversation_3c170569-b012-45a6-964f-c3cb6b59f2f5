{"Climb": "k3f9", "moves": [{"status": "complete", "description": "Install required dependencies for API authentication", "details": "Install jsonwebtoken, @fastify/jwt, @fastify/cookie, bcrypt, zod, passport, passport-google-oauth20, passport-apple"}, {"status": "complete", "description": "Create authentication service and utilities in API", "details": "Create JWT utilities, password hashing, and token management", "rest": true}, {"status": "complete", "description": "Implement GraphQL schema for authentication", "details": "Define User type, AuthPayload, and authentication mutations/queries"}, {"status": "complete", "description": "Implement GraphQL resolvers for authentication", "details": "Create resolvers for register, login, refreshToken, logout, and me query", "rest": true}, {"status": "complete", "description": "Set up OAuth routes and handlers for Google and Apple", "details": "Create REST endpoints for OAuth flows and implement callback handling"}, {"status": "complete", "description": "Implement authentication middleware for API", "details": "Create middleware to validate JWT tokens and attach user to context", "rest": true}, {"status": "complete", "description": "Install required dependencies for mobile authentication", "details": "Install flutter_appauth, flutter_secure_storage in the mobile app"}, {"status": "complete", "description": "Create authentication models and providers in mobile app", "details": "Create User model, AuthState provider, and secure storage utilities"}, {"status": "complete", "description": "Implement login screen functionality in mobile app", "details": "Connect login form to authentication provider and handle errors", "rest": true}, {"status": "complete", "description": "Create registration screen in mobile app", "details": "Implement registration form and connect to authentication provider"}, {"status": "complete", "description": "Implement social login buttons and flows in mobile app", "details": "Add Google and Apple login buttons and implement OAuth flows"}, {"status": "complete", "description": "Update app router to handle authentication state", "details": "Modify app_router.dart to use authentication state for navigation", "rest": true}, {"status": "complete", "description": "Create profile screen in mobile app", "details": "Implement screen to display user information and logout button"}, {"status": "complete", "description": "Configure GraphQL client with authentication headers", "details": "Update GraphQL client to include authentication token in requests"}, {"status": "complete", "description": "Test authentication flows end-to-end", "details": "Test registration, login, social login, and protected routes", "rest": true}, {"status": "complete", "description": "Add error handling and user feedback", "details": "Implement proper error messages and loading indicators"}, {"status": "complete", "description": "Document authentication system", "details": "Update README files and add documentation for authentication flows"}]}