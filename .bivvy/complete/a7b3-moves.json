{"Climb": "a7b3", "moves": [{"status": "complete", "description": "Set up test environment configuration", "details": "Create .env.test files and update configuration to load test-specific environment variables"}, {"status": "complete", "description": "Implement test database management utilities", "details": "Create scripts and utilities for managing the test database, including reset and seed functions", "rest": true}, {"status": "complete", "description": "Configure Vitest global setup and teardown", "details": "Set up global setup and teardown functions for test suites"}, {"status": "complete", "description": "Establish mocking strategy and utilities", "details": "Create a consistent approach to mocking external services and dependencies"}, {"status": "complete", "description": "Create API test client utility", "details": "Implement a GraphQL client utility for testing API endpoints", "rest": true}, {"status": "complete", "description": "Configure coverage reporting", "details": "Set up Vitest for code coverage reporting with minimum thresholds using @vitest/coverage-istanbul and configure proper transformers for ESM + TypeScript"}, {"status": "complete", "description": "Implement unit tests for database code", "details": "Create unit tests for the database schema, types, migrate utility, and seed data functions in apps/api/src/database. Database package has been relocated from packages/db to apps/api/src/database. Previous tests were removed and need to be recreated.", "rest": true}, {"status": "complete", "description": "Implement integration tests for database code", "details": "Create integration tests for all database tables including users, accounts, categories, journal entries, journal lines, budgets, and goals. Tests should be placed in apps/api/__tests__/integration/database/", "rest": true}, {"status": "complete", "description": "Implement unit tests for API config files", "details": "Test environment variable loading and configuration parsing"}, {"status": "complete", "description": "Implement unit tests for API auth libraries", "details": "Test JWT and password utilities", "rest": true}, {"status": "complete", "description": "Implement unit tests for API auth service", "details": "Test user registration, login, and OAuth handling"}, {"status": "complete", "description": "Implement unit tests for API utilities", "details": "Test error handling and other utility functions"}, {"status": "complete", "description": "Implement unit tests for GraphQL resolvers", "details": "Test GraphQL resolvers with mocked services", "rest": true}, {"status": "complete", "description": "Implement integration tests for API auth service", "details": "Test auth service with a real test database"}, {"status": "complete", "description": "Implement integration tests for GraphQL auth flow", "details": "Test the complete authentication flow through GraphQL"}, {"status": "complete", "description": "Implement integration tests for REST auth routes", "details": "Test OAuth routes and callbacks", "rest": true}, {"status": "complete", "description": "Implement E2E tests for authentication lifecycle", "details": "Test the full authentication lifecycle from registration to logout"}, {"status": "complete", "description": "Implement E2E tests for error handling and validation", "details": "Test error responses for invalid inputs and authentication failures"}, {"status": "complete", "description": "Configure package.json scripts for testing", "details": "Set up scripts for running different types of tests"}, {"status": "complete", "description": "Set up CI integration for automated testing", "details": "Configure GitHub Actions to run tests automatically - COMPLETED: Comprehensive CI/CD pipeline with GitHub Actions workflows for API and mobile deployment, Docker containerization, security scanning, and automated testing integration", "rest": true}]}