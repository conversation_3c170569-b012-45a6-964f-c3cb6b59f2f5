# Climb k7m2: Account Management API Implementation

<Climb>
  <header>
    <id>k7m2</id>
    <type>feature</type>
    <description>Implement complete CRUD API endpoints for account management using GraphQL with proper validation, error handling, and integration with existing database schema</description>
  </header>
  <newDependencies>No new dependencies required - will use existing GraphQL, Drizzle ORM, Zod validation, and testing infrastructure</newDependencies>
  <prerequisitChanges>Existing database schema (accounts table) and authentication system must be functional</prerequisitChanges>
  <relevantFiles>
    - apps/api/src/database/schema/accounts.ts (existing accounts table schema)
    - apps/api/src/graphql/schema/ (GraphQL schema definitions)
    - apps/api/src/graphql/resolvers/ (GraphQL resolvers)
    - apps/api/src/services/ (business logic services)
    - apps/api/src/types/ (validation schemas)
    - apps/api/src/config/env.ts (environment configuration)
    - Database seed data in apps/api/src/database/seed/
  </relevantFiles>
</Climb>

## Feature Overview

**Feature Name**: Account Management API  
**Purpose**: Provide comprehensive GraphQL API endpoints for managing user financial accounts (checking, savings, credit cards, etc.) with full CRUD operations, validation, and error handling.

**Problem Being Solved**: Users need to manage multiple financial accounts within BudApp to track their finances across different account types. The API must provide secure, validated operations for account creation, reading, updating, and deletion.

**Success Metrics**:
- All account CRUD operations available via GraphQL API
- API response times P95 < 200ms for account operations
- 100% test coverage for account-related code
- Zero account data integrity issues
- Proper error handling with user-friendly messages

## Requirements

### Functional Requirements
- **Account Creation**: Users can create new financial accounts with name, type, initial balance
- **Account Retrieval**: Users can fetch individual accounts or lists of accounts with filtering
- **Account Updates**: Users can modify account details (name, balance, metadata)
- **Account Deletion**: Users can soft-delete accounts with proper constraints
- **Account Types**: Support for Asset and Liability account types
- **Balance Management**: Proper handling of account balances with decimal precision
- **User Isolation**: Users can only access their own accounts (enforced by authentication)

### Technical Requirements
- GraphQL mutations and queries following existing patterns
- Zod validation for all inputs with comprehensive error messages
- Integration with existing Drizzle ORM database schema
- Proper TypeScript types for all operations
- Standardized error handling following project patterns
- Authentication middleware integration
- Database transactions for data integrity

### Security Requirements
- All operations require valid JWT authentication
- Users can only access accounts they own
- Input validation prevents SQL injection and XSS
- Sensitive account data properly sanitized in responses
- Rate limiting on account operations (if needed)

## Design and Implementation

### User Flow
1. **Create Account**: User provides account name, type, and initial balance → API validates → Creates account in database → Returns created account data
2. **List Accounts**: User requests account list → API fetches user's accounts → Returns filtered/sorted results
3. **View Account**: User requests specific account → API validates ownership → Returns account details
4. **Update Account**: User modifies account data → API validates changes → Updates database → Returns updated account
5. **Delete Account**: User requests deletion → API checks constraints → Soft deletes account → Returns confirmation

### Architecture Overview
- **GraphQL Layer**: Mutations and queries exposing account operations
- **Resolver Layer**: GraphQL resolvers handling request/response logic
- **Service Layer**: Business logic for account operations and validation
- **Repository Layer**: Database access via Drizzle ORM
- **Validation Layer**: Zod schemas for input validation

### API Specifications

**GraphQL Types**:
```graphql
type Account {
  id: ID!
  userId: ID!
  name: String!
  accountType: AccountType!
  balance: Decimal!
  description: String
  isActive: Boolean!
  createdAt: DateTime!
  updatedAt: DateTime!
}

enum AccountType {
  ASSET
  LIABILITY
}

input CreateAccountInput {
  name: String!
  accountType: AccountType!
  initialBalance: Decimal!
  description: String
}

input UpdateAccountInput {
  name: String
  description: String
}

input AccountFilters {
  accountType: AccountType
  isActive: Boolean
  searchTerm: String
}
```

**Mutations**:
- `createAccount(input: CreateAccountInput!): Account!`
- `updateAccount(id: ID!, input: UpdateAccountInput!): Account!`
- `deleteAccount(id: ID!): Boolean!`

**Queries**:
- `account(id: ID!): Account`
- `accounts(filters: AccountFilters, limit: Int, offset: Int): [Account!]!`

### Data Models
Integration with existing `accounts` table:
- `id`: Primary key (UUID)
- `user_id`: Foreign key to users table
- `name`: Account display name
- `account_type`: ENUM ('ASSET', 'LIABILITY')
- `balance`: Decimal with proper precision
- `description`: Optional account description
- `is_active`: Boolean for soft delete
- `created_at`/`updated_at`: Timestamps

## Development Details

### Implementation Approach
1. **Schema First**: Define GraphQL schema types and operations
2. **Validation Layer**: Create Zod schemas for input validation
3. **Service Layer**: Implement business logic with proper error handling
4. **Resolver Implementation**: Connect GraphQL operations to services
5. **Database Integration**: Ensure proper ORM usage and transactions
6. **Testing**: Comprehensive unit and integration tests
7. **Error Handling**: Standardized error responses and logging

### Dependencies
- Existing authentication system for user context
- Drizzle ORM for database operations
- GraphQL (Mercurius) for API layer
- Zod for validation
- Existing error handling utilities

### Security Considerations
- JWT token validation in GraphQL context
- User ownership verification for all operations
- Input sanitization and validation
- Database query parameterization (handled by Drizzle)
- Proper error message sanitization

## Testing Approach

### Test Cases
- **Unit Tests**: Service methods, resolvers, validation schemas
- **Integration Tests**: Database operations, GraphQL operations end-to-end
- **Edge Cases**: Invalid inputs, unauthorized access, constraint violations
- **Performance Tests**: API response times under load

### Acceptance Criteria
- All GraphQL operations work as specified
- Proper error handling for all failure scenarios
- Users cannot access other users' accounts
- Account balances maintain precision and integrity
- Soft delete prevents data loss while maintaining constraints
- 100% test coverage for new code

## Future Considerations

### Scalability Plans
- Pagination for large account lists
- Caching for frequently accessed account data
- Database indexing optimization for account queries

### Enhancement Ideas
- Account balance history tracking
- Account linking/grouping functionality
- Account import from external sources
- Advanced filtering and search capabilities

### Known Limitations
- No real-time balance updates (will be batch processed)
- Single currency support initially
- Basic account types only (no investment accounts, etc.) 