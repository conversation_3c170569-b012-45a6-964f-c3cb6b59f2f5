# Project Brief: <PERSON><PERSON><PERSON> (MVP)

## Overview
BudApp is a personal finance management mobile application (Flutter + Node.js GraphQL API) that helps users track income/expenses, create budgets, monitor savings goals, and gain financial insights. The app provides a simple, secure, and reliable tool for fundamental personal finance management.

## Core Requirements
- User authentication & profile management (JWT + OAuth 2.0)
- Accounts & wallets management with double-entry accounting
- Categories & subcategories for transactions
- Transaction management with financial integrity
- Budgeting functionality with progress tracking
- Financial goal tracking and monitoring
- Basic reporting & insights dashboard
- Notifications & alerts for budgets/balances
- Multi-device sync & basic offline support
- Subscription management (free/premium tiers)

## Goals & Vision
**Primary Goal**: Provide users with an intuitive, secure, and comprehensive tool for tracking finances, setting budgets, and achieving financial well-being.

**Target Audience**: Tech-savvy individuals and young professionals (ages 18-45) seeking a modern alternative to manual tracking or overly complex existing tools.

## Project Scope
### In Scope (MVP)
- Mobile application (iOS/Android) using Flutter
- Backend API (Node.js, Fastify, GraphQL, PostgreSQL)
- User authentication (email/password, Google, Apple)
- Basic financial account management
- Transaction recording and categorization
- Simple monthly budgeting with visual progress
- Basic savings goal tracking
- Simple reporting and insights
- Push notifications for alerts
- Multi-device sync with basic offline support
- Free tier with core functionality + premium framework

### Out of Scope (Post-MVP)
- Web application interface
- Advanced reporting/visualizations
- Debt management, investment tracking
- Multi-currency support, shared budgets
- Automated bank linking, AI insights
- Enhanced security features (2FA)
- Gamification and rewards
