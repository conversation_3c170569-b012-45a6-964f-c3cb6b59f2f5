---
description: 
globs: 
alwaysApply: true
---
## Code Quality & Standards
- ESM Only: Use ECMAScript Modules exclusively. CommonJS modules are prohibited.
- TypeScript Enforcement: All code must be written in TypeScript with strict type checking enabled.
- Linting: ESLint must be run before any commit. No warnings or errors allowed.
- Formatting: Prettier must be used to format all code. Custom rules in .prettierrc.
- Naming Conventions: Use camelCase for variables/functions, PascalCase for classes/types/interfaces, and snake_case for database fields.
- Code Reviews: All PRs require at least one approval. No self-approvals allowed.
- Pure Functions: Prefer pure functions with clear inputs and outputs.
- Error Handling: Use the standardized error format for all API error responses.

## Architecture & Design
- GraphQL Schema Design: Follow code-first approach exclusively. No SDL-first development.
- Monorepo Structure: Strictly adhere to the defined apps/packages structure.
- Package Organization: Shared code must be in packages, app-specific code in apps directories.
- Double-Entry Foundation: All financial transactions must use double-entry accounting principles.
- Schema Evolution: Use GraphQL deprecation directives for field/type removal. Never remove fields directly without deprecation period.
- Database Operations: Drizzle ORM is mandatory for all database operations. No raw SQL queries.
- API Layer: All client-server communication must use GraphQL. REST endpoints only for health checks or third-party webhooks.
- Mobile Architecture: Follow Riverpod for state management, go_router for navigation.

## Testing & Quality Assurance
- Test Coverage: Minimum 80% code coverage for core business logic.
- TDD Approach: Critical components must have tests written before or alongside implementation.
- Test Types: Each component requires appropriate unit, integration, and E2E tests.
- Test Data: Use tiered seed data (base, dev, test) for consistent test scenarios.
- Testing Libraries: Backend: Vitest; Mobile: flutter_test and integration_test.
- CI Validation: All tests must pass in CI before merge. No exceptions.
- Automated Testing: UI tests must cover all critical user flows.
- Performance Testing: API endpoints must be tested with expected load.

## Security
- Authentication: Use JWTs for sessions, bcrypt/argon2 for password hashing.
- Row-Level Security: Mandatory RLS policies for all user data tables in PostgreSQL.
- Sensitive Data: No sensitive data in logs or client-side storage outside secure storage.
- Input Validation: Use Zod for all API input validation. No exceptions.
- Dependency Scanning: Run security scans on dependencies regularly.
- Data Encryption: Use flutter_secure_storage for sensitive client data.
- Rate Limiting: Implement and enforce rate limits on all API endpoints.
- GDPR Compliance: Follow defined data anonymization and deletion policies.

## Performance
- Response Times: API P95 response time must be under 200ms for core operations.
- Mobile Performance: App must maintain 60fps for scrolls/animations.
- Cold Start: App cold start under 2 seconds on mid-range devices.
- Query Optimization: All database queries must be optimized with appropriate indexes.
- Bundle Size: Monitor and optimize app bundle size with each release.
- Background Processing: Use BullMQ for all long-running tasks.
- Caching Strategy: Implement appropriate caching at client and server levels.
- Lazy Loading: Use lazy loading for non-critical UI elements and data.

## Documentation
- Code Documentation: All public APIs, functions, and types must have documentation comments.
- Architecture Docs: Update architecture diagrams when making significant changes.
- Knowledge Sharing: Document learnings, decisions, and patterns in the appropriate repo location.
- API Documentation: Keep GraphQL schema documentation up-to-date.
- Readmes: Each package must have a README with usage examples.
- Decision Records: Document architectural decisions with context and alternatives considered.
- User Documentation: User-facing features require documentation updates.
- Release Notes: Maintain detailed release notes for each version.

## Deployment & CI/CD
- Environment Variables: No hardcoded credentials or environment-specific values.
- Deployment Approval: Production deployments require explicit approval.
- Database Migrations: Test all migrations in staging before production.
- Rollback Plan: Document rollback procedures for all significant changes.
- Monitoring: Configure appropriate alerting for all new features.
- Zero-Downtime: All deployments must be zero-downtime capable.
- Versioning: Follow semantic versioning for all released packages.
- Canary Releases: Use staged rollouts for high-risk changes.