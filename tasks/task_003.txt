# Task ID: 3
# Title: Configure Mobile App Project
# Status: done
# Dependencies: 1
# Priority: high
# Description: Set up the Flutter mobile app project with Dar<PERSON> and required dependencies.
# Details:
Initialize the Flutter project in `apps/mobile/` with Material 3 and Cupertino components. Configure `graphql_flutter` for data fetching and Riverpod for state management.

# Test Strategy:
Verify the setup by running the app on an emulator and ensuring it launches without errors.
