# Task ID: 6
# Title: Set Up Database Schema
# Status: done
# Dependencies: 2
# Priority: high
# Description: Define the PostgreSQL database schema using Drizzle ORM.
# Details:
Create tables for users, accounts, transactions, categories, budgets, and goals in `packages/db/`. Enforce RLS policies and foreign key constraints.

# Test Strategy:
Run migrations and verify table creation and RLS policies in Supabase.
