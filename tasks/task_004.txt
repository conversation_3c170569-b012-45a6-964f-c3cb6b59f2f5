# Task ID: 4
# Title: Design GraphQL Schema
# Status: done
# Dependencies: 2
# Priority: high
# Description: Define the GraphQL schema for the API using a code-first approach.
# Details:
Use Nexus/TypeGraphQL to define the schema in `packages/graphql-schema/`. Include types for users, accounts, transactions, categories, budgets, and goals.

# Test Strategy:
Generate the schema and validate it using GraphQL Playground.
