# Task ID: 17
# Title: Set Up CI/CD Pipeline
# Status: done
# Dependencies: 1, 2, 3
# Priority: high
# Description: Configure GitHub Actions for automated testing and deployment.
# Details:
Define workflows for linting, testing, and deploying the API and mobile app. Include database migrations in the pipeline.

# Test Strategy:
Verify workflows by triggering a test run and checking for successful execution.
