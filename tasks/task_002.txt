# Task ID: 2
# Title: Configure Backend API Project
# Status: done
# Dependencies: 1
# Priority: high
# Description: Set up the Fastify 5 + GraphQL API project with TypeScript and required dependencies.
# Details:
Initialize the API project in `apps/api/` with Fastify, Apollo Server, Drizzle ORM, and Zod. Configure TypeScript for ESM and set up basic linting with ESLint.

# Test Strategy:
Validate the setup by running a basic GraphQL query and ensuring the server starts without errors.
