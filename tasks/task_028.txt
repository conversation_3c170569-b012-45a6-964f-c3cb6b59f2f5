# Task ID: 28
# Title: Make GraphQL API URL Configurable in Mobile App
# Status: done
# Dependencies: 3
# Priority: medium
# Description: Replace the hardcoded GraphQL API URL in the mobile app with a configurable solution using --dart-define build-time variables.
# Details:
1. Implement environment configuration using --dart-define approach (chosen for better security and no additional dependencies).
2. Create build scripts for development, staging, and production environments with distinct API URLs.
3. Modify `graphql_provider.dart` to read the API URL from the build-time variables instead of the hardcoded value.
4. Update build scripts and documentation to explain how to configure API URLs for each environment.
5. Test the app's ability to connect to different API endpoints based on the configuration.
6. Ensure the solution works seamlessly for both debug and release builds.

# Test Strategy:
1. Verify the app reads the API URL from the build-time variables.
2. Test the app with different environment configurations (development, staging, production) to ensure it connects to the correct API endpoint.
3. Confirm the app functions correctly in both debug and release modes.
4. Validate the documentation and build scripts are clear and accurate for team use.

# Subtasks:
## 28.1. undefined [completed]
### Dependencies: None
### Description: Research and decide configuration approach
### Details:
Decided to use --dart-define approach for environment configuration

