# BudApp MVP - Product Requirements Document

## Project Overview
BudApp is a personal finance management mobile application that helps users track income and expenses, create budgets, monitor savings goals, and gain insights into spending habits. The MVP focuses on core functionality with a Flutter mobile app and Node.js GraphQL API backend.

## Current Status
✅ **COMPLETED FOUNDATION**:
- Authentication system (JWT + OAuth 2.0) with comprehensive testing
- Database schema with double-entry accounting foundation
- Production-ready API with Fastify 5 + GraphQL (Mercurius)
- Mobile app authentication with secure token storage
- Testing framework with 543/543 tests passing (100% success rate)
- CI/CD pipeline with GitHub Actions
- Build & code quality excellence (0 linting errors, zero `any` types)

## Core Features to Implement

### 1. Account Management System
**Description**: Complete account creation, management, and balance tracking system
**Priority**: High
**Dependencies**: Authentication system (completed)

**Requirements**:
- Create multiple financial accounts (checking, savings, credit card, cash)
- Set account types (Asset, Liability) and initial balances
- Edit account details (name, type, balance)
- Delete accounts with proper data handling
- Account balance visualization and tracking
- Account filtering and sorting capabilities

**Technical Details**:
- API endpoints for CRUD operations on accounts
- GraphQL mutations and queries for account management
- Mobile screens for account creation, editing, and listing
- Integration with existing database schema (accounts table)
- Proper validation and error handling

### 2. Transaction Management System
**Description**: Core transaction recording with double-entry foundation
**Priority**: High
**Dependencies**: Account Management System

**Requirements**:
- Record income transactions (amount, date, category, receiving account)
- Record expense transactions (amount, date, category, paying account)
- Record transfer transactions between accounts
- Add optional notes and tags to transactions
- Edit and delete existing transactions
- Transaction history and details views
- Search and filter transactions

**Technical Details**:
- Double-entry accounting implementation (journal entries and journal lines)
- Transaction categorization system
- Mobile transaction entry screens with intuitive UX
- Transaction validation and balance updates
- Immutable transaction records with correction mechanisms

### 3. Category Management System
**Description**: Category and subcategory organization for transactions
**Priority**: Medium
**Dependencies**: None (can work independently)

**Requirements**:
- Predefined common income and expense categories
- Create custom categories and subcategories
- Edit and delete custom categories
- Assign custom icons and colors to categories
- Category hierarchy management

**Technical Details**:
- Category CRUD API endpoints
- Mobile category management screens
- Icon and color picker components
- Category validation and constraint handling

### 4. Budgeting System
**Description**: Monthly budget creation, tracking, and monitoring
**Priority**: Medium
**Dependencies**: Transaction Management System, Category Management System

**Requirements**:
- Set overall monthly budgets for total spending
- Set category-specific monthly budgets
- Visual budget progress indicators (progress bars, charts)
- Budget vs actual spending comparisons
- Budget alerts and notifications

**Technical Details**:
- Budget creation and management API
- Budget progress calculation logic
- Mobile budget screens with visual indicators
- Integration with transaction data for progress tracking

### 5. Goal Tracking System
**Description**: Savings goal creation and progress monitoring
**Priority**: Medium
**Dependencies**: Account Management System

**Requirements**:
- Create savings goals with target amounts and deadlines
- Track contributions towards goals
- Visual progress indicators for goals
- Goal achievement notifications
- Edit and delete goals

**Technical Details**:
- Goals CRUD API endpoints
- Goal progress calculation
- Mobile goal management screens
- Visual progress components

### 6. Basic Reporting & Dashboard
**Description**: Financial overview and simple reporting
**Priority**: Medium
**Dependencies**: Transaction Management System, Account Management System

**Requirements**:
- Dashboard with current account balances
- Recent transactions overview
- Budget summaries and progress
- Simple expense/income reports by category
- Time period filtering (monthly, weekly)

**Technical Details**:
- Dashboard API with aggregated data
- Chart and visualization components
- Mobile dashboard screen
- Report generation logic

### 7. Notifications & Alerts
**Description**: Push notifications for budget and balance alerts
**Priority**: Low
**Dependencies**: Budgeting System, Account Management System

**Requirements**:
- Budget threshold alerts (80% spent warning)
- Low balance alerts (configurable thresholds)
- Goal achievement notifications
- Reliable push notification delivery

**Technical Details**:
- Firebase Cloud Messaging integration
- Notification scheduling and delivery system
- User notification preferences
- Background notification processing

## Technical Constraints
- **Architecture**: Maintain double-entry accounting principles
- **Performance**: API P95 response time < 200ms
- **Mobile**: 60fps animations, cold start < 2 seconds
- **Security**: Row-Level Security (RLS) for all user data
- **Testing**: Maintain 100% test success rate
- **Code Quality**: Zero linting errors, no `any` types

## Success Criteria
- All core features implemented and tested
- Mobile app responsive and performant
- API endpoints secure and optimized
- User authentication and data protection working
- Comprehensive test coverage maintained
- Production deployment ready

## Out of Scope (Post-MVP)
- Web application interface
- Advanced reporting and visualizations
- Multi-currency support
- Bank account linking
- Investment tracking
- Debt management tools
- AI-powered insights
