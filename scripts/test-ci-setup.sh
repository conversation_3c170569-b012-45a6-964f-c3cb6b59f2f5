#!/bin/bash

# BudApp CI/CD Test Script
# This script tests the CI/CD setup locally before pushing to GitHub

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run command with error handling
run_command() {
    local cmd="$1"
    local description="$2"
    
    print_status "Running: $description"
    if eval "$cmd"; then
        print_success "$description completed"
        return 0
    else
        print_error "$description failed"
        return 1
    fi
}

# Function to test API build and tests
test_api() {
    print_status "Testing API build and tests..."
    
    # Install dependencies
    run_command "pnpm install" "Installing dependencies"
    
    # Run linting
    run_command "pnpm lint" "Running linting"
    
    # Run formatting check
    run_command "pnpm check" "Running format check"
    
    # Build API
    run_command "pnpm api:build" "Building API"
    
    # Run unit tests
    run_command "pnpm api:test:unit" "Running API unit tests"
    
    # Run integration tests (if database is available)
    if [[ -n "$DATABASE_URL" ]]; then
        run_command "pnpm db:migrate" "Running database migrations"
        run_command "pnpm api:test:integration" "Running API integration tests"
    else
        print_warning "DATABASE_URL not set, skipping integration tests"
    fi
}

# Function to test mobile build
test_mobile() {
    print_status "Testing mobile build..."
    
    # Check if Flutter is installed
    if ! command -v flutter >/dev/null 2>&1; then
        print_warning "Flutter not installed, skipping mobile tests"
        return 0
    fi
    
    cd apps/mobile
    
    # Get dependencies
    run_command "flutter pub get" "Getting Flutter dependencies"
    
    # Run analysis
    run_command "flutter analyze" "Running Flutter analysis"
    
    # Run tests
    run_command "flutter test" "Running Flutter tests"
    
    # Test build (Android)
    if command -v android >/dev/null 2>&1 || [[ -d "$ANDROID_HOME" ]]; then
        run_command "flutter build apk --debug --flavor staging" "Building Android APK (staging)"
    else
        print_warning "Android SDK not found, skipping Android build"
    fi
    
    cd ../..
}

# Function to test Docker build
test_docker() {
    print_status "Testing Docker build..."
    
    if ! command -v docker >/dev/null 2>&1; then
        print_warning "Docker not installed, skipping Docker tests"
        return 0
    fi
    
    # Build Docker image
    run_command "docker build -t budapp-api-test -f apps/api/Dockerfile ." "Building Docker image"
    
    # Test Docker image
    print_status "Testing Docker image..."
    local container_id
    container_id=$(docker run -d -p 3001:3000 -e NODE_ENV=production budapp-api-test)
    
    # Wait for container to start
    sleep 10
    
    # Test health endpoint
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        print_success "Docker health check passed"
    else
        print_error "Docker health check failed"
        docker logs "$container_id"
        docker stop "$container_id" >/dev/null 2>&1
        docker rm "$container_id" >/dev/null 2>&1
        return 1
    fi
    
    # Cleanup
    docker stop "$container_id" >/dev/null 2>&1
    docker rm "$container_id" >/dev/null 2>&1
    docker rmi budapp-api-test >/dev/null 2>&1
    
    print_success "Docker test completed"
}

# Function to validate workflow files
validate_workflows() {
    print_status "Validating GitHub Actions workflows..."
    
    local workflows=(
        ".github/workflows/ci.yml"
        ".github/workflows/deploy-api.yml"
        ".github/workflows/deploy-mobile.yml"
    )
    
    for workflow in "${workflows[@]}"; do
        if [[ -f "$workflow" ]]; then
            print_success "Workflow found: $workflow"
            
            # Basic YAML syntax check
            if command -v yamllint >/dev/null 2>&1; then
                if yamllint "$workflow" >/dev/null 2>&1; then
                    print_success "YAML syntax valid: $workflow"
                else
                    print_warning "YAML syntax issues in: $workflow"
                fi
            fi
        else
            print_error "Workflow not found: $workflow"
            return 1
        fi
    done
}

# Function to check environment setup
check_environment() {
    print_status "Checking environment setup..."
    
    # Check Node.js version
    if command -v node >/dev/null 2>&1; then
        local node_version
        node_version=$(node --version)
        print_success "Node.js version: $node_version"
        
        # Check if version is 22+
        local major_version
        major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
        if [[ "$major_version" -ge 22 ]]; then
            print_success "Node.js version is compatible"
        else
            print_warning "Node.js version should be 22 or higher"
        fi
    else
        print_error "Node.js not found"
        return 1
    fi
    
    # Check pnpm
    if command -v pnpm >/dev/null 2>&1; then
        local pnpm_version
        pnpm_version=$(pnpm --version)
        print_success "pnpm version: $pnpm_version"
    else
        print_error "pnpm not found"
        return 1
    fi
    
    # Check Flutter (optional)
    if command -v flutter >/dev/null 2>&1; then
        local flutter_version
        flutter_version=$(flutter --version | head -n 1)
        print_success "Flutter: $flutter_version"
    else
        print_warning "Flutter not found (optional for API-only testing)"
    fi
}

# Function to run security checks
run_security_checks() {
    print_status "Running security checks..."
    
    # Run npm audit
    if run_command "pnpm audit --audit-level moderate" "Running dependency audit"; then
        print_success "No security vulnerabilities found"
    else
        print_warning "Security vulnerabilities detected - review audit output"
    fi
    
    # Check for sensitive files
    local sensitive_files=(
        ".env"
        "apps/api/.env"
        "apps/mobile/.env"
        "*.key"
        "*.pem"
        "*.p12"
        "keystore.jks"
    )
    
    for pattern in "${sensitive_files[@]}"; do
        if find . -name "$pattern" -not -path "./node_modules/*" | grep -q .; then
            print_warning "Sensitive files found matching pattern: $pattern"
        fi
    done
}

# Main execution
main() {
    print_status "Starting BudApp CI/CD test suite..."
    
    # Change to project root
    cd "$(dirname "$0")/.."
    
    # Check environment
    check_environment
    
    # Validate workflows
    validate_workflows
    
    # Test API
    test_api
    
    # Test mobile (if Flutter is available)
    test_mobile
    
    # Test Docker
    test_docker
    
    # Run security checks
    run_security_checks
    
    print_success "All CI/CD tests completed successfully!"
    print_status "Your setup is ready for GitHub Actions CI/CD"
    
    echo ""
    print_status "Next steps:"
    echo "  1. Push your changes to GitHub"
    echo "  2. Create a pull request to test the CI pipeline"
    echo "  3. Configure required secrets for deployment"
    echo "  4. Set up staging and production environments"
}

# Handle script arguments
case "${1:-}" in
    "api")
        check_environment
        test_api
        ;;
    "mobile")
        check_environment
        test_mobile
        ;;
    "docker")
        check_environment
        test_docker
        ;;
    "security")
        run_security_checks
        ;;
    *)
        main
        ;;
esac
