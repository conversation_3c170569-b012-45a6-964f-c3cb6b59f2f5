#!/bin/bash

# BudApp CI/CD Setup Script
# This script helps set up the CI/CD environment and validates the configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check GitHub CLI authentication
check_gh_auth() {
    if command_exists gh; then
        if gh auth status >/dev/null 2>&1; then
            print_success "GitHub CLI is authenticated"
            return 0
        else
            print_warning "GitHub CLI is not authenticated"
            return 1
        fi
    else
        print_warning "GitHub CLI is not installed"
        return 1
    fi
}

# Function to validate environment files
validate_env_files() {
    print_status "Validating environment files..."
    
    if [[ -f "apps/api/.env.example" ]]; then
        print_success "API .env.example found"
    else
        print_error "API .env.example not found"
        exit 1
    fi
    
    # Check if .env.test exists for CI
    if [[ -f "apps/api/.env.test" ]]; then
        print_success "API .env.test found"
    else
        print_warning "API .env.test not found - will be created by CI"
    fi
}

# Function to validate Docker setup
validate_docker() {
    print_status "Validating Docker configuration..."
    
    if [[ -f "apps/api/Dockerfile" ]]; then
        print_success "API Dockerfile found"
    else
        print_error "API Dockerfile not found"
        exit 1
    fi
    
    if [[ -f "apps/api/.dockerignore" ]]; then
        print_success "API .dockerignore found"
    else
        print_warning "API .dockerignore not found"
    fi
    
    # Test Docker build if Docker is available
    if command_exists docker; then
        print_status "Testing Docker build..."
        if docker build -t budapp-api-test -f apps/api/Dockerfile . >/dev/null 2>&1; then
            print_success "Docker build test passed"
            docker rmi budapp-api-test >/dev/null 2>&1 || true
        else
            print_error "Docker build test failed"
            exit 1
        fi
    else
        print_warning "Docker not available for build test"
    fi
}

# Function to validate GitHub Actions workflows
validate_workflows() {
    print_status "Validating GitHub Actions workflows..."
    
    local workflows=(
        ".github/workflows/ci.yml"
        ".github/workflows/deploy-api.yml"
        ".github/workflows/deploy-mobile.yml"
    )
    
    for workflow in "${workflows[@]}"; do
        if [[ -f "$workflow" ]]; then
            print_success "Workflow found: $workflow"
        else
            print_error "Workflow not found: $workflow"
            exit 1
        fi
    done
    
    # Validate workflow syntax if GitHub CLI is available
    if check_gh_auth; then
        for workflow in "${workflows[@]}"; do
            if gh workflow view "$(basename "$workflow" .yml)" >/dev/null 2>&1; then
                print_success "Workflow syntax valid: $workflow"
            else
                print_warning "Could not validate workflow: $workflow"
            fi
        done
    fi
}

# Function to check required secrets
check_secrets() {
    print_status "Checking required secrets configuration..."
    
    local api_secrets=(
        "RENDER_STAGING_DEPLOY_HOOK"
        "RENDER_STAGING_URL"
        "RENDER_PRODUCTION_DEPLOY_HOOK"
        "RENDER_PRODUCTION_URL"
        "ADMIN_API_KEY"
    )
    
    local mobile_secrets=(
        "ANDROID_KEYSTORE"
        "ANDROID_KEYSTORE_PASSWORD"
        "ANDROID_KEY_PASSWORD"
        "ANDROID_KEY_ALIAS"
        "GOOGLE_PLAY_SERVICE_ACCOUNT"
        "IOS_CERTIFICATE"
        "IOS_CERTIFICATE_PASSWORD"
        "IOS_PROVISIONING_PROFILE"
        "APPLE_ID"
        "APPLE_APP_PASSWORD"
        "FIREBASE_SERVICE_ACCOUNT"
        "FIREBASE_ANDROID_APP_ID"
        "FIREBASE_IOS_APP_ID"
    )
    
    if check_gh_auth; then
        print_status "Checking API deployment secrets..."
        for secret in "${api_secrets[@]}"; do
            if gh secret list | grep -q "$secret"; then
                print_success "Secret configured: $secret"
            else
                print_warning "Secret not configured: $secret"
            fi
        done
        
        print_status "Checking mobile deployment secrets..."
        for secret in "${mobile_secrets[@]}"; do
            if gh secret list | grep -q "$secret"; then
                print_success "Secret configured: $secret"
            else
                print_warning "Secret not configured: $secret"
            fi
        done
    else
        print_warning "Cannot check secrets without GitHub CLI authentication"
        print_status "Required API secrets: ${api_secrets[*]}"
        print_status "Required mobile secrets: ${mobile_secrets[*]}"
    fi
}

# Function to validate package.json scripts
validate_scripts() {
    print_status "Validating package.json scripts..."
    
    local required_scripts=(
        "build"
        "test"
        "lint"
        "api:build"
        "api:test"
        "db:migrate"
    )
    
    for script in "${required_scripts[@]}"; do
        if npm run "$script" --silent >/dev/null 2>&1 || pnpm run "$script" --silent >/dev/null 2>&1; then
            print_success "Script available: $script"
        else
            print_error "Script not available or failing: $script"
        fi
    done
}

# Function to test database connection
test_database() {
    print_status "Testing database connection..."
    
    if [[ -n "$DATABASE_URL" ]]; then
        # Try to run a simple database test
        if command_exists psql; then
            if psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
                print_success "Database connection successful"
            else
                print_warning "Database connection failed"
            fi
        else
            print_warning "psql not available for database test"
        fi
    else
        print_warning "DATABASE_URL not set"
    fi
}

# Function to create sample environment files
create_sample_env() {
    print_status "Creating sample environment files..."
    
    if [[ ! -f "apps/api/.env.test" ]]; then
        cat > apps/api/.env.test << EOF
# Test Environment Configuration
NODE_ENV=test
PORT=3000
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/budapp_test
JWT_SECRET=test-jwt-secret-key-for-ci-testing-only
LOG_LEVEL=error
EOF
        print_success "Created apps/api/.env.test"
    fi
}

# Main execution
main() {
    print_status "Starting BudApp CI/CD setup validation..."
    
    # Change to project root
    cd "$(dirname "$0")/.."
    
    # Run validations
    validate_env_files
    validate_docker
    validate_workflows
    check_secrets
    validate_scripts
    test_database
    create_sample_env
    
    print_success "CI/CD setup validation completed!"
    print_status "Next steps:"
    echo "  1. Configure required secrets in GitHub repository settings"
    echo "  2. Set up staging and production environments"
    echo "  3. Configure mobile app signing certificates"
    echo "  4. Test the CI pipeline with a pull request"
    echo ""
    print_status "For detailed setup instructions, see docs/ci-cd.md"
}

# Run main function
main "$@"
