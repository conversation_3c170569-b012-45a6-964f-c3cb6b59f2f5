{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "extends": "./tsconfig.paths.json", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "noEmitOnError": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "isolatedModules": true}, "exclude": ["node_modules", "dist"]}