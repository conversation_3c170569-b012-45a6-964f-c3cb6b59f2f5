{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", "dist", "build", "coverage", ".next", ".turbo", ".pnpm-store", "*.generated.*", "*.gen.*", "apps/mobile/**", "pnpm-lock.yaml"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off"}, "suspicious": {"noExplicitAny": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "always", "trailingCommas": "es5", "arrowParentheses": "asNeeded"}}}