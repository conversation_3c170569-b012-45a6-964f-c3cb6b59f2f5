{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**"]}, "lint": {"outputs": []}, "format": {"outputs": []}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"], "env": ["DATABASE_URL", "NODE_ENV"]}, "test:unit": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "__tests__/**/*.ts", "__tests__/**/*.tsx"], "env": ["NODE_ENV"]}, "test:integration": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "__tests__/**/*.ts", "__tests__/**/*.tsx"], "env": ["DATABASE_URL", "NODE_ENV"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "__tests__/**/*.ts", "__tests__/**/*.tsx"], "env": ["DATABASE_URL", "NODE_ENV"]}, "test:coverage": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"], "env": ["DATABASE_URL", "NODE_ENV"]}, "test:coverage:summary": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"], "env": ["DATABASE_URL", "NODE_ENV"]}, "test:coverage:json": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"], "env": ["DATABASE_URL", "NODE_ENV"]}, "db:generate": {"cache": false, "outputs": ["migrations/**"]}, "db:migrate": {"cache": false, "dependsOn": ["build"], "env": ["DATABASE_URL", "NODE_ENV"]}, "db:seed": {"cache": false, "dependsOn": ["build", "db:migrate"], "env": ["DATABASE_URL", "NODE_ENV"]}, "db:seed:test": {"cache": false, "dependsOn": ["build", "db:migrate"], "env": ["DATABASE_URL", "NODE_ENV"]}, "db:studio": {"cache": false, "persistent": true}, "generate:schema": {"cache": false, "outputs": ["generated/**"]}}}