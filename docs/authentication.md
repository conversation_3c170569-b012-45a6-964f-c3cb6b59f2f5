# BudApp Authentication System

This document provides a comprehensive overview of the authentication system implemented in BudApp, covering both the API and mobile application.

## Overview

BudApp implements a secure authentication system using JWT (JSON Web Tokens) for stateless authentication and OAuth 2.0 for social login. The system provides:

- Email/password registration and login
- Social login with Google and Apple
- Secure token storage and management
- Role-based authorization
- Protected routes and queries

## Architecture

The authentication system spans across multiple components:

1. **API (Node.js/Fastify/GraphQL)**
   - JWT token generation and validation
   - Password hashing and verification
   - OAuth provider integration
   - User management in the database
   - Error handling and security measures

2. **Mobile App (Flutter)**
   - Authentication UI (login, register, profile screens)
   - Token storage using secure storage
   - Authentication state management
   - Protected routes
   - Error handling and user feedback

## Authentication Flow

### Email/Password Registration

1. User enters email, password, and optional profile information
2. Client validates the input (email format, password strength)
3. Client sends registration request to the API
4. API validates the input
5. API checks if the email already exists
6. API hashes the password using bcrypt
7. API creates a new user in the database
8. API generates a JWT token
9. API returns the token and user information
10. Client stores the token securely
11. Client updates the authentication state
12. Client navigates to the authenticated area

### Email/Password Login

1. User enters email and password
2. Client validates the input
3. Client sends login request to the API
4. API validates the input
5. API finds the user by email
6. API verifies the password using bcrypt
7. API generates a JWT token
8. API returns the token and user information
9. Client stores the token securely
10. Client updates the authentication state
11. Client navigates to the authenticated area

### Social Login (OAuth 2.0)

1. User clicks on a social login button (Google, Apple)
2. Client initiates the OAuth flow
3. User is redirected to the OAuth provider's login page
4. User authenticates with the provider
5. Provider redirects back to the client with an authorization code
6. Client sends the code to the API
7. API exchanges the code for an access token
8. API retrieves user information from the provider
9. API finds or creates the user in the database
10. API generates a JWT token
11. API returns the token and user information
12. Client stores the token securely
13. Client updates the authentication state
14. Client navigates to the authenticated area

### Token Refresh

1. Client detects that the token is about to expire
2. Client sends a refresh token request to the API
3. API validates the refresh token
4. API generates a new JWT token
5. API returns the new token
6. Client updates the stored token

### Logout

1. User clicks on the logout button
2. Client removes the token from secure storage
3. Client updates the authentication state
4. Client navigates to the unauthenticated area

## API Implementation

### JWT Configuration

```typescript
// config/auth.js
export const authConfig = {
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: '1d',
  },
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      redirectUri: process.env.GOOGLE_REDIRECT_URI,
    },
    apple: {
      clientId: process.env.APPLE_CLIENT_ID,
      teamId: process.env.APPLE_TEAM_ID,
      keyId: process.env.APPLE_KEY_ID,
      privateKey: process.env.APPLE_PRIVATE_KEY,
      redirectUri: process.env.APPLE_REDIRECT_URI,
    },
  },
};
```

### JWT Generation and Validation

```typescript
// lib/auth/jwt.js
import jwt from 'jsonwebtoken';
import { authConfig } from '../../config/auth.js';

export function generateToken(payload) {
  return jwt.sign(payload, authConfig.jwt.secret, {
    expiresIn: authConfig.jwt.expiresIn,
  });
}

export function verifyToken(token) {
  try {
    return jwt.verify(token, authConfig.jwt.secret);
  } catch (error) {
    return null;
  }
}
```

### Password Hashing

```typescript
// lib/auth/password.js
import bcrypt from 'bcrypt';

export async function hashPassword(password) {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

export async function verifyPassword(password, hash) {
  return bcrypt.compare(password, hash);
}
```

### Error Handling

```typescript
// utils/error-handler.js
export enum ErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  UNAUTHORIZED = 'UNAUTHORIZED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  // ...other error codes
}

export class AuthenticationError extends AppError {
  constructor(message, code = ErrorCode.UNAUTHORIZED, details) {
    super(message, code, 401, details);
  }
}

export class InvalidCredentialsError extends AuthenticationError {
  constructor(message = 'Invalid email or password', details) {
    super(message, ErrorCode.INVALID_CREDENTIALS, details);
  }
}
```

## Mobile Implementation

### Secure Storage

```dart
// services/secure_storage_service.dart
class SecureStorageService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  Future<void> saveToken(String token) async {
    await _secureStorage.write(key: 'auth_token', value: token);
  }
  
  Future<String?> getToken() async {
    return await _secureStorage.read(key: 'auth_token');
  }
  
  Future<void> deleteToken() async {
    await _secureStorage.delete(key: 'auth_token');
  }
}
```

### Authentication State

```dart
// models/auth_state.dart
enum AuthStatus {
  initial,
  unauthenticated,
  authenticating,
  authenticated,
  error,
}

class AuthState {
  final AuthStatus status;
  final User? user;
  final String? token;
  final String? errorMessage;
  
  AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.token,
    this.errorMessage,
  });
  
  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? token,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      token: token ?? this.token,
      errorMessage: errorMessage,
    );
  }
}
```

### Authentication Provider

```dart
// providers/auth_provider.dart
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref);
});

class AuthNotifier extends StateNotifier<AuthState> {
  final Ref _ref;
  late final AuthService _authService;
  
  AuthNotifier(this._ref) : super(AuthState()) {
    _authService = _ref.read(authServiceProvider);
    _init();
  }
  
  Future<void> _init() async {
    final token = await _authService.getToken();
    if (token != null) {
      try {
        final user = await _authService.getCurrentUser();
        state = state.copyWith(
          status: AuthStatus.authenticated,
          user: user,
          token: token,
        );
      } catch (e) {
        state = state.copyWith(
          status: AuthStatus.unauthenticated,
        );
      }
    } else {
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
      );
    }
  }
  
  // Login, register, logout methods...
}
```

## Security Considerations

1. **Password Security**
   - Passwords are hashed using bcrypt with appropriate salt rounds
   - Password strength validation is enforced
   - Passwords are never stored in plain text

2. **Token Security**
   - JWT tokens are signed with a strong secret key
   - Tokens have a limited lifetime
   - Tokens are stored securely on the client
   - Sensitive information is not stored in tokens

3. **OAuth Security**
   - OAuth providers are configured with appropriate scopes
   - State parameter is used to prevent CSRF attacks
   - PKCE is used for mobile applications

4. **API Security**
   - All endpoints require HTTPS
   - Rate limiting is implemented
   - Input validation is performed on all requests
   - Error messages don't leak sensitive information

5. **Mobile Security**
   - Tokens are stored in secure storage
   - Authentication state is cleared on logout
   - Network requests use HTTPS
   - App prevents screenshots on sensitive screens

## Testing

The authentication system is thoroughly tested:

1. **Unit Tests**
   - JWT generation and validation
   - Password hashing and verification
   - Authentication service methods

2. **Integration Tests**
   - Registration flow
   - Login flow
   - OAuth flow
   - Token refresh flow

3. **End-to-End Tests**
   - Complete authentication flows from UI to database

## Conclusion

The BudApp authentication system provides a secure, robust, and user-friendly authentication experience. It follows industry best practices for security while maintaining a smooth user experience.
