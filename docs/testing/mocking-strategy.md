# BudApp Mocking Strategy

This document outlines the mocking strategy for the BudApp monorepo. It provides guidelines and examples for mocking external services, dependencies, and other components in tests.

## Table of Contents

1. [Introduction](#introduction)
2. [Mocking Utilities](#mocking-utilities)
3. [HTTP Mocking](#http-mocking)
4. [GraphQL Mocking](#graphql-mocking)
5. [Database Mocking](#database-mocking)
6. [Authentication Mocking](#authentication-mocking)
7. [External Services Mocking](#external-services-mocking)
8. [Best Practices](#best-practices)

## Introduction

Mocking is an essential part of testing as it allows us to isolate the code being tested from its dependencies. The BudApp monorepo uses a consistent approach to mocking across all packages to ensure tests are reliable, maintainable, and easy to understand.

We use the following tools for mocking:

- **MSW (Mock Service Worker)**: For mocking HTTP requests and responses
- **Vitest**: For mocking functions and modules
- **Custom Utilities**: For mocking specific components like GraphQL, database, and authentication

## Mocking Utilities

All mocking utilities are located in the `apps/api/src/test-utils/mocks` directory. The main entry point is `index.ts`, which exports all the mocking utilities.

```typescript
// Import all mocking utilities
import { 
  createMockServer, 
  jsonResponse, 
  errorResponse,
  graphqlResponse,
  graphqlError,
  createGraphQLMocks,
  mockDatabase,
  mockTableOperation,
  mockJwtUtils,
  mockPasswordUtils,
  createOAuthMocks,
  createEmailServiceMocks,
  createPaymentServiceMocks,
  createNotificationServiceMocks
} from '../test-utils/mocks';
```

## HTTP Mocking

For mocking HTTP requests and responses, we use MSW (Mock Service Worker). The `http.ts` file provides utilities for creating a mock server and handling HTTP requests.

### Example: Mocking a REST API

```typescript
import { describe, it, expect } from 'vitest';
import { http } from 'msw';
import { createMockServer, jsonResponse } from '../test-utils/mocks';

// Create mock handlers
const handlers = [
  http.get('https://api.example.com/users', () => {
    return jsonResponse([{ id: 1, name: 'Test User' }]);
  })
];

// Set up the mock server
createMockServer(handlers);

describe('API Client', () => {
  it('fetches users', async () => {
    const response = await fetch('https://api.example.com/users');
    const data = await response.json();
    expect(data).toEqual([{ id: 1, name: 'Test User' }]);
  });
});
```

## GraphQL Mocking

For mocking GraphQL operations, we use the utilities in `graphql.ts`. These utilities allow you to mock GraphQL queries and mutations.

### Example: Mocking GraphQL Operations

```typescript
import { describe, it, expect } from 'vitest';
import { createMockServer, createGraphQLMocks, graphqlResponse } from '../test-utils/mocks';

// Create mock handlers for GraphQL operations
const graphqlMocks = createGraphQLMocks('/graphql', {
  // Mock the 'GetUser' query
  GetUser: (variables) => {
    if (variables.id === '123') {
      return graphqlResponse({
        user: { id: '123', name: 'Test User' }
      });
    }
    
    return graphqlResponse(null, [
      graphqlError('User not found', 'NOT_FOUND', ['user'])
    ]);
  }
});

// Set up the mock server
createMockServer(graphqlMocks);

describe('GraphQL Client', () => {
  it('fetches a user', async () => {
    const response = await fetch('/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: `query GetUser($id: ID!) { user(id: $id) { id name } }`,
        variables: { id: '123' }
      })
    });
    
    const result = await response.json();
    expect(result.data.user).toEqual({ id: '123', name: 'Test User' });
  });
});
```

## Database Mocking

For mocking database operations, we use the utilities in `database.ts`. These utilities allow you to mock Drizzle ORM operations.

### Example: Mocking Database Operations

```typescript
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { mockDatabase, mockTableOperation } from '../test-utils/mocks';
import { UserService } from '../services/user.service';

describe('UserService', () => {
  let cleanup;
  
  beforeEach(() => {
    // Mock the database for this test suite
    cleanup = mockDatabase();
  });
  
  afterEach(() => {
    // Restore the original implementation
    cleanup();
  });
  
  it('should get a user by ID', async () => {
    // Mock the users.select operation to return a specific user
    const mockUser = { id: '123', email: '<EMAIL>', name: 'Test User' };
    mockDatabase(mockTableOperation('users', 'select', [mockUser]));
    
    // Call the service method
    const userService = new UserService();
    const user = await userService.getUserById('123');
    
    // Verify the result
    expect(user).toEqual(mockUser);
  });
});
```

## Authentication Mocking

For mocking authentication, we use the utilities in `auth.ts`. These utilities allow you to mock JWT utilities, password utilities, and OAuth providers.

### Example: Mocking Authentication

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { mockJwtUtils, mockPasswordUtils, createOAuthMocks, createMockServer } from '../test-utils/mocks';
import { AuthService } from '../services/auth.service';

describe('AuthService', () => {
  beforeEach(() => {
    // Mock JWT and password utilities
    mockJwtUtils();
    mockPasswordUtils();
    
    // Set up the mock server with OAuth mocks
    createMockServer(createOAuthMocks());
  });
  
  it('should register a new user', async () => {
    const authService = new AuthService();
    
    const result = await authService.register({
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User',
    });
    
    expect(result.user.email).toBe('<EMAIL>');
    expect(result.token).toBeDefined();
  });
});
```

## External Services Mocking

For mocking external services like email, payment, and notification services, we use the utilities in `services.ts`. These utilities allow you to mock the APIs of these services.

### Example: Mocking External Services

```typescript
import { describe, it, expect } from 'vitest';
import { createMockServer, createEmailServiceMocks } from '../test-utils/mocks';
import { EmailService } from '../services/email.service';

// Set up the mock server with email service mocks
createMockServer(createEmailServiceMocks());

describe('EmailService', () => {
  it('should send an email', async () => {
    const emailService = new EmailService();
    
    const result = await emailService.sendEmail({
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Test Email',
      text: 'This is a test email',
    });
    
    expect(result).toEqual({
      id: 'mock-email-id',
      status: 'sent',
    });
  });
});
```

## Best Practices

1. **Use the provided mocking utilities**: Always use the provided mocking utilities instead of creating your own. This ensures consistency across the codebase.

2. **Mock at the appropriate level**: Mock at the level that makes the most sense for your test. For example, if you're testing a service that makes HTTP requests, mock the HTTP requests. If you're testing a resolver that uses a service, mock the service.

3. **Clean up after tests**: Always clean up after your tests by restoring mocks and stopping mock servers. This prevents test pollution.

4. **Use descriptive names**: Use descriptive names for your mocks and test cases to make it clear what's being tested.

5. **Keep mocks simple**: Keep your mocks as simple as possible while still providing the functionality needed for your tests.

6. **Test edge cases**: Use mocks to test edge cases like error responses, empty results, and other scenarios that might be difficult to test with real dependencies.

7. **Document your mocks**: Document your mocks with comments to make it clear what they're doing and how they should be used.

8. **Use type-safe mocks**: Use TypeScript to ensure your mocks are type-safe and match the expected interfaces.

9. **Avoid testing implementation details**: Focus on testing the behavior of your code, not the implementation details. This makes your tests more resilient to changes.

10. **Keep tests independent**: Each test should be independent of others. Avoid shared state between tests.
