**Product Requirement Document: <PERSON><PERSON><PERSON> (MVP)**

*   **Document Version:** 1.0
*   **Date:** 16.05.2025
*   **Author:** [<PERSON><PERSON><PERSON>, with assistance from PM Expert AI]
*   **Status:** Draft

**Suggestion:** *Consider version controlling this document (e.g., in Git alongside your codebase) to track changes and maintain history.*

## 1. Introduction

### 1.1. Document Purpose
This document outlines the product requirements for the Minimum Viable Product (MVP) of BudApp, a personal finance management application. It serves as the central source of truth for the project team, detailing the product's objectives, target users, features, and technical considerations.

### 1.2. Product Vision
To become the go-to personal finance management application, empowering users worldwide to achieve financial well-being through intuitive tools, actionable insights, and a secure, reliable platform. BudApp will start as a focused mobile application and evolve into a comprehensive cross-platform financial companion.

### 1.3. Product Objectives (MVP)
*   Provide users with a simple, secure, and reliable mobile tool for fundamental personal finance management.
*   Enable users to track income and expenses accurately.
*   Allow users to create and monitor basic budgets.
*   Offer a foundation for tracking savings goals.
*   Deliver basic insights into spending habits.
*   Establish a secure and scalable platform for future feature development.
*   Validate the core value proposition with the target audience.

### 1.4. Core Value Proposition
An intuitive, secure, and comprehensive tool for tracking income and expenses, setting budgets, monitoring financial goals, and gaining insights into spending habits.

## 2. Target Audience
Primarily individuals and young professionals (ages 18-45) seeking a user-friendly mobile tool to gain control over their personal finances. These users are typically tech-savvy, comfortable with mobile applications, and are looking for a modern solution to replace manual tracking methods or overly complex existing tools.

**Suggestion:** *As you gather user feedback post-MVP, consider creating more detailed user personas to further refine feature development and marketing efforts.*

## 3. User Stories (MVP)

User stories will be grouped by the major features of the MVP.

**Suggestion:** *For larger projects, consider managing user stories in a dedicated tool (e.g., Jira, Trello, Asana) for better tracking and sprint planning.*

**Implementation Update:** *Each user story should include 3-5 clear acceptance criteria following this format:*
```
Given [precondition]
When [action]
Then [expected result]
```
*Example:*
```
Given a user with valid credentials
When they submit the login form
Then they should be redirected to the dashboard with a welcome message
```
*This format will be used for all user stories to ensure clear expectations and testable outcomes.*

### 3.1. User Authentication & Profile Management
*   **US1.1:** As a new user, I want to register for an account using my email and a secure password so that I can start using BudApp.
*   **US1.2:** As a new user, I want to register using my Google account so that I can sign up quickly and securely.
*   **US1.3:** As a new user, I want to register using my Apple account so that I can sign up quickly and securely.
*   **US1.4:** As a registered user, I want to log in with my email and password so that I can access my financial data.
*   **US1.5:** As a registered user, I want to log in with my Google account so that I can access my financial data.
*   **US1.6:** As a registered user, I want to log in with my Apple account so that I can access my financial data.
*   **US1.7:** As a registered user, I want to manage my profile (e.g., update username, change password) so that my account information is current and secure.
*   **US1.8:** As a user, I want my session to be managed securely (e.g., JWTs) so that my data is protected and I remain logged in across app uses.
*   **US1.9:** As a new user, I want to verify my email address so that I can secure my account and enable features like password recovery.

### 3.2. Accounts & Wallets Management
*   **US2.1:** As a user, I want to create multiple financial accounts (e.g., Checking, Savings, Credit Card, Cash) so that I can track all my finances in one place.
*   **US2.2:** As a user, I want to assign a type (e.g., Asset, Liability) to each account so that my financial overview is accurate.
*   **US2.3:** As a user, I want to set an initial balance for each account so that my current financial standing is correctly reflected from the start.
*   **US2.4:** As a user, I want to edit the details of an existing account (e.g., name, type) so that I can correct mistakes or update information.
*   **US2.5:** As a user, I want to delete an account if it's no longer needed (with appropriate warnings about data loss).
*   **US2.6:** As a user, I want to mark an account as "primary" or "favorite" so that I can easily access frequently used accounts.

### 3.3. Categories & Subcategories
*   **US3.1:** As a user, I want to see a predefined list of common income and expense categories so that I can quickly start categorizing my transactions.
*   **US3.2:** As a user, I want to create custom categories and subcategories so that I can organize my finances according to my specific needs.
*   **US3.3:** As a user, I want to edit existing custom categories and subcategories so that I can refine my organization over time.
*   **US3.4:** As a user, I want to delete custom categories and subcategories (with options for re-categorizing existing transactions).
*   **US3.5:** As a user, I want to assign custom icons and colors to categories so that I can visually distinguish them and personalize my experience.

### 3.4. Transaction Management (Double-Entry Foundation)
*   **US4.1:** As a user, I want to record an income transaction with details like amount, date, time, category, and receiving account, so that my income is accurately tracked.
*   **US4.2:** As a user, I want to record an expense transaction with details like amount, date, time, category, and paying account, so that my spending is accurately tracked.
*   **US4.3:** As a user, I want to record a transfer transaction between my accounts (e.g., from Checking to Savings) so that my internal fund movements are accurately reflected.
*   **US4.4:** As a user, I want the transaction entry UI to be simple, even though the backend uses double-entry, so that I don't need to understand accounting principles.
*   **US4.5:** As a user, I want to add optional notes and simple tags to my transactions so that I can add more context or search for them later.
*   **US4.6:** As a user, I want to edit existing transactions so that I can correct any mistakes.
*   **US4.7:** As a user, I want to delete transactions (understanding this will adjust account balances).
*   **US4.8:** As a user, I want to set up recurring transactions (daily, weekly, monthly, yearly) for regular income or expenses (e.g., salary, rent) so that I don't have to enter them manually each time.

### 3.5. Budgeting
*   **US5.1:** As a user, I want to set an overall monthly budget for my total income or expenses so that I can manage my overall spending.
*   **US5.2:** As a user, I want to set monthly budgets for specific categories (e.g., Groceries, Entertainment) so that I can control my spending in key areas.
*   **US5.3:** As a user, I want to see visual indicators (e.g., progress bars) of my budget progress so that I can quickly understand how much I've spent versus my budget.

### 3.6. Financial Goal Tracking
*   **US6.1:** As a user, I want to create savings goals (e.g., "Vacation Fund," "New Laptop") with a target amount so that I can work towards specific financial objectives.
*   **US6.2:** As a user, I want to track my contributions towards my savings goals so that I can see my progress.
*   **US6.3:** As a user, I want to see visual indicators of my progress towards achieving my goals.

### 3.7. Basic Reporting & Insights
*   **US7.1:** As a user, I want to see an overview dashboard with my current account balances, recent transactions, and budget summaries so that I can get a quick snapshot of my financial health.
*   **US7.2:** As a user, I want to view simple reports of my expenses and income by category and time period (e.g., a pie chart of this month's spending) so that I can understand my financial habits.

### 3.8. Notifications & Alerts
*   **US8.1:** As a user, I want to receive alerts when I'm about to exceed or have exceeded my budget for a category (e.g., "You've spent 80% of your Groceries budget") so that I can adjust my spending.
*   **US8.2:** As a user, I want to receive low balance alerts for my accounts (configurable threshold) so that I can avoid overdrafts or insufficient funds.
*   **US8.3:** As a user, I want these notifications to be reliable and delivered even if the app is not open (via push notifications).

### 3.9. Multi-Device Sync & Basic Offline Support
*   **US9.1:** As a user with multiple devices (e.g., phone and tablet), I want my financial data to be synchronized across all devices linked to my account so that I always see the latest information.
*   **US9.2:** As a user, I want to be able to view my data and record new transactions even when I'm offline, so that the app is usable without constant internet connectivity.
*   **US9.3:** As a user, I want my offline changes to sync automatically when connectivity is restored.

### 3.10. Subscription Management
*   **US10.1:** As a user, I want to be able to use the core features of the app for free so that I can evaluate its usefulness.
*   **US10.2:** As a user, I want the option to subscribe to a premium tier to unlock advanced features so that I can get more value from the app.
*   **US10.3:** As a user, I want to be able_ (if offered)_ to purchase a lifetime premium option.
*   **US10.4:** As a user, I want the in-app purchase process for subscriptions to be secure and straightforward.

### 3.11. Settings
*   **US11.1:** As a user, I want to select my preferred currency (defaulting to my locale) so that amounts are displayed correctly.
*   **US11.2:** As a user, I want to set my preferred date and time formats so that information is displayed in a way I understand.
*   **US11.3:** As a user, I want to manage my notification preferences so that I only receive alerts I find useful.
*   **US11.4:** As a user, I want an option to export my data (e.g., in JSON format) so that I have a personal backup or can use it elsewhere.
*   **US11.5:** As a user, I want to toggle between Dark Mode and Light Mode so that I can choose the appearance that is most comfortable for my eyes.
*   **US11.6:** As a user, I want a "Secure Mode" option to mask my balances on screen so that I can maintain privacy in public places.

## 4. Product Features (MVP)

This section details the features planned for the Minimum Viable Product.

**Suggestion:** *For each feature, consider adding acceptance criteria to clarify when a feature is considered "done." This PRD is already very detailed, but for specific tasks, acceptance criteria are invaluable.*

### 4.1. User Authentication & Profile Management
*   **Description**: Secure user registration (email/password, OAuth 2.0 for Google & Apple), login, profile management (username, password change, email verification), and session management using JWTs.
*   **Key Details**: Strong password policies, email verification flow, secure OAuth integration.
*   **Corresponds to User Stories**: US1.1 - US1.9.

### 4.2. Accounts & Wallets Management
*   **Description**: Ability to create, edit, and delete multiple financial accounts (e.g., Checking, Savings, Credit Card, Cash). Users can assign account types (Asset, Liability), set initial balances, and mark accounts as "primary" or "favorite."
*   **Key Details**: Clear distinction between asset and liability accounts. Initial balance setting is crucial for accurate starting points.
*   **Corresponds to User Stories**: US2.1 - US2.6.

### 4.3. Categories & Subcategories
*   **Description**: A predefined list of common income/expense categories will be provided. Users can create, edit, and delete custom categories and subcategories, assigning custom icons and colors.
*   **Key Details**: Flexibility for users to tailor categories to their needs. Predefined list aids onboarding.
*   **Corresponds to User Stories**: US3.1 - US3.5.

### 4.4. Transaction Management
*   **Description**: Record income, expenses, and transfers. The system will internally use a double-entry foundation (debits/credits) for data integrity, but the UI will simplify this for the user. Transactions will include fields for amount, date, time, category, account, optional notes, and simple tags. Users can edit and delete transactions. Basic recurring transactions (daily, weekly, monthly, yearly) will be supported.
*   **Key Details**:
    *   **Double-Entry Foundation**: Backend translates user input (e.g., "Expense of $50 for Groceries from Checking Account") into DEBIT "Groceries" (Expense), CREDIT "Checking Account" (Asset).
    *   **Managing Double-Entry Complexity for MVP**: The UI must abstract the complexity of double-entry. Users will interact with single-leg entries (e.g., "add expense"). The backend handles the corresponding credit/debit. This allows for future expansion to more complex multi-leg entries without initial user burden.
    *   **Immutability**: Transaction records (journal entries and lines), once created, should be immutable. Corrections must be made via new, reversing, or adjusting journal entries. The system should provide a user-friendly UI (e.g., a "Correct Mistake" feature) that can automatically generate the necessary reversal and new entries.
*   **Corresponds to User Stories**: US4.1 - US4.8.

### 4.5. Budgeting
*   **Description**: Users can set monthly budgets for overall income/expenses and for specific categories. Visual indicators (e.g., progress bars) will show budget progress.
*   **Key Details**: Focus on simple, monthly budgeting for MVP.
*   **Corresponds to User Stories**: US5.1 - US5.3.

### 4.6. Financial Goal Tracking
*   **Description**: Users can create savings goals (e.g., "Vacation Fund") with target amounts, track contributions, and see visual progress.
*   **Key Details**: Simple goal creation and manual contribution tracking for MVP.
*   **Corresponds to User Stories**: US6.1 - US6.3.

### 4.7. Basic Reporting & Insights
*   **Description**: An overview dashboard showing current balances, recent transactions, and budget summaries. Simple expense/income reports by category/period (e.g., pie chart of spending).
*   **Key Details**: Focus on providing immediately useful, easy-to-understand visual summaries.
*   **Corresponds to User Stories**: US7.1 - US7.2.

### 4.8. Notifications & Alerts
*   **Description**: Server-driven push notifications for budget threshold alerts (e.g., "You've spent 80% of your Groceries budget") and configurable low balance alerts.
*   **Key Details**: Notifications must be reliable and timely. Server-driven approach is critical.
*   **Corresponds to User Stories**: US8.1 - US8.3.

### 4.9. Multi-Device Sync & Basic Offline Support
*   **Description**: Data synchronized across multiple devices linked to the same user account. Ability to view data and record transactions offline, with changes syncing when connectivity is restored.
*   **Key Details**: Last-Writer-Wins (LWW) conflict resolution policy will be used, based on server-authoritative `updated_at` timestamps.
*   **Corresponds to User Stories**: US9.1 - US9.3. (Refer to Section 6.3.2 for detailed sync strategy).

### 4.10. Subscription Management
*   **Description**: Integration with RevenueCat for managing subscriptions. A free tier with core functionality and a premium tier unlocking advanced features (specific premium features TBD for MVP, but the framework will be in place). A lifetime option may be offered.
*   **Key Details**: Secure in-app purchase flow. Clear distinction of free vs. premium (even if premium features are minimal in MVP launch).
*   **Corresponds to User Stories**: US10.1 - US10.4.

### 4.11. Settings
*   **Description**: User preferences for currency selection (default to locale, allow override), date/time format, notification controls, data export (JSON), Dark Mode/Light Mode toggle, and "Secure Mode" (mask balances).
*   **Key Details**: Provide essential customization and accessibility options.
*   **Corresponds to User Stories**: US11.1 - US11.6.

## 5. Design and UX Considerations

*   **Overall Philosophy**: Intuitive, clean, and user-friendly interface. The application should be easy to navigate and understand, even for users new to budgeting apps.
*   **Onboarding**: Implement a minimal user onboarding process using a progressive disclosure pattern, guiding users through initial setup (e.g., adding first account, first transaction) step-by-step.
*   **Visual Design**: Support both Dark Mode and Light Mode. Use clear visual hierarchy and consistent design language (leveraging Flutter's Material/Cupertino widgets or a chosen Flutter component library).
*   **Accessibility**: Strive to meet WCAG AA guidelines where feasible for a mobile application. Consider font sizes, color contrast, and screen reader compatibility.
*   **Feedback & Responsiveness**: The app should provide immediate feedback for user actions. UI should be responsive and performant.
*   **Secure Mode**: A toggle to mask balance values and other sensitive financial figures on screen for privacy in public.
*   **Error Handling**: User-friendly error messages that guide the user on how to resolve issues.

**Suggestion:** *This section should link to detailed design specifications, wireframes, and prototypes once they are available. UI/UX for financial data entry and visualization needs careful thought and user testing.*

## 6. Technical Requirements

### 6.1. Technology Stack

#### 6.1.1. Backend
*   **Runtime/Language**: Node.js 22 LTS (Jod) with TypeScript (pure ESM)
*   **Package Manager**: pnpm
*   **Framework/API**: Fastify 5 + GraphQL (Apollo Server)
*   **GraphQL Schema Design**: Code-first approach (e.g., Nexus/TypeGraphQL)
*   **ORM**: Drizzle ORM
*   **Validation**: Zod
*   **Database**: Supabase (PostgreSQL) with Row-Level Security
*   **Authentication**: Custom JWT-based, OAuth 2.0 (Google, Apple)
*   **Testing**: Vitest
*   **API Documentation**: GraphQL Playground/GraphiQL, consider OpenAPI/Swagger for RESTful utility endpoints
*   **Logging**: Pino
*   **Job Queue**: BullMQ with Redis
*   **Email**: Resend
*   **Hosting**: Render
*   **Error Monitoring**: Sentry
*   **CI/CD**: GitHub Actions
*   **Containerization**: Docker

#### 6.1.2. Mobile
*   **Framework**: Flutter SDK (latest stable version) with Dart
*   **UI Kit**: Material 3 + Cupertino components (no third-party UI libraries for MVP)
*   **Data Fetching/Caching**: graphql_flutter
*   **GraphQL Code Generation**: artemis (with build_runner) for generating Dart types from GraphQL schema
    *   Strong integration with GraphQL Flutter ecosystem
    *   Support for fragments, input objects, and custom scalars
    *   Nullability-aware code generation for Dart's null safety
*   **State Management**: Riverpod
*   **Navigation**: go_router
*   **Forms**: Flutter's built-in Form + TextFormField
*   **Testing**: flutter_test (unit and widget tests), integration_test (E2E)
*   **Build/Deployment**: GitHub Actions with Flutter-optimized workflow
*   **Notifications**: firebase_messaging
*   **Local Storage**: shared_preferences (for JWTs, preferences)
*   **Local Database**: sqflite (for advanced offline capabilities if needed post-MVP)
*   **Error Monitoring**: Sentry (Flutter SDK)
*   **Data Security**: flutter_secure_storage for sensitive secrets (e.g., JWTs, API keys)
*   **Database Encryption**: SQLCipher for Flutter (if needed for sensitive financial data in sqflite)

#### 6.1.3. Monorepo & Build System
*   **Monorepo**: Turborepo with pnpm workspaces
*   **Module Format**: ESM everywhere
*   **GraphQL Schema Management**:
    *   Generate schema from code-first approach in API package
    *   Output/copy to `packages/graphql-schema/` as the single source of truth
    *   Client code generation tools reference this schema
*   **Design Token Management**: 
    *   Consistent format for design tokens (colors, typography, spacing) in JSON/YAML
    *   Codegen process to convert tokens to Dart constants
    *   Packaged as flutter_design_tokens within the monorepo

### 6.2. Architecture Overview
*   **API**: A GraphQL API layer (using Apollo Server with Fastify) will provide data and services to the mobile client.
*   **GraphQL Schema Management**: Code-first approach for schema definition with GraphQL Code Generator for type generation.
*   **Schema Evolution**:
    *   Leverage GraphQL's built-in schema evolution capabilities instead of traditional API versioning
    *   For breaking changes, use GraphQL's deprecation directives for fields and types that will be removed
    *   Define a "breaking-change playbook" for schema evolution, announcing, and migrating clients
*   **Database**: PostgreSQL managed by Supabase will be the primary data store. Drizzle ORM will be used for database interaction on the backend. The Flutter mobile client will use graphql_flutter for caching and may optionally use `sqflite` for more advanced local storage post-MVP.
*   **Authentication**:
    *   JWT-based stateless sessions
    *   OAuth 2.0 for social logins
    *   **OAuth Configuration Management**:
        *   Dedicated `/auth` configuration module with environment-specific configs
        *   Development/Staging/Production OAuth configurations with appropriate redirect URIs
        *   Platform-specific redirect schemes for Flutter (iOS/Android)
        *   Custom URL scheme for app redirects
*   **Double-Entry Accounting**: Core financial transactions will be based on double-entry principles for data integrity, abstracted for user simplicity in the UI.
*   **Offline Support & Sync**: GraphQL client caching with offline mutation queueing (see Section 6.3.2).
*   **Real-time Features (Future)**: While not MVP, the architecture should not preclude future additions like real-time updates.

### 6.3. Data Management

#### 6.3.1. Data Model
*   The detailed database schema using Drizzle ORM is provided in **Appendix A: Data Model Schemas**. Key entities include: `users`, `accounts`, `journal_entries` (transactions), `journal_lines` (transaction legs), `categories`, `budgets`, `goals`, `subscriptions`, `user_settings`, `tags`, `journal_entry_tags`.
*   **General Considerations**:
    *   Enforce relationships with foreign key constraints and appropriate `onDelete` behavior.
    - Strategically add indexes on frequently queried columns.
    - All tables with user-specific data must have Row-Level Security (RLS) policies.
    *   Store currency codes (ISO 4217) consistently.
    *   Use `timestamptz` for timestamps, stored in UTC.

**Suggestion:** *Regularly review and optimize database queries, especially as data volume grows. The RLS performance prototyping mentioned in "Important considerations" is crucial.*

#### 6.3.2. Sync Strategy for Multi-Device and Offline Support
*   **Requirement**: Support multi-device usage and basic offline functionality.
*   **Approach**: GraphQL API will be the single source of truth. The Flutter mobile client will use graphql_flutter for data fetching, mutations, and client-side caching.
*   **Key Components**:
    *   **Timestamping**: All relevant records include server-authoritative `created_at` and `updated_at` (UTC).
    *   **GraphQL Operations**: Client fetches data using GraphQL queries and sends changes via GraphQL mutations.
    *   **Client-Side Caching**: The GraphQL client will manage a normalized cache. Optimistic updates will be used for a responsive UX.
    *   **Background Sync**: Implement background data fetching/mutation submission in Flutter, mindful of battery/data.
*   **Conflict Resolution**: **Last-Writer-Wins (LWW)** policy based on the most recent `updated_at` timestamp (server-authoritative).
*   **Offline Support for MVP**: 
    *   Basic offline cache + queued mutations (built into GraphQL client)
    *   The GraphQL client's cache provides basic offline viewing of previously fetched data
    *   Mutations made offline can be queued and retried upon reconnection
    *   Full sqflite sync logic will be deferred unless user testing shows major data-loss pain points
*   **Client-Side Schema Management (for `sqflite` if used post-MVP)**:
    *   If `sqflite` is adopted for complex offline storage post-MVP, a versioning and migration strategy for the local client SQLite database (e.g., using `sqflite_migration_plan` or similar) will be necessary.
    *   Implement embedded client migrations, automated execution, pre-migration backups, and rollback paths.

#### 6.3.3. Data Integrity & Validation
*   **Double-Entry Foundation**: Ensures balance consistency and provides an audit trail.
*   **Immutable Transaction Records**: Corrections via new, reversing, or adjusting entries.
*   **Currency Precision**: Use fixed-point decimal types for monetary values (e.g., `DECIMAL(19,4)` in PostgreSQL).
*   **Date Range and Timezone Handling**:
    *   Store event timestamps in UTC.
    *   Capture user's local timezone offset for user-input dates/times.
    *   Server-side validation on UTC; client-side display using device timezone.
*   **Balance Consistency & Negative Balances**:
    *   Define `AccountType` (Asset, Liability, etc.).
    *   Asset accounts generally non-negative unless overdraft is configured.
    *   Liability accounts (e.g., credit cards) represent debt. Credit limits will be enforced.
    *   Implement alerts for low balances, approaching credit limits, and overdrafts.
*   **Layered Validation**: Zod at API boundaries (GraphQL resolvers), business logic validation, PostgreSQL constraints.
*   **Error Handling Strategy**:
    *   **GraphQL Error Structure**:
      ```typescript
      {
        code: "ERROR_CODE", // Machine-readable code
        message: "User-friendly message",
        details: {} // Additional context (only for logging)
      }
      ```
    *   **Standard Error Codes**:
        *   `AUTHENTICATION_ERROR` - User not authenticated
        *   `AUTHORIZATION_ERROR` - User not authorized
        *   `VALIDATION_ERROR` - Input validation failed
        *   `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
        *   `BUSINESS_RULE_VIOLATION` - Business logic constraint
        *   `SERVER_ERROR` - Unexpected server error
    *   **Client-side handling approach**:
        *   Map error codes to localized user messages
        *   Define retry policies for transient errors
        *   Log appropriately while protecting sensitive information
*   **Audit Trail**: Maintain an audit trail for all financial data modifications (achieved through immutable transactions and new adjusting entries).

#### 6.3.4. Database Backup & Recovery
*   Leverage Supabase's automated backup features.
*   **Recovery Time Objective (RTO)**: 4 hours
*   **Recovery Point Objective (RPO)**: 1 hour
*   **Regular Backup Verification**: Monthly "restore staging DB from backup" drill to verify RPO/RTO objectives and ensure backup integrity.

#### 6.3.5. Data Migration Strategy (Server-Side)
*   Implement versioned database migrations using Drizzle ORM from day one.
*   Integrate migrations into the CI/CD pipeline, applied before new app versions deploy.
*   Plan for backward compatibility in API responses if schema changes affect them.

### 6.4. Security
*   **Authentication**: Secure email/password with hashing (bcrypt/argon2), OAuth 2.0 (Google, Apple), JWTs for sessions.
*   **Authorization**: Row-Level Security (RLS) in Supabase to ensure users only access their own data.
    *   **RLS Performance Prototyping**: Critically, prototype typical queries with RLS active against realistic data volumes to identify and mitigate performance bottlenecks early. `EXPLAIN ANALYZE` is your friend.
*   **Data Encryption**:
    *   Data in transit: TLS/SSL everywhere.
    *   Data at rest: Supabase provides at-rest encryption. For client-side, use `flutter_secure_storage` for sensitive secrets (e.g., JWTs, API keys). If `sqflite` is used and encryption is needed, consider SQLCipher for Flutter.
*   **Input Validation**: Rigorous validation using Zod on all API inputs (GraphQL resolvers).
*   **Rate Limiting**: Implement API rate limits to prevent abuse.
*   **GDPR/CCPA Compliance**:
    *   Right to Access & Portability: Users can export their data (JSON).
    *   Right to be Forgotten (Erasure):
        *   PII anonymized/deleted from live DB:
            *   Fully anonymize direct identifiers (names, SSNs, emails)
            *   Pseudonymize or disassociate indirect identifiers (device IDs)
        *   Transactional data disassociated from PII. Retain anonymized/pseudonymized transaction metadata for legal/audit purposes (e.g., 5 years, subject to legal review).
        *   **Legal Review Crucial**: Obtain formal legal opinion on the erasure strategy, especially for GDPR.
        *   Data purged from backups per rotation schedule.
    *   Consent Management: Clear consent for data collection/processing.
    *   Privacy Policy: Clear, accessible, up-to-date.
    *   **Data Lifecycle & Erasure Plan Diagram (Mermaid)**:
        ```mermaid
        graph TD
            A[User Inputs Data via Mobile App] --> B{Client-Side Caching (GraphQL client / sqflite)};
            B --> C{Sync to Server};
            C --> D[API (Fastify/GraphQL) Receives Data];
            D --> E[Server-Side Validation (Zod)];
            E --> F[PostgreSQL Database (Supabase - Drizzle ORM)];
            F --> G[Regular Backups (e.g., Daily/Weekly)];

            subgraph Data Storage & Processing
                B; F; G;
            end

            subgraph User Rights
                H[User Requests Data Export] --> I[API Generates Export File (JSON)];
                I --> J[User Downloads Data];
                K[User Requests Data Erasure] --> L{Verification Process};
                L -- Verified --> M[Anonymize/Delete PII from Live DB (PostgreSQL)];
                M --> N[Disassociate/Anonymize Transactional Data];
                N --> O[Data Flagged for Purge in Backups];
                G -- Rotation Schedule --> P[Older Backups Expired/Deleted];
                O -.-> P;
            end

            A --> Q[Third-Party Services (e.g., Auth, Push Notifications, Analytics - with consent)];
            Q --> R[Data Processed by Third Parties];
            K --> S{Erasure Request Propagated to Third Parties};

            style A fill:#lightgrey
            style B fill:#lightblue
            style C fill:#lightyellow
            style D fill:#lightyellow
            style E fill:#lightyellow
            style F fill:#skyblue
            style G fill:#blue,color:#fff
            style H fill:#lightgreen
            style I fill:#lightgreen
            style J fill:#lightgreen
            style K fill:#lightcoral
            style L fill:#lightcoral
            style M fill:#orange
            style N fill:#orange
            style O fill:#darkorange
            style P fill:#grey,color:#fff
            style Q fill:#lightgrey
            style R fill:#lightgrey
            style S fill:#lightcoral
        end
        ```
*   **Secure Mode Feature**: Client-side feature to mask balances.
*   **Regular Security Audits**: Plan for periodic security assessments post-MVP.

### 6.5. Performance & Scalability
*   **Client-Side**:
    *   **Flutter Performance Metrics**:
        *   Cold start < 2s on mid-range devices
        *   Screen transition < 100ms
        *   Maintain 60 fps for scrolls/animations
    *   Monitor and optimize app bundle size.
    *   Efficient local data handling with GraphQL client cache and potentially `sqflite`/`shared_preferences`.
*   **Server-Side**:
    *   Optimize database queries and use appropriate indexing.
    *   Efficient GraphQL schema design to prevent N+1 query issues.
    *   Horizontal scalability of API instances on Render.
    *   Use BullMQ for background jobs to offload long-running tasks.
*   **Caching**: Implement caching strategies at appropriate levels (client-side with GraphQL client, potentially Redis for server-side query results or session data).
*   **API Response Times**: Target specific response time percentiles (e.g., P95 < 200ms for common requests).

### 6.6. Notifications
*   Utilize server-driven push notifications via firebase_messaging for reliability of budget and low balance alerts.
*   **Default Notification Thresholds**:
    *   Budget alerts at 80% spent
    *   Low balance alerts at €50 remaining
    *   User-configurable thresholds for both categories will be available

### 6.7. Internationalization (i18n) & Localization (l10n)
*   **MVP**: Single language (English).
*   **Foundation**: Implement an i18n framework early to facilitate future language additions.
*   Currency, date, and number formats should be locale-aware where possible, with user overrides.

### 6.8. Monitoring and Observability
*   **Hybrid Approach**: Grafana Cloud (backend metrics, logs, traces) and Sentry (frontend error tracking, session analysis).
*   **Key Monitoring Areas**: API performance, database performance, application errors (frontend/backend), system resource utilization, background job monitoring, client-side performance.
*   **Health Endpoints**: `/health/live`, `/health/ready` for backend.
*   **Correlation**: Use shared identifiers (correlation IDs) to link frontend and backend telemetry.

### 6.9. Development Process & Quality Assurance
*   **Monorepo Structure**: As per the suggested `apps/`, `packages/` layout.
*   **Database Seeding Strategy**:
    *   **Tiered approach**:
        *   **Base seed**: Minimal data required for app to function (system settings, defaults)
        *   **Dev seed**: Rich dataset for local development
        *   **Test seed**: Predictable data for automated tests
    *   **Implementation**: In `packages/db/seeds/` with seed types organized into subdirectories
*   **Test Driven Development (TDD) / Behavior Driven Development (BDD) Principles**:
    *   Adopt a "test-first" or "test-alongside" approach.
    *   **Unit Tests (Vitest for backend, flutter_test for mobile)**: For functions, modules, components.
    *   **Integration Tests (Vitest with Fastify `inject`, flutter_test for widget tests)**: For API endpoints, component interactions.
    *   **E2E Tests (integration_test for Flutter)**: For critical user flows.
    *   **Coverage**: Aim for high test coverage (e.g., >80% for critical logic).
*   **CI/CD Pipeline (GitHub Actions)**:
    *   Automate linting, testing, building for backend and mobile.
    *   Automate backend deployment to Render.
    *   Automate Flutter builds/submissions to TestFlight/Google Play Console via GitHub Actions with Flutter-optimized workflow.
    *   Secure environment variable management.
    *   Integrate database migrations.
    *   Define rollback strategies.
*   **Coding Standards**: Enforce with ESLint, Prettier.
*   **Code Reviews**: Mandatory for all code changes.
*   **Feature Flags**: Consider a lightweight system (e.g., environment variables or a simple config) for toggling new/risky features, especially in early releases. (LaunchDarkly/Unleash might be overkill for MVP but good to keep in mind).
*   **Standardized Error Responses**: Consistent error format for APIs.

## 7. Non-Functional Requirements (NFRs) Summary

*   **Security**: Adherence to best practices outlined in Sec 6.4, including RLS, data encryption, and secure authentication.
*   **Performance**:
    *   App cold start time (target: < 2 seconds on mid-range devices).
    *   Screen transition time (target: < 100ms).
    *   Maintain 60 fps for scrolls/animations.
    *   API P95 response time (target: < 200ms for core operations).
    *   Offline operations should be seamless.
*   **Reliability**:
    *   System uptime (target: 99.9%).
    *   Data integrity ensured by double-entry and robust validation.
    *   Low crash rate for mobile app (target: < 0.1% of sessions).
*   **Scalability**: Architecture should support growth to [e.g., 10,000] active users within the first year without major re-architecture.
*   **Usability**: Intuitive and easy-to-learn interface. Target high user satisfaction scores (e.g., >4.0 app store rating).
*   **Maintainability**: Well-structured, documented code. Adherence to monorepo structure and ESM.
*   **Testability**: High test coverage for critical paths. All tests pass in CI.
*   **Compliance**: GDPR/CCPA readiness as detailed.

**Suggestion:** *Quantify NFRs where possible (as shown with examples). These targets will help guide development and testing.*

## 8. Success Metrics (MVP)

How we will measure the success of the BudApp MVP:

*   **User Acquisition & Adoption**:
    *   Number of app downloads.
    *   Number of registered users.
    *   Activation Rate: % of downloads that complete onboarding and record at least one transaction.
*   **User Engagement**:
    *   Daily Active Users (DAU) / Monthly Active Users (MAU) ratio.
    *   Average session duration.
    *   Feature Adoption:
        *   % of active users who have created at least one budget.
        *   % of active users who have created at least one financial goal.
        *   Average number of transactions logged per active user per week.
*   **Retention**:
    *   Day 1, Day 7, Day 30 user retention rates.
    *   Churn rate.
*   **App Performance & Stability**:
    *   App crash rate (via Sentry).
    *   Average API response time (via Grafana/Sentry).
    *   App Store ratings and reviews.
*   **Monetization (Post-MVP focus, but foundation in MVP)**:
    *   Free-to-Premium conversion rate.
    *   **Premium Tier Features**:
        *   Account limit: up to 5 accounts (vs. 2 in free tier)
        *   Categories: all custom categories unlocked (vs. base set in free tier)
        *   Reporting: simple category spend analysis (vs. "coming soon" advanced trends in free tier)

**Suggestion:** *Set specific targets for these metrics before launch (e.g., "Achieve 1,000 MAU within 3 months post-launch"). Regularly track and review these metrics to inform product decisions.*

## 9. Release Criteria (MVP)

The BudApp MVP will be considered ready for release when the following criteria are met:

*   All MVP features defined in Section 4 are implemented and pass QA.
*   User stories (Section 3) associated with MVP features are fulfilled.
*   Critical and Major bugs identified during testing are resolved.
*   Security:
    *   RLS policies implemented and tested.
    *   Authentication flows are secure and tested.
    *   Basic GDPR/CCPA measures (privacy policy, data export) are in place.
*   Performance: Meets NFR targets for app responsiveness and API speed under simulated load.
*   Offline sync functionality is robust and tested across various scenarios (conflicts, intermittent connectivity).
*   Push notifications (budget, low balance alerts) are functional and reliable.
*   Subscription management flow (free tier, placeholder for premium) via RevenueCat is functional.
*   Legal: Privacy Policy and Terms of Service are finalized and accessible within the app.
*   Documentation:
    *   Basic user-facing FAQ or help section.
    *   Internal technical documentation for key architectural components.
*   CI/CD pipeline is operational for both backend and mobile deployments.
*   Successful build and submission to TestFlight (iOS) and Google Play Internal Testing (Android).
*   Monitoring (Sentry, Grafana) is configured for production environments.

## 10. Future Considerations (Post-MVP Roadmap)

The following features are planned for future releases beyond the MVP:

*   **Advanced Reporting & Visualizations**: Customizable dashboards, cash flow projections, net worth tracking, comparison reports, trend analysis.
*   **Debt Management**: Detailed loan tracking, debt payoff planning tools.
*   **Investment Tracking**: Basic manual tracking of investment accounts.
*   **Advanced Transaction Features**: Split transactions, transaction attachments (receipts), advanced search/filtering, bulk editing.
*   **Multi-Currency Support**: True multi-currency accounts with automatic/manual exchange rate handling.
*   **Shared Budgets & Accounts**: Ability to share items with other BudApp users.
*   **Import/Export Enhancements**: Import from CSV/OFX, automated bank account linking (e.g., Plaid).
*   **Full Internationalization (i18n) & Localization (l10n)**.
*   **Web Application**: Companion web interface.
*   **AI-Powered Insights & Recommendations**: Automated categorization, spending pattern analysis, predictive forecasting.
*   **Invoice Management (for Freelancers/Small Businesses)**.
*   **Enhanced Security Features**: Two-Factor Authentication (2FA), biometric authentication.
*   **Gamification & Rewards**.

**Suggestion:** *Prioritize this list based on MVP feedback and strategic goals. Each major feature here will warrant its own PRD or feature specification.*

## 11. Risks and Dependencies

### 11.1. Risks
*   **Technical Complexity**:
    *   Implementing the double-entry backend accurately and efficiently.
    *   Developing a robust and bug-free multi-device sync mechanism with offline support can be challenging.
    *   Learning curve and ramp-up time for Flutter/Dart and GraphQL/Apollo Server for the team.
    *   Potential for over-fetching or under-fetching with GraphQL if queries are not designed carefully.
    *   Maturity and stability of certain Flutter plugins compared to the React Native ecosystem.
    *   Managing state effectively in Flutter (choice of Provider, Riverpod, BLoC etc. has implications).
*   **Performance**:
    *   RLS policies in Supabase impacting query performance at scale.
    *   Flutter performance on lower-end devices or with complex widget trees.
    *   Background sync impacting battery life if not optimized.
*   **Security**:
    *   Vulnerabilities in custom authentication or third-party integrations.
    *   Ensuring data privacy and compliance with GDPR/CCPA, especially regarding the "Right to be Forgotten" for financial data.
*   **User Adoption**:
    *   Competition from established budgeting apps.
    *   Onboarding complexity discouraging new users.
*   **Third-Party Dependencies**:
    *   Reliance on Supabase, Render, RevenueCat, and other services (uptime, cost, feature changes).
    *   Future reliance on Plaid or similar for bank aggregation.
*   **Scope Creep**: Tendency to add more features into MVP, delaying release.
*   **Monetization**: Achieving a sustainable free-to-premium conversion rate.
*   **Team Capacity/Expertise**: Ensuring the team has the necessary skills for the chosen tech stack and complex features.

**Suggestion:** *For each high-impact risk, define mitigation strategies and contingency plans.*

### 11.2. Dependencies
*   **External Services**:
    *   Supabase for database and backend services.
    *   Render for API hosting.
    *   RevenueCat for subscription management.
    *   Resend for email delivery.
    *   OAuth providers (Google, Apple) for social login.
    *   Firebase Cloud Messaging (FCM) / Apple Push Notification service (APNs) for notifications.
*   **Internal**:
    *   Availability of development and QA resources.
    *   Completion of UI/UX designs for all MVP screens.
*   **Legal/Compliance**:
    *   Legal review of Privacy Policy, Terms of Service, and GDPR/CCPA compliance strategy, especially for data erasure and retention.

## 12. Open Issues / Questions for Discussion
*   ~~What are the specific features that will differentiate the Premium tier from the Free tier in the MVP launch?~~ **RESOLVED**: Premium tier features defined in Section 8 (Success Metrics): Up to 5 accounts (vs. 2 in free tier), all custom categories unlocked, and simple category spend analysis.
*   ~~What are the exact thresholds and configurability options for budget and low balance alerts in MVP?~~ **RESOLVED**: Default thresholds defined in Section 6.6 (Notifications): Budget alerts at 80% spent, low balance alerts at €50 remaining, with user-configurable options.
*   ~~Which Flutter state management solution will be adopted?~~ **RESOLVED**: Riverpod selected as per Section 6.1.2 (Mobile).
*   ~~Strategy for designing GraphQL schema?~~ **RESOLVED**: Code-first approach selected as per Section 6.1.1 (Backend). Schema evolution will leverage GraphQL's built-in capabilities with deprecation directives as noted in Section 6.2.
*   ~~Which Flutter navigation package best fits our needs?~~ **RESOLVED**: go_router selected as per Section 6.1.2 (Mobile).
*   ~~What is the strategy for Flutter UI components?~~ **RESOLVED**: Material 3 + Cupertino components with no third-party UI libraries for MVP as per Section 6.1.2 (Mobile).
*   ~~How will GraphQL Code Generation be set up for Flutter?~~ **RESOLVED**: GraphQL Code Generator will be used as noted in Section 6.1.1 (Backend) and 6.2.
*   ~~Re-evaluation of detailed offline support requirements for MVP?~~ **RESOLVED**: GraphQL client caching will be used for basic offline viewing with queued mutations, deferring full sqflite sync to post-MVP as per Section 6.3.2.
*   ~~Specific RTO/RPO targets for database backup and recovery?~~ **RESOLVED**: RTO of 4 hours and RPO of 1 hour defined in Section 6.3.4.

### Remaining Open Issues:
*   Detailed criteria for "significant" PII that requires anonymization versus data that can be disassociated.
*   Specific user experience for correcting transaction mistakes (implementation of the immutable transaction records principle).
*   Detailed specification for how the graphql_flutter client will handle conflict resolution in practice.

## Appendix A: Data Model Schemas (Drizzle ORM)
*(This section would contain the full Drizzle ORM schema definitions as provided in the initial plan. For brevity in this response, I will state that it's included here. The user provided this in full detail, so it would be copied verbatim into this section of the actual PRD).*

The proposed database schemas using Drizzle ORM are as follows:
*   `users`
*   `accounts`
*   `journal_entries` (Transactions)
*   `journal_lines` (Transaction Legs)
*   `categories`
*   `journal_entry_categories` (For split transactions - consider if truly MVP or defer if single category per transaction is sufficient for MVP)
    *   **Suggestion:** *The initial plan for `journal_entry_categories` enables split transactions. If split transactions are not MVP, this could be simplified by adding `category_id` directly to `journal_entries` or `journal_lines` for MVP, and introducing this M2M table later. This aligns with "Managing Double-Entry Complexity for MVP." For MVP, a single category per transaction (or per expense/income leg of a simplified transaction) is common.*
*   `budgets`
*   `budget_categories`
*   `goals`
*   `subscriptions`
*   `user_settings`
*   `tags`
*   `journal_entry_tags`

**General Schema Considerations (Recap from Technical Requirements):**
*   **Foreign Keys**: Enforce relationships with `onDelete` behavior.
*   **Indexes**: Strategic indexing for performance.
*   **RLS Policies**: Mandatory for user data tables.
*   **Currency Codes**: ISO 4217, stored with amounts.
*   **Timestamps**: `timestamptz` in UTC.

## Appendix B: Monorepo Project Structure
```
.
├── apps
│   ├── api/                 # Fastify 5 + GraphQL API (Apollo Server), Drizzle ORM, Vitest
│   └── mobile/              # Flutter (Dart), GraphQL client (graphql_flutter)
├── packages
│   ├── graphql-schema/      # Shared GraphQL schema and type definitions
│   │   ├── schema.graphql   # Generated SDL schema file from code-first approach
│   │   ├── fragments/       # Shared GraphQL fragments used across clients
│   │   └── validation/      # Schema validation scripts
│   ├── db/                  # Drizzle schema & migrations
│   │   └── seeds/           # Database seeding
│   │       ├── base/        # Minimal data required for app to function (always runs)
│   │       ├── dev/         # Rich dataset for local development
│   │       ├── test/        # Predictable data for automated tests
│   │       └── index.ts     # Main seeding logic
│   ├── config/              # eslint, tsconfig, jest/vitest presets
│   └── design/              # Theme tokens for UI (with codegen for Flutter)
├── scripts/                 # build, deploy & utility scripts
└── docs/
│   ├── developer/           # developer guides, architecture decisions
│   └── user/                # user documentation
├── turbo.json
├── tsconfig.base.json
└── pnpm-workspace.yaml
```