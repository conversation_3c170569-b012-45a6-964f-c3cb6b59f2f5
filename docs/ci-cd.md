# CI/CD Documentation

This document describes the Continuous Integration and Continuous Deployment (CI/CD) setup for BudApp.

## Overview

BudApp uses GitHub Actions for CI/CD with the following workflows:

1. **CI Pipeline** (`ci.yml`) - Runs on every push and pull request
2. **API Deployment** (`deploy-api.yml`) - Deploys the API to staging and production
3. **Mobile Deployment** (`deploy-mobile.yml`) - Builds and deploys mobile apps

## CI Pipeline (`ci.yml`)

### Triggers
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop` branches

### Jobs

#### 1. Setup Dependencies
- Caches pnpm and Flutter dependencies
- Sets up Node.js 22 and Flutter 3.29.3
- Installs dependencies for both API and mobile

#### 2. Code Quality
- Runs Biome linting and formatting checks
- Builds TypeScript code
- Ensures code quality standards

#### 3. API Testing
- Sets up PostgreSQL test database
- Runs unit, integration, and E2E tests
- Generates and uploads test coverage reports
- Runs database migrations

#### 4. Mobile Testing
- Runs Flutter code analysis
- Executes Flutter unit tests
- Generates test coverage reports

#### 5. Security Scanning
- Runs npm audit for dependency vulnerabilities
- Performs CodeQL security analysis

## API Deployment (`deploy-api.yml`)

### Triggers
- Push to `main` branch (automatic production deployment)
- Manual workflow dispatch with environment selection

### Jobs

#### 1. Build and Test
- Runs full test suite
- Builds production-ready code
- Uploads build artifacts

#### 2. Build Docker Image
- Creates optimized Docker image
- Pushes to GitHub Container Registry
- Uses multi-stage build for production optimization

#### 3. Deploy to Staging
- Deploys to staging environment
- Runs health checks
- Available for all main branch pushes

#### 4. Deploy to Production
- Deploys to production environment
- Runs database migrations
- Performs health checks
- Requires staging deployment success

#### 5. Notifications
- Sends deployment status notifications

## Mobile Deployment (`deploy-mobile.yml`)

### Triggers
- Push to `main` branch affecting mobile code
- Manual workflow dispatch with platform and environment selection

### Jobs

#### 1. Build and Test
- Runs Flutter tests and analysis
- Validates code quality

#### 2. Build Android
- Creates APK for staging (debug)
- Creates APK and AAB for production (release)
- Signs with production certificates

#### 3. Build iOS
- Creates IPA for staging and production
- Signs with Apple certificates
- Requires macOS runner

#### 4. Deploy to App Stores
- Uploads to Google Play Store (internal track)
- Uploads to Apple App Store
- Only for production builds

#### 5. Deploy to Firebase App Distribution
- Distributes staging builds to testers
- Provides early access for testing

## Required Secrets

### API Deployment
```
GITHUB_TOKEN                    # Automatically provided
RENDER_STAGING_DEPLOY_HOOK     # Render staging deployment webhook
RENDER_STAGING_URL             # Staging environment URL
RENDER_PRODUCTION_DEPLOY_HOOK  # Render production deployment webhook
RENDER_PRODUCTION_URL          # Production environment URL
ADMIN_API_KEY                  # API key for admin operations
```

### Mobile Deployment
```
# Android
ANDROID_KEYSTORE              # Base64 encoded keystore file
ANDROID_KEYSTORE_PASSWORD     # Keystore password
ANDROID_KEY_PASSWORD          # Key password
ANDROID_KEY_ALIAS            # Key alias
GOOGLE_PLAY_SERVICE_ACCOUNT   # Google Play service account JSON

# iOS
IOS_CERTIFICATE              # Base64 encoded P12 certificate
IOS_CERTIFICATE_PASSWORD     # Certificate password
IOS_PROVISIONING_PROFILE     # Base64 encoded provisioning profile
APPLE_ID                     # Apple ID for App Store uploads
APPLE_APP_PASSWORD           # App-specific password

# Firebase App Distribution
FIREBASE_SERVICE_ACCOUNT     # Firebase service account JSON
FIREBASE_ANDROID_APP_ID      # Firebase Android app ID
FIREBASE_IOS_APP_ID         # Firebase iOS app ID
```

## Environment Configuration

### Staging Environment
- Automatic deployment from `main` branch
- Uses debug builds for mobile
- Connected to staging database
- Available for testing and validation

### Production Environment
- Manual approval required
- Uses release builds with signing
- Connected to production database
- Includes database migrations

## Docker Configuration

### Multi-stage Build
The API uses a multi-stage Docker build:

1. **Base**: Sets up Node.js and pnpm
2. **Development**: For local development
3. **Build**: Compiles TypeScript
4. **Production Dependencies**: Installs only production dependencies
5. **Production**: Final optimized image

### Health Checks
- Built-in Docker health check
- Monitors `/health` endpoint
- 30-second intervals with 3 retries

## Monitoring and Observability

### Test Coverage
- Uploaded to Codecov
- Separate reports for API and mobile
- Minimum 80% coverage required

### Security
- Dependabot for automated dependency updates
- CodeQL for security analysis
- npm audit for vulnerability scanning

### Performance
- Docker image optimization
- Caching strategies for dependencies
- Parallel job execution where possible

## Development Workflow

### Pull Request Process
1. Create feature branch
2. Make changes and commit
3. Push to GitHub
4. CI pipeline runs automatically
5. Review and merge after CI passes

### Deployment Process
1. Merge to `main` triggers staging deployment
2. Staging deployment runs automatically
3. Production deployment requires manual approval
4. Health checks validate deployment success

## Troubleshooting

### Common Issues

#### CI Failures
- Check test failures in GitHub Actions logs
- Verify environment variables are set
- Ensure database migrations are included

#### Deployment Failures
- Check health check endpoints
- Verify secrets are configured correctly
- Review deployment logs in hosting platform

#### Mobile Build Failures
- Verify signing certificates are valid
- Check Flutter version compatibility
- Ensure platform-specific configurations are correct

### Debugging Tips
- Use workflow dispatch for manual testing
- Check artifact uploads for build outputs
- Review security scan results for vulnerabilities

## Best Practices

### Code Quality
- All code must pass linting and formatting checks
- Maintain test coverage above 80%
- Include tests for new features

### Security
- Keep dependencies updated via Dependabot
- Review security scan results
- Use secure secrets management

### Performance
- Optimize Docker images
- Use caching for dependencies
- Monitor build times and optimize as needed

## Future Improvements

### Planned Enhancements
- Integration with monitoring tools (Sentry, DataDog)
- Automated performance testing
- Blue-green deployment strategy
- Automated rollback on failure
- Enhanced notification system (Slack, Discord)

### Monitoring Integration
- Application performance monitoring
- Error tracking and alerting
- Infrastructure monitoring
- User analytics integration
