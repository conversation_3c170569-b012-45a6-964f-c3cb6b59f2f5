version: 2
updates:
  # Enable version updates for npm (root)
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "budapp-team"
    assignees:
      - "budapp-team"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"

  # Enable version updates for npm (API)
  - package-ecosystem: "npm"
    directory: "/apps/api"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "budapp-team"
    assignees:
      - "budapp-team"
    commit-message:
      prefix: "deps(api)"
      prefix-development: "deps-dev(api)"
      include: "scope"
    labels:
      - "dependencies"
      - "api"
      - "automated"

  # Enable version updates for pub (Flutter)
  - package-ecosystem: "pub"
    directory: "/apps/mobile"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "budapp-team"
    assignees:
      - "budapp-team"
    commit-message:
      prefix: "deps(mobile)"
      include: "scope"
    labels:
      - "dependencies"
      - "mobile"
      - "automated"

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/apps/api"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "budapp-team"
    assignees:
      - "budapp-team"
    commit-message:
      prefix: "deps(docker)"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
      - "automated"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "budapp-team"
    assignees:
      - "budapp-team"
    commit-message:
      prefix: "deps(actions)"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
      - "automated"
