name: CI

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main, staging]

permissions:
  contents: read
  security-events: write
  actions: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '22'
  PNPM_VERSION: '10.11.0'
  FLUTTER_VERSION: '3.29.3'

jobs:
  # Job 1: Setup and Cache Dependencies
  setup:
    name: Setup Dependencies
    runs-on: ubuntu-latest
    outputs:
      api-cache-hit: ${{ steps.api-cache.outputs.cache-hit }}
      mobile-cache-hit: ${{ steps.mobile-cache.outputs.cache-hit }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Cache pnpm dependencies
        id: api-cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        if: steps.api-cache.outputs.cache-hit != 'true'
        run: pnpm install --frozen-lockfile

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Cache Flutter dependencies
        id: mobile-cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.pub-cache
            apps/mobile/.dart_tool
          key: ${{ runner.os }}-flutter-${{ hashFiles('apps/mobile/pubspec.lock') }}
          restore-keys: |
            ${{ runner.os }}-flutter-

      - name: Install Flutter dependencies
        if: steps.mobile-cache.outputs.cache-hit != 'true'
        working-directory: apps/mobile
        run: flutter pub get

      - name: Generate code
        working-directory: apps/mobile
        run: dart run build_runner build --delete-conflicting-outputs

  # Job 2: Code Quality Checks
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Restore pnpm dependencies
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Biome linting
        run: pnpm lint

      - name: Run Biome formatting check
        run: pnpm check

      - name: Build TypeScript
        run: pnpm build

  # Job 3: API Testing
  api-test:
    name: API Tests
    runs-on: ubuntu-latest
    needs: setup
    environment: test
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: budapp_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Restore pnpm dependencies
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build API
        run: pnpm api:build

      - name: Setup test environment
        working-directory: apps/api
        run: |
          cp .env.example .env.test || echo "No .env.example found, creating .env.test"
          echo "DATABASE_URL=${{ secrets.DATABASE_URL }}" >> .env.test
          echo "JWT_SECRET=test-jwt-supersecret-key-for-github-ci" >> .env.test
          echo "NODE_ENV=test" >> .env.test

      - name: Run database migrations
        run: pnpm db:migrate
        env:
          NODE_ENV: test
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Run unit tests
        run: pnpm api:test:unit
        env:
          NODE_ENV: test

      - name: Run integration tests
        run: pnpm api:test:integration
        env:
          NODE_ENV: test
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          JWT_SECRET: test-jwt-secret-for-testing-purposes-only
          JWT_EXPIRES_IN: 1h
          AUTH_COOKIE_NAME: budapp_auth_test
          AUTH_COOKIE_SECURE: false
          AUTH_COOKIE_HTTP_ONLY: true
          AUTH_COOKIE_DOMAIN: localhost
          AUTH_COOKIE_MAX_AGE: 86400
          AUTH_REDIRECT_URL: http://localhost:3001/auth/callback
          AUTH_MOBILE_REDIRECT_URL: budapp://auth/callback
          GOOGLE_CLIENT_ID: test-google-client-id
          GOOGLE_CLIENT_SECRET: test-google-client-secret
          APPLE_CLIENT_ID: test-apple-client-id
          APPLE_CLIENT_SECRET: test-apple-client-secret
          OTEL_SDK_DISABLED: true

      - name: Run E2E tests
        run: pnpm api:test:e2e
        env:
          NODE_ENV: test
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          JWT_SECRET: test-jwt-secret-for-testing-purposes-only
          JWT_EXPIRES_IN: 1h
          AUTH_COOKIE_NAME: budapp_auth_test
          AUTH_COOKIE_SECURE: false
          AUTH_COOKIE_HTTP_ONLY: true
          AUTH_COOKIE_DOMAIN: localhost
          AUTH_COOKIE_MAX_AGE: 86400
          AUTH_REDIRECT_URL: http://localhost:3001/auth/callback
          AUTH_MOBILE_REDIRECT_URL: budapp://auth/callback
          GOOGLE_CLIENT_ID: test-google-client-id
          GOOGLE_CLIENT_SECRET: test-google-client-secret
          APPLE_CLIENT_ID: test-apple-client-id
          APPLE_CLIENT_SECRET: test-apple-client-secret
          OTEL_SDK_DISABLED: true

      - name: Generate test coverage
        run: pnpm api:test:coverage
        env:
          NODE_ENV: test
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          JWT_SECRET: test-jwt-secret-for-testing-purposes-only
          JWT_EXPIRES_IN: 1h
          AUTH_COOKIE_NAME: budapp_auth_test
          AUTH_COOKIE_SECURE: false
          AUTH_COOKIE_HTTP_ONLY: true
          AUTH_COOKIE_DOMAIN: localhost
          AUTH_COOKIE_MAX_AGE: 86400
          AUTH_REDIRECT_URL: http://localhost:3001/auth/callback
          AUTH_MOBILE_REDIRECT_URL: budapp://auth/callback
          GOOGLE_CLIENT_ID: test-google-client-id
          GOOGLE_CLIENT_SECRET: test-google-client-secret
          APPLE_CLIENT_ID: test-apple-client-id
          APPLE_CLIENT_SECRET: test-apple-client-secret
          OTEL_SDK_DISABLED: true

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          directory: apps/api/coverage
          flags: api
          name: api-coverage
          fail_ci_if_error: false

  # Job 4: Mobile Testing
  mobile-test:
    name: Mobile Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Restore Flutter dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.pub-cache
            apps/mobile/.dart_tool
          key: ${{ runner.os }}-flutter-${{ hashFiles('apps/mobile/pubspec.lock') }}

      - name: Install Flutter dependencies
        working-directory: apps/mobile
        run: flutter pub get

      - name: Generate code
        working-directory: apps/mobile
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Analyze Flutter code
        working-directory: apps/mobile
        run: flutter analyze

      - name: Run Flutter tests
        continue-on-error: true
        working-directory: apps/mobile
        run: flutter test --coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          directory: apps/mobile/coverage
          flags: mobile
          name: mobile-coverage
          fail_ci_if_error: false

  # Job 5: Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: setup
    permissions:
      contents: read
      security-events: write
      actions: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Restore pnpm dependencies
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        continue-on-error: true
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:javascript"
          upload-database: true

      - name: Run npm audit
        run: pnpm audit --audit-level moderate
