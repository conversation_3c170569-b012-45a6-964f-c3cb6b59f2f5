name: Deploy Mobile

on:
  push:
    branches: [main]
    paths:
      - 'apps/mobile/**'
      - '.github/workflows/deploy-mobile.yml'
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build'
        required: true
        default: 'both'
        type: choice
        options:
          - android
          - ios
          - both
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

concurrency:
  group: deploy-mobile-${{ github.ref }}-${{ inputs.platform || 'both' }}-${{ inputs.environment || 'staging' }}
  cancel-in-progress: false

env:
  FLUTTER_VERSION: '3.29.3'
  JAVA_VERSION: '17'

jobs:
  # Job 1: Build and Test
  build-and-test:
    name: Build and Test Mobile
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Install dependencies
        working-directory: apps/mobile
        run: flutter pub get

      - name: Analyze code
        working-directory: apps/mobile
        run: flutter analyze

      - name: Run tests
        working-directory: apps/mobile
        run: flutter test --coverage

      - name: Upload test results
        uses: actions/upload-artifact@v4
        with:
          name: mobile-test-results
          path: apps/mobile/coverage/
          retention-days: 1

  # Job 2: Build Android
  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: build-and-test
    if: inputs.platform == 'android' || inputs.platform == 'both' || github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Install dependencies
        working-directory: apps/mobile
        run: flutter pub get

      - name: Setup Android signing
        working-directory: apps/mobile/android
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE }}" | base64 -d > app/keystore.jks
          echo "storePassword=${{ secrets.ANDROID_KEYSTORE_PASSWORD }}" >> key.properties
          echo "keyPassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> key.properties
          echo "keyAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> key.properties
          echo "storeFile=keystore.jks" >> key.properties

      - name: Build Android APK (Debug)
        if: inputs.environment == 'staging' || github.ref != 'refs/heads/main'
        working-directory: apps/mobile
        run: flutter build apk --debug --flavor staging

      - name: Build Android APK (Release)
        if: inputs.environment == 'production' && github.ref == 'refs/heads/main'
        working-directory: apps/mobile
        run: flutter build apk --release --flavor production

      - name: Build Android App Bundle (Release)
        if: inputs.environment == 'production' && github.ref == 'refs/heads/main'
        working-directory: apps/mobile
        run: flutter build appbundle --release --flavor production

      - name: Upload Android artifacts
        uses: actions/upload-artifact@v4
        with:
          name: android-build
          path: |
            apps/mobile/build/app/outputs/flutter-apk/*.apk
            apps/mobile/build/app/outputs/bundle/productionRelease/*.aab
          retention-days: 30

  # Job 3: Build iOS
  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: build-and-test
    if: inputs.platform == 'ios' || inputs.platform == 'both' || github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Install dependencies
        working-directory: apps/mobile
        run: flutter pub get

      - name: Setup iOS signing
        working-directory: apps/mobile/ios
        run: |
          echo "${{ secrets.IOS_CERTIFICATE }}" | base64 -d > ios_certificate.p12
          echo "${{ secrets.IOS_PROVISIONING_PROFILE }}" | base64 -d > ios_profile.mobileprovision
          
          # Import certificate
          security create-keychain -p "" build.keychain
          security import ios_certificate.p12 -t agg -k build.keychain -P "${{ secrets.IOS_CERTIFICATE_PASSWORD }}" -A
          security list-keychains -s build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "" build.keychain
          security set-key-partition-list -S apple-tool:,apple: -s -k "" build.keychain
          
          # Install provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp ios_profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles/

      - name: Build iOS (Debug)
        if: inputs.environment == 'staging' || github.ref != 'refs/heads/main'
        working-directory: apps/mobile
        run: |
          flutter build ios --debug --no-codesign --flavor staging
          cd ios
          xcodebuild -workspace Runner.xcworkspace -scheme staging -configuration Debug-staging -archivePath build/Runner.xcarchive archive
          xcodebuild -exportArchive -archivePath build/Runner.xcarchive -exportPath build/Runner.ipa -exportOptionsPlist ExportOptions-staging.plist

      - name: Build iOS (Release)
        if: inputs.environment == 'production' && github.ref == 'refs/heads/main'
        working-directory: apps/mobile
        run: |
          flutter build ios --release --flavor production
          cd ios
          xcodebuild -workspace Runner.xcworkspace -scheme production -configuration Release-production -archivePath build/Runner.xcarchive archive
          xcodebuild -exportArchive -archivePath build/Runner.xcarchive -exportPath build/Runner.ipa -exportOptionsPlist ExportOptions-production.plist

      - name: Upload iOS artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ios-build
          path: |
            apps/mobile/ios/build/Runner.ipa/*.ipa
          retention-days: 30

  # Job 4: Deploy to App Stores
  deploy-stores:
    name: Deploy to App Stores
    runs-on: ubuntu-latest
    needs: [build-android, build-ios]
    if: github.ref == 'refs/heads/main' && (inputs.environment == 'production' || github.event_name == 'push')
    environment:
      name: production
    steps:
      - name: Download Android artifacts
        uses: actions/download-artifact@v4
        with:
          name: android-build
          path: android-build/

      - name: Download iOS artifacts
        uses: actions/download-artifact@v4
        with:
          name: ios-build
          path: ios-build/

      - name: Deploy to Google Play Store
        if: inputs.platform == 'android' || inputs.platform == 'both' || github.event_name == 'push'
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.budapp.app
          releaseFiles: android-build/build/app/outputs/bundle/productionRelease/*.aab
          track: internal
          status: completed

      - name: Deploy to Apple App Store
        if: inputs.platform == 'ios' || inputs.platform == 'both' || github.event_name == 'push'
        run: |
          # Install App Store Connect API tools
          npm install -g @apple/app-store-connect-cli
          
          # Upload to App Store Connect
          xcrun altool --upload-app \
            --type ios \
            --file ios-build/build/Runner.ipa/*.ipa \
            --username "${{ secrets.APPLE_ID }}" \
            --password "${{ secrets.APPLE_APP_PASSWORD }}"

  # Job 5: Deploy to Firebase App Distribution (Staging)
  deploy-firebase:
    name: Deploy to Firebase App Distribution
    runs-on: ubuntu-latest
    needs: [build-android, build-ios]
    if: inputs.environment == 'staging' || github.ref != 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Android artifacts
        uses: actions/download-artifact@v4
        with:
          name: android-build
          path: android-build/

      - name: Deploy Android to Firebase
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_ANDROID_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          groups: testers
          file: android-build/build/app/outputs/flutter-apk/app-staging-debug.apk
          releaseNotes: |
            Build from commit: ${{ github.sha }}
            Branch: ${{ github.ref_name }}

      - name: Download iOS artifacts
        uses: actions/download-artifact@v4
        with:
          name: ios-build
          path: ios-build/

      - name: Deploy iOS to Firebase
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_IOS_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          groups: testers
          file: ios-build/build/Runner.ipa/*.ipa
          releaseNotes: |
            Build from commit: ${{ github.sha }}
            Branch: ${{ github.ref_name }}

  # Job 6: Notify
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-stores, deploy-firebase]
    if: always()
    steps:
      - name: Notify success
        if: needs.deploy-stores.result == 'success' || needs.deploy-firebase.result == 'success'
        run: |
          echo "✅ Mobile app deployed successfully"
          # Add Slack/Discord notification here if needed

      - name: Notify failure
        if: needs.deploy-stores.result == 'failure' || needs.deploy-firebase.result == 'failure'
        run: |
          echo "❌ Mobile app deployment failed"
          # Add Slack/Discord notification here if needed
