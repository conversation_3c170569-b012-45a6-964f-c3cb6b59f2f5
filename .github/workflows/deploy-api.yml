name: Deploy API

on:
  push:
    branches: [main]
    paths:
      - 'apps/api/**'
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'turbo.json'
      - '.github/workflows/deploy-api.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

concurrency:
  group: deploy-api-${{ github.ref }}-${{ inputs.environment || 'production' }}
  cancel-in-progress: false

env:
  NODE_VERSION: '22'
  PNPM_VERSION: '10.11.0'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/api

jobs:
  # Job 1: Build and Test
  build-and-test:
    name: Build and Test API
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: budapp_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm lint

      - name: Build API
        run: pnpm api:build

      - name: Setup test environment
        working-directory: apps/api
        run: |
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/budapp_test" > .env.test
          echo "JWT_SECRET=test-jwt-secret-key-for-ci" >> .env.test
          echo "NODE_ENV=test" >> .env.test

      - name: Run database migrations
        run: pnpm db:migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/budapp_test

      - name: Run tests
        run: pnpm api:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/budapp_test

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: api-build
          path: apps/api/dist/
          retention-days: 1

  # Job 2: Build Docker Image
  build-image:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: build-and-test
    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/api/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

      - name: Output image
        id: image
        run: |
          echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}" >> $GITHUB_OUTPUT

  # Job 3: Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-image
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && inputs.environment == 'staging')
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.url }}
    steps:
      - name: Deploy to Render (Staging)
        id: deploy
        run: |
          curl -X POST "${{ secrets.RENDER_STAGING_DEPLOY_HOOK }}" \
            -H "Content-Type: application/json" \
            -d '{"imageUrl": "${{ needs.build-image.outputs.image }}"}'
          echo "url=${{ secrets.RENDER_STAGING_URL }}" >> $GITHUB_OUTPUT

      - name: Wait for deployment
        run: |
          echo "Waiting for deployment to complete..."
          sleep 60

      - name: Health check
        run: |
          curl -f "${{ secrets.RENDER_STAGING_URL }}/health" || exit 1

  # Job 4: Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-image, deploy-staging]
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && inputs.environment == 'production')
    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}
    steps:
      - name: Deploy to Render (Production)
        id: deploy
        run: |
          curl -X POST "${{ secrets.RENDER_PRODUCTION_DEPLOY_HOOK }}" \
            -H "Content-Type: application/json" \
            -d '{"imageUrl": "${{ needs.build-image.outputs.image }}"}'
          echo "url=${{ secrets.RENDER_PRODUCTION_URL }}" >> $GITHUB_OUTPUT

      - name: Wait for deployment
        run: |
          echo "Waiting for deployment to complete..."
          sleep 90

      - name: Health check
        run: |
          curl -f "${{ secrets.RENDER_PRODUCTION_URL }}/health" || exit 1

      - name: Run database migrations (Production)
        run: |
          curl -X POST "${{ secrets.RENDER_PRODUCTION_URL }}/admin/migrate" \
            -H "Authorization: Bearer ${{ secrets.ADMIN_API_KEY }}" \
            -H "Content-Type: application/json"

  # Job 5: Notify
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Notify success
        if: needs.deploy-production.result == 'success'
        run: |
          echo "✅ API deployed successfully to production"
          # Add Slack/Discord notification here if needed

      - name: Notify failure
        if: needs.deploy-production.result == 'failure' || needs.deploy-staging.result == 'failure'
        run: |
          echo "❌ API deployment failed"
          # Add Slack/Discord notification here if needed
