# Pull Request

## Description
<!-- Provide a brief description of the changes in this PR -->

## Type of Change
<!-- Mark the relevant option with an "x" -->
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes, no api changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test update
- [ ] 🔒 Security update
- [ ] 🏗️ Build/CI update

## Related Issues
<!-- Link to related issues using "Fixes #123" or "Closes #123" -->
- Fixes #
- Related to #

## Changes Made
<!-- List the main changes made in this PR -->
- 
- 
- 

## Testing
<!-- Describe the tests you ran to verify your changes -->
- [ ] Unit tests pass (`pnpm test:unit`)
- [ ] Integration tests pass (`pnpm test:integration`)
- [ ] E2E tests pass (`pnpm test:e2e`)
- [ ] Manual testing completed
- [ ] New tests added for new functionality

### Test Coverage
<!-- If applicable, mention test coverage changes -->
- Current coverage: %
- Coverage change: +/- %

## API Changes
<!-- If this PR includes API changes, describe them -->
- [ ] No API changes
- [ ] GraphQL schema changes (breaking/non-breaking)
- [ ] REST endpoint changes
- [ ] Database schema changes

### Migration Required
- [ ] No migration required
- [ ] Database migration included
- [ ] Manual migration steps required (documented below)

## Mobile App Changes
<!-- If this PR affects the mobile app -->
- [ ] No mobile changes
- [ ] UI/UX changes
- [ ] New screens/components
- [ ] Navigation changes
- [ ] Platform-specific changes (iOS/Android)

## Security Considerations
<!-- Address any security implications -->
- [ ] No security implications
- [ ] Security review completed
- [ ] Sensitive data handling reviewed
- [ ] Authentication/authorization changes reviewed

## Performance Impact
<!-- Describe any performance implications -->
- [ ] No performance impact
- [ ] Performance improvement
- [ ] Potential performance regression (justified below)
- [ ] Performance testing completed

## Deployment Notes
<!-- Any special deployment considerations -->
- [ ] No special deployment requirements
- [ ] Environment variables added/changed
- [ ] Infrastructure changes required
- [ ] Feature flags required
- [ ] Rollback plan documented

## Screenshots/Videos
<!-- If applicable, add screenshots or videos to help explain your changes -->

## Checklist
<!-- Mark completed items with an "x" -->
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Additional Notes
<!-- Any additional information that reviewers should know -->

---

### For Reviewers
<!-- Guidelines for reviewers -->
**Focus Areas:**
- [ ] Code quality and maintainability
- [ ] Test coverage and quality
- [ ] Security implications
- [ ] Performance impact
- [ ] Documentation completeness
- [ ] API design consistency
