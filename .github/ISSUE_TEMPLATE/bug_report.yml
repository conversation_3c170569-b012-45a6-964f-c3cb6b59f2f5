name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please provide as much detail as possible.

  - type: checkboxes
    id: terms
    attributes:
      label: Prerequisites
      description: Please confirm the following before submitting
      options:
        - label: I have searched existing issues to ensure this bug hasn't been reported
          required: true
        - label: I have read the documentation
          required: true
        - label: I am using the latest version
          required: false

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which component is affected?
      options:
        - API (Backend)
        - Mobile App (Flutter)
        - Database
        - CI/CD
        - Documentation
        - Other
    validations:
      required: true

  - type: dropdown
    id: platform
    attributes:
      label: Platform (if mobile)
      description: Which platform is affected?
      options:
        - Not applicable
        - iOS
        - Android
        - Both iOS and Android
    validations:
      required: false

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen
      placeholder: What should have happened?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment
      description: Please provide environment details
      placeholder: |
        - OS: [e.g. iOS 17, Android 14, macOS 14]
        - App Version: [e.g. 1.0.0]
        - Device: [e.g. iPhone 15, Pixel 8]
        - Node.js Version: [e.g. 22.0.0]
        - Flutter Version: [e.g. 3.29.3]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Logs/Error Messages
      description: Please copy and paste any relevant log output or error messages
      render: shell
    validations:
      required: false

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem
    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here
    validations:
      required: false
