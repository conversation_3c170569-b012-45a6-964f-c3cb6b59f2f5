name: ✨ Feature Request
description: Suggest a new feature or enhancement
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a new feature! Please provide as much detail as possible.

  - type: checkboxes
    id: terms
    attributes:
      label: Prerequisites
      description: Please confirm the following before submitting
      options:
        - label: I have searched existing issues to ensure this feature hasn't been requested
          required: true
        - label: I have read the documentation and project roadmap
          required: true
        - label: This feature aligns with the project's goals
          required: true

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which component would this feature affect?
      options:
        - API (Backend)
        - Mobile App (Flutter)
        - Database
        - CI/CD
        - Documentation
        - Multiple Components
        - Other
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would improve user experience
        - High - Essential for my use case
        - Critical - Blocking my usage
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: What problem does this feature solve? What is the current limitation?
      placeholder: Describe the problem or limitation...
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented
      placeholder: Describe your proposed solution...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Describe any alternative solutions or features you've considered
      placeholder: What alternatives have you considered?
    validations:
      required: false

  - type: textarea
    id: user-story
    attributes:
      label: User Story
      description: Describe this feature from a user's perspective
      placeholder: |
        As a [type of user],
        I want [some goal]
        So that [some reason/benefit]
    validations:
      required: true

  - type: textarea
    id: acceptance-criteria
    attributes:
      label: Acceptance Criteria
      description: What criteria must be met for this feature to be considered complete?
      placeholder: |
        - [ ] Criterion 1
        - [ ] Criterion 2
        - [ ] Criterion 3
    validations:
      required: false

  - type: textarea
    id: mockups
    attributes:
      label: Mockups/Wireframes
      description: If applicable, add mockups, wireframes, or screenshots to help explain your feature
    validations:
      required: false

  - type: dropdown
    id: complexity
    attributes:
      label: Estimated Complexity
      description: How complex do you think this feature would be to implement?
      options:
        - Low - Simple change or addition
        - Medium - Moderate development effort
        - High - Significant development effort
        - Very High - Major feature requiring extensive work
        - Unknown - Not sure about complexity
    validations:
      required: false

  - type: checkboxes
    id: impact
    attributes:
      label: Impact Areas
      description: Which areas might be impacted by this feature?
      options:
        - label: User Interface/Experience
        - label: API/Backend Logic
        - label: Database Schema
        - label: Authentication/Security
        - label: Performance
        - label: Testing
        - label: Documentation
        - label: Mobile App Functionality
        - label: Third-party Integrations

  - type: textarea
    id: technical-considerations
    attributes:
      label: Technical Considerations
      description: Any technical considerations, constraints, or implementation details
      placeholder: Technical details, constraints, or implementation notes...
    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, links, or information about the feature request
    validations:
      required: false
