name: "BudApp CodeQL Configuration"

# Disable default queries and use only security-focused ones
disable-default-queries: false

# Additional query suites to run
queries:
  - uses: security-extended
  - uses: security-and-quality

# Paths to include in analysis
paths:
  - apps/api/src
  - apps/mobile/lib

# Paths to exclude from analysis
paths-ignore:
  - apps/api/node_modules
  - apps/api/dist
  - apps/api/__tests__
  - apps/api/coverage
  - apps/mobile/build
  - apps/mobile/.dart_tool
  - apps/mobile/test
  - "**/*.test.ts"
  - "**/*.test.js"
  - "**/*.spec.ts"
  - "**/*.spec.js"
  - "**/*.d.ts"

# Additional packs to include
packs:
  - codeql/javascript-queries
  - codeql/typescript-queries
