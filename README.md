# BudApp

BudApp is a personal finance management application that helps users track income and expenses, create and monitor budgets, track savings goals, and gain insights into their spending habits.

## Project Structure

This is a monorepo managed with Turborepo and pnpm workspaces. It contains the following packages:

### Apps

- `apps/api` - Backend API built with Node.js, Fastify, and GraphQL
- `apps/mobile` - Mobile application built with Flutter

### Packages

- `packages/graphql-schema` - Shared GraphQL schema and type definitions
- `packages/db` - Database schema and migrations using Drizzle ORM
- `packages/config` - Shared configuration for TypeScript, ESLint, etc.
- `packages/design` - Design tokens and UI components

## Getting Started

### Prerequisites

- Node.js 22 LTS
- pnpm 10+
- Flutter SDK (latest stable)
- Docker (optional, for local development)

### Setup

1. Install dependencies:

```bash
pnpm install
```

2. Start the development server:

```bash
# Start the API
pnpm api:dev

# In another terminal, start the mobile app (from the mobile directory)
cd apps/mobile
flutter run
```

## Available Scripts

- `pnpm build` - Build all packages
- `pnpm dev` - Start all development servers
- `pnpm lint` - Run ESLint on all packages
- `pnpm format` - Run Prettier on all packages
- `pnpm test` - Run tests for all packages
- `pnpm clean` - Clean build artifacts and node_modules

## Development Workflow

1. Make changes to the code
2. Run tests to ensure everything works
3. Commit your changes using conventional commit messages
4. Push your changes to the repository

## Documentation

- [API Documentation](apps/api/README.md) - Documentation for the backend API
- [Mobile Documentation](apps/mobile/README.md) - Documentation for the mobile app
- [Authentication System](docs/authentication.md) - Comprehensive documentation of the authentication system

## Tech Stack

### Backend

- Node.js 22 LTS with TypeScript (ESM)
- Fastify 5 + GraphQL (Apollo Server)
- Drizzle ORM with PostgreSQL
- JWT and OAuth 2.0 for authentication

### Mobile

- Flutter SDK with Dart
- GraphQL for data fetching
- Riverpod for state management
- Material 3 + Cupertino components

## License

[MIT](LICENSE)
