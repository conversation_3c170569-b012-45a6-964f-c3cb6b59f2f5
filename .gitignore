# Dependencies
node_modules
.pnpm-store
.pnp
.pnp.js

# Build outputs
dist
build
out
.next
.nuxt
.turbo
.vercel
.output

# Testing
coverage
.nyc_output

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
*.pem

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Flutter/Dart specific
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.*
**/ios/Flutter/ephemeral
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*
**/ios/Pods/
**/ios/.symlinks/
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/flutter_export_environment.sh
**/ios/Flutter/Flutter.xcframework
**/ios/Flutter/App.xcframework
**/ios/Flutter/ephemeral/
**/ios/.symlinks/
**/ios/Pods/
**/ios/Runner.xcworkspace/
**/ios/Runner.xcodeproj/
**/android/.gradle
**/android/captures/
**/android/app/debug
**/android/app/profile
**/android/app/release
**/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
**/android/local.properties
**/android/app/debug.keystore
**/android/key.properties
**/android/app/upload-keystore.jks
**/android/app/google-services.json
**/ios/GoogleService-Info.plist
**/ios/Runner/GoogleService-Info.plist
**/ios/firebase_app_id_file.json
**/android/app/src/main/kotlin/io/flutter/plugins/GeneratedPluginRegistrant.kt
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h
**/web/flutter_service_worker.js
**/web/flutter.js
**/web/main.dart.js
**/web/main.dart.js.map
**/web/assets/FontManifest.json
**/web/assets/AssetManifest.json
**/web/assets/AssetManifest.bin
**/web/assets/FontManifest.json
**/web/assets/NOTICES
**/web/assets/packages/
**/web/assets/shaders/
**/web/assets/fonts/
**/web/canvaskit/
**/web/canvaskit/profiling/

# Generated files
*.g.dart
*.freezed.dart
*.gen.dart
*.mocks.dart
*.config.dart
*.gr.dart
*.chopper.dart
*.swagger.dart
*.graphql.dart
*.graphql.g.dart
*.gql.dart
*.gql.g.dart
*.gql.freezed.dart
*.graphql.freezed.dart
app_router.g.dart

# Generated GraphQL schema
# Uncomment if you don't want to commit the generated schema
# apps/api/generated/

# Miscellaneous
*.class
*.log
*.pyc
*.swp
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Database
*.sqlite
*.sqlite3
*.db

# Logs
logs
*.log

# Temporary files
tmp
temp

# Added by Claude Task Master
dev-debug.log
# Dependency directories
node_modules/
.vscode
# OS specific
# Task files
#tasks.json
#tasks/ 