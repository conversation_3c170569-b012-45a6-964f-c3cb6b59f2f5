{"name": "buda<PERSON>", "version": "0.1.0", "private": true, "description": "Personal finance management application", "type": "module", "engines": {"node": ">=22.0.0", "pnpm": ">=10.0.0"}, "packageManager": "pnpm@10.11.0", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "turbo run format", "check": "biome check .", "check:fix": "biome check --write .", "test": "turbo run test", "test:coverage": "turbo run test:coverage", "test:coverage:summary": "turbo run test:coverage:summary", "clean": "turbo run clean && rm -rf node_modules", "api:dev": "turbo run dev --filter=api", "api:build": "turbo run build --filter=api", "api:test": "turbo run test --filter=api", "api:test:unit": "turbo run test:unit --filter=api", "api:test:integration": "turbo run test:integration --filter=api", "api:test:e2e": "turbo run test:e2e --filter=api", "api:test:coverage": "turbo run test:coverage --filter=api", "db:generate": "turbo run db:generate --filter=api", "db:migrate": "turbo run db:migrate --filter=api", "db:seed": "turbo run db:seed --filter=api", "db:studio": "turbo run db:studio --filter=api", "prepare": "husky", "generate:schema": "turbo run generate:schema --filter=api", "mobile:copy-schema": "cp apps/api/generated/schema.graphql apps/mobile/graphql/schema.graphql", "mobile:codegen": "cd apps/mobile && dart run build_runner build --delete-conflicting-outputs", "mobile:codegen:watch": "cd apps/mobile && dart run build_runner watch --delete-conflicting-outputs", "mobile:analyze": "cd apps/mobile && flutter analyze", "mobile:test": "cd apps/mobile && flutter test", "schema:update": "pnpm generate:schema"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "husky": "^9.0.11", "lint-staged": "^15.2.0", "turbo": "^2.0.0", "typescript": "^5.8.3"}, "workspaces": ["apps/*"], "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["biome check --write --no-errors-on-unmatched"]}, "pnpm": {"overrides": {"esbuild": "^0.25.0"}}}